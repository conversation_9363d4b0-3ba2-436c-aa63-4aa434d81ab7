package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 预测电量
 **/
@Data
@ApiModel("预测电量")
public class QueryUserTreeListDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("1.获取绑定周期用户 2.获取全部用户")
    private Integer type;

    @ApiModelProperty("查询日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始日期不可为空")
    private Date startDate;

    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束日期不可为空")
    private Date endDate;

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    @NotNull(message = "预测维度不可为空")
    private Integer dimension;


}