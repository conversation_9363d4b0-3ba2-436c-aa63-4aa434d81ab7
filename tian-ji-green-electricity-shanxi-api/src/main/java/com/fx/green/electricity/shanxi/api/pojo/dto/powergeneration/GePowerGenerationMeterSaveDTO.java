package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 发电电表保存DTO
 */
@Data
@ApiModel("发电电表保存DTO")
public class GePowerGenerationMeterSaveDTO implements Serializable {

    private static final long serialVersionUID = -525886433686176838L;

    @ApiModelProperty("发电设备id")
    @NotNull(message = "发电设备id不能为空")
    private Long deviceId;

    @ApiModelProperty("发电电表保存DTO内部类")
    @NotNull(message = "发电电表保存DTO内部类不能为空")
    private List<GePowerGenerationMeterSaveDTOInner> meterList;

    @Data
    @ApiModel("发电电表保存DTO内部类内部类")
    public static class GePowerGenerationMeterSaveDTOInner {

        @ApiModelProperty("电表名称")
        @NotBlank(message = "电表名称不能为空")
        private String meterName;

        @ApiModelProperty("电表表号")
        @NotBlank(message = "电表表号不能为空")
        private String meterNo;

        @ApiModelProperty("方向类型(0 无方向、1上网方向、2下网方向、3汇流站方向、4逆变站方向、5电网电表、6直流电表)")
        @NotNull(message = "方向类型不能为空")
        private Integer directionType;

    }

}
