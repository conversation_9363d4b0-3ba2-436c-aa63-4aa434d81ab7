package com.fx.green.electricity.shanxi.api.converter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.VppRetailContractsImportExcel;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;

import java.util.List;
import java.util.Map;

/**
 * 零售合同导入转换器
 *
 * <AUTHOR>
 */
public class VppRetailContractsImportConverter {

    /**
     * 将导入Excel实体转换为业务实体
     *
     * @param importExcel       导入Excel实体
     * @param runMonth          运行月份
     * @param retailContractsId 零售合同ID
     * @param userNameMap       用户名称映射
     * @param tenantId          租户ID
     * @return 业务实体
     */
    public static VppRetailContractsManageVO convertToManageVO(
            VppRetailContractsImportExcel importExcel,
            String runMonth,
            Long retailContractsId,
            Map<String, List<VppLoadUserVO.TreeUserVO>> userNameMap,
            Long tenantId) {

        if (!importExcel.isValidContract()) {
            return null; // 跳过无效合同
        }

        String userName = importExcel.getUserName();
        if (ObjectUtil.isEmpty(userName) || !userNameMap.containsKey(userName)) {
            throw new IllegalArgumentException("用户名称不存在或未绑定: " + userName);
        }

        VppRetailContractsManageVO manageVO = new VppRetailContractsManageVO();

        // 设置基础信息
        manageVO.setCycle(1);
        manageVO.setRetailContractsId(retailContractsId);
        manageVO.setSigningMonth(DateUtil.parseDate(runMonth + "-01"));
        manageVO.setType(2); // 电价类型
        manageVO.setTenantId(tenantId);

        // 设置用户信息
        manageVO.setName(userName);
        manageVO.setUserId(userNameMap.get(userName).get(0).getId());

        // 设置商品信息
        manageVO.setTradeName(importExcel.getTradeName());
        manageVO.setPriceName(importExcel.getPriceValueName());

        // 设置价格信息
        manageVO.setVal(importExcel.getPriceValueDecimal());
        manageVO.setPriceDifference(importExcel.getPriceDifferenceDecimal());

        // 设置时段信息
        manageVO.setTimeFrame(importExcel.getTimeFrame());

        return manageVO;
    }

    /**
     * 验证导入数据的完整性
     *
     * @param importExcel 导入Excel实体
     * @throws IllegalArgumentException 验证失败时抛出异常
     */
    public static void validateImportData(VppRetailContractsImportExcel importExcel) {
        if (importExcel == null) {
            throw new IllegalArgumentException("导入数据不能为空");
        }

        // 验证必填字段
        if (ObjectUtil.isEmpty(importExcel.getUserName())) {
            throw new IllegalArgumentException("电力用户名称不能为空");
        }

        if (ObjectUtil.isEmpty(importExcel.getStatus())) {
            throw new IllegalArgumentException("状态不能为空");
        }

        if (ObjectUtil.isEmpty(importExcel.getTradeName())) {
            throw new IllegalArgumentException("商品名称不能为空");
        }

        if (ObjectUtil.isEmpty(importExcel.getIsTimeSharing())) {
            throw new IllegalArgumentException("是否分时不能为空");
        }

        if (ObjectUtil.isEmpty(importExcel.getTimeSlot())) {
            throw new IllegalArgumentException("时段不能为空");
        }

        // 验证时段范围
        try {
            Integer timeSlot = importExcel.getTimeSlotInteger();
            if (timeSlot == null || timeSlot < 1 || timeSlot > 24) {
                throw new IllegalArgumentException("时段必须是1-24之间的数字");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("时段格式不正确: " + importExcel.getTimeSlot());
        }

    }
}
