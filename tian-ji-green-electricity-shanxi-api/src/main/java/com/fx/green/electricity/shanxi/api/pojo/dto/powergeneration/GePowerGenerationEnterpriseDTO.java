package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发电企业信息DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("发电企业信息DTO")
public class GePowerGenerationEnterpriseDTO extends BaseDTO {
    private static final long serialVersionUID = -4047719807394980773L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("联系人")
    private String leader;

    @ApiModelProperty("联系方式")
    private String phone;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("地区名称")
    private String areaName;

}


