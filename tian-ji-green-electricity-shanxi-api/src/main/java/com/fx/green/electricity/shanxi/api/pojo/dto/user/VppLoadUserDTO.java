package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("负荷用户DTO")
public class VppLoadUserDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @NotBlank(message = "负荷用户名称不能为空")
    @ApiModelProperty(value = "负荷用户名称", example = "张三", required = true)
    private String name;

    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", example = "1234567890", required = true)
    private String userCode;

    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty(value = "联系人姓名", example = "张三", required = true)
    private String contactName;

    @NotBlank(message = "联系人电话不能为空")
    @ApiModelProperty(value = "联系人电话", example = "1234567890", required = true)
    private String contactPhone;

    @ApiModelProperty(value = "微信绑定ID")
    private String openId;

    @ApiModelProperty(value = "红利系数")
    private BigDecimal dividendRatio;

    @NotBlank(message = "位置code(到区)不能为空")
    @ApiModelProperty(value = "位置code(到区)", example = "140105", required = true)
    private String areaCode;

    @NotBlank(message = "所在区县不能为空")
    @ApiModelProperty(value = "所在区县", example = "小店区", required = true)
    private String districtAddr;

    @NotBlank(message = "位置code(详细信息)不能为空")
    @ApiModelProperty(value = "位置code(详细信息)", example = "山西省太原市小店区", required = true)
    private String areaCodeDetail;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址", example = "平阳路大马社区小学北侧", required = true)
    private String deptAddress;

    @NotBlank(message = "经度不能为空")
    @ApiModelProperty(value = "经度", example = "37.80", required = true)
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty(value = "纬度", example = "112.56", required = true)
    private String lat;

    @NotNull(message = "用电类型不能为空")
    @ApiModelProperty(value = "用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)", example = "1", required = true)
    private Integer electricityType;

    @ApiModelProperty(value = "企业简介", example = "xxxx")
    private String enterpriseIntroduction;

    @ApiModelProperty(value = "企业照片", example = "xxxx.png,xxx.png")
    private String enterprisePhotos;

    @ApiModelProperty("户号信息")
    private List<VppUserAccountDTO> accountList;

}
