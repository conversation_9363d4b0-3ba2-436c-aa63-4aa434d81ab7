package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("设备信息VO")
public class GeUnitDeviceVO extends BaseVO {
    private static final long serialVersionUID = 2503675752798088126L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备编号")
    private String deviceCode;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经纬度")
    private String coordinate;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;
}
