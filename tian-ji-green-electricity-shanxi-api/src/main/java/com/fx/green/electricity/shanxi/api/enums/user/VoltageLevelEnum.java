package com.fx.green.electricity.shanxi.api.enums.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电压等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VoltageLevelEnum {
    /**
     * 电压等级定义
     */
    LEVEL_220KV(1, "220Kv", "220千伏"),
    LEVEL_110KV(2, "110Kv", "110千伏"),
    LEVEL_35KV(3, "35Kv", "35千伏"),
    LEVEL_10KV(4, "10Kv", "10千伏"),
    LEVEL_6KV(5, "6Kv", "6千伏"),
    LEVEL_380V(6, "380V", "380伏"),
    LEVEL_220V(7, "220V", "220伏");

    /**
     * 等级代码
     */
    private final Integer code;

    /**
     * 等级值（显示用）
     */
    private final String value;

    /**
     * 等级描述
     */
    private final String description;

    /**
     * 根据代码获取电压等级
     *
     * @param code 等级代码
     * @return 电压等级枚举，如果未找到返回null
     */
    public static VoltageLevelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VoltageLevelEnum level : values()) {
            if (level.code.equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 根据代码获取电压等级值
     *
     * @param code 等级代码
     * @return 电压等级值，如果未找到返回"未知"
     */
    public static String getValueByCode(Integer code) {
        VoltageLevelEnum level = getByCode(code);
        return level != null ? level.getValue() : "未知";
    }
}
