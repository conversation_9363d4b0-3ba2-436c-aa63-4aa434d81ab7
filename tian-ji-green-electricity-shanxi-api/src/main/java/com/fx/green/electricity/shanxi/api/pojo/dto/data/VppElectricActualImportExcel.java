package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实际用电量导入Excel实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel("实际用电量导入Excel")
public class VppElectricActualImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("市场成员名称")
    @ExcelProperty(value = "市场成员名称", index = 0)
    private String name;

    @ApiModelProperty("户号")
    @ExcelProperty(value = "户号", index = 1)
    private String registered;

    @ApiModelProperty("计量点")
    @ExcelProperty(value = "计量点", index = 2)
    private String meteringCode;

    @ApiModelProperty("日期")
    @ExcelProperty(value = "日期", index = 3)
    @DateTimeFormat("yyyy-MM-dd")
    private Date dateDay;

    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注", index = 4)
    private String remark;

    @ApiModelProperty("单位")
    @ExcelProperty(value = "单位", index = 5)
    private String unit;

    @ApiModelProperty("类型")
    @ExcelProperty(value = "类型", index = 6)
    private String type;

    // 96个时间点的电量数据（从索引7开始）
    @ApiModelProperty("00:15")
    @ExcelProperty(value = "00:15", index = 7)
    private BigDecimal electricity0015;

    @ApiModelProperty("00:30")
    @ExcelProperty(value = "00:30", index = 8)
    private BigDecimal electricity0030;

    @ApiModelProperty("00:45")
    @ExcelProperty(value = "00:45", index = 9)
    private BigDecimal electricity0045;

    @ApiModelProperty("01:00")
    @ExcelProperty(value = "01:00", index = 10)
    private BigDecimal electricity0100;

    @ApiModelProperty("01:15")
    @ExcelProperty(value = "01:15", index = 11)
    private BigDecimal electricity0115;

    @ApiModelProperty("01:30")
    @ExcelProperty(value = "01:30", index = 12)
    private BigDecimal electricity0130;

    @ApiModelProperty("01:45")
    @ExcelProperty(value = "01:45", index = 13)
    private BigDecimal electricity0145;

    @ApiModelProperty("02:00")
    @ExcelProperty(value = "02:00", index = 14)
    private BigDecimal electricity0200;

    @ApiModelProperty("02:15")
    @ExcelProperty(value = "02:15", index = 15)
    private BigDecimal electricity0215;

    @ApiModelProperty("02:30")
    @ExcelProperty(value = "02:30", index = 16)
    private BigDecimal electricity0230;

    @ApiModelProperty("02:45")
    @ExcelProperty(value = "02:45", index = 17)
    private BigDecimal electricity0245;

    @ApiModelProperty("03:00")
    @ExcelProperty(value = "03:00", index = 18)
    private BigDecimal electricity0300;

    @ApiModelProperty("03:15")
    @ExcelProperty(value = "03:15", index = 19)
    private BigDecimal electricity0315;

    @ApiModelProperty("03:30")
    @ExcelProperty(value = "03:30", index = 20)
    private BigDecimal electricity0330;

    @ApiModelProperty("03:45")
    @ExcelProperty(value = "03:45", index = 21)
    private BigDecimal electricity0345;

    @ApiModelProperty("04:00")
    @ExcelProperty(value = "04:00", index = 22)
    private BigDecimal electricity0400;

    @ApiModelProperty("04:15")
    @ExcelProperty(value = "04:15", index = 23)
    private BigDecimal electricity0415;

    @ApiModelProperty("04:30")
    @ExcelProperty(value = "04:30", index = 24)
    private BigDecimal electricity0430;

    @ApiModelProperty("04:45")
    @ExcelProperty(value = "04:45", index = 25)
    private BigDecimal electricity0445;

    @ApiModelProperty("05:00")
    @ExcelProperty(value = "05:00", index = 26)
    private BigDecimal electricity0500;

    @ApiModelProperty("05:15")
    @ExcelProperty(value = "05:15", index = 27)
    private BigDecimal electricity0515;

    @ApiModelProperty("05:30")
    @ExcelProperty(value = "05:30", index = 28)
    private BigDecimal electricity0530;

    @ApiModelProperty("05:45")
    @ExcelProperty(value = "05:45", index = 29)
    private BigDecimal electricity0545;

    @ApiModelProperty("06:00")
    @ExcelProperty(value = "06:00", index = 30)
    private BigDecimal electricity0600;

    @ApiModelProperty("06:15")
    @ExcelProperty(value = "06:15", index = 31)
    private BigDecimal electricity0615;

    @ApiModelProperty("06:30")
    @ExcelProperty(value = "06:30", index = 32)
    private BigDecimal electricity0630;

    @ApiModelProperty("06:45")
    @ExcelProperty(value = "06:45", index = 33)
    private BigDecimal electricity0645;

    @ApiModelProperty("07:00")
    @ExcelProperty(value = "07:00", index = 34)
    private BigDecimal electricity0700;

    @ApiModelProperty("07:15")
    @ExcelProperty(value = "07:15", index = 35)
    private BigDecimal electricity0715;

    @ApiModelProperty("07:30")
    @ExcelProperty(value = "07:30", index = 36)
    private BigDecimal electricity0730;

    @ApiModelProperty("07:45")
    @ExcelProperty(value = "07:45", index = 37)
    private BigDecimal electricity0745;

    @ApiModelProperty("08:00")
    @ExcelProperty(value = "08:00", index = 38)
    private BigDecimal electricity0800;

    @ApiModelProperty("08:15")
    @ExcelProperty(value = "08:15", index = 39)
    private BigDecimal electricity0815;

    @ApiModelProperty("08:30")
    @ExcelProperty(value = "08:30", index = 40)
    private BigDecimal electricity0830;

    @ApiModelProperty("08:45")
    @ExcelProperty(value = "08:45", index = 41)
    private BigDecimal electricity0845;

    @ApiModelProperty("09:00")
    @ExcelProperty(value = "09:00", index = 42)
    private BigDecimal electricity0900;

    @ApiModelProperty("09:15")
    @ExcelProperty(value = "09:15", index = 43)
    private BigDecimal electricity0915;

    @ApiModelProperty("09:30")
    @ExcelProperty(value = "09:30", index = 44)
    private BigDecimal electricity0930;

    @ApiModelProperty("09:45")
    @ExcelProperty(value = "09:45", index = 45)
    private BigDecimal electricity0945;

    @ApiModelProperty("10:00")
    @ExcelProperty(value = "10:00", index = 46)
    private BigDecimal electricity1000;

    @ApiModelProperty("10:15")
    @ExcelProperty(value = "10:15", index = 47)
    private BigDecimal electricity1015;

    @ApiModelProperty("10:30")
    @ExcelProperty(value = "10:30", index = 48)
    private BigDecimal electricity1030;

    @ApiModelProperty("10:45")
    @ExcelProperty(value = "10:45", index = 49)
    private BigDecimal electricity1045;

    @ApiModelProperty("11:00")
    @ExcelProperty(value = "11:00", index = 50)
    private BigDecimal electricity1100;

    @ApiModelProperty("11:15")
    @ExcelProperty(value = "11:15", index = 51)
    private BigDecimal electricity1115;

    @ApiModelProperty("11:30")
    @ExcelProperty(value = "11:30", index = 52)
    private BigDecimal electricity1130;

    @ApiModelProperty("11:45")
    @ExcelProperty(value = "11:45", index = 53)
    private BigDecimal electricity1145;

    @ApiModelProperty("12:00")
    @ExcelProperty(value = "12:00", index = 54)
    private BigDecimal electricity1200;

    @ApiModelProperty("12:15")
    @ExcelProperty(value = "12:15", index = 55)
    private BigDecimal electricity1215;

    @ApiModelProperty("12:30")
    @ExcelProperty(value = "12:30", index = 56)
    private BigDecimal electricity1230;

    @ApiModelProperty("12:45")
    @ExcelProperty(value = "12:45", index = 57)
    private BigDecimal electricity1245;

    @ApiModelProperty("13:00")
    @ExcelProperty(value = "13:00", index = 58)
    private BigDecimal electricity1300;

    @ApiModelProperty("13:15")
    @ExcelProperty(value = "13:15", index = 59)
    private BigDecimal electricity1315;

    @ApiModelProperty("13:30")
    @ExcelProperty(value = "13:30", index = 60)
    private BigDecimal electricity1330;

    @ApiModelProperty("13:45")
    @ExcelProperty(value = "13:45", index = 61)
    private BigDecimal electricity1345;

    @ApiModelProperty("14:00")
    @ExcelProperty(value = "14:00", index = 62)
    private BigDecimal electricity1400;

    @ApiModelProperty("14:15")
    @ExcelProperty(value = "14:15", index = 63)
    private BigDecimal electricity1415;

    @ApiModelProperty("14:30")
    @ExcelProperty(value = "14:30", index = 64)
    private BigDecimal electricity1430;

    @ApiModelProperty("14:45")
    @ExcelProperty(value = "14:45", index = 65)
    private BigDecimal electricity1445;

    @ApiModelProperty("15:00")
    @ExcelProperty(value = "15:00", index = 66)
    private BigDecimal electricity1500;

    @ApiModelProperty("15:15")
    @ExcelProperty(value = "15:15", index = 67)
    private BigDecimal electricity1515;

    @ApiModelProperty("15:30")
    @ExcelProperty(value = "15:30", index = 68)
    private BigDecimal electricity1530;

    @ApiModelProperty("15:45")
    @ExcelProperty(value = "15:45", index = 69)
    private BigDecimal electricity1545;

    @ApiModelProperty("16:00")
    @ExcelProperty(value = "16:00", index = 70)
    private BigDecimal electricity1600;

    @ApiModelProperty("16:15")
    @ExcelProperty(value = "16:15", index = 71)
    private BigDecimal electricity1615;

    @ApiModelProperty("16:30")
    @ExcelProperty(value = "16:30", index = 72)
    private BigDecimal electricity1630;

    @ApiModelProperty("16:45")
    @ExcelProperty(value = "16:45", index = 73)
    private BigDecimal electricity1645;

    @ApiModelProperty("17:00")
    @ExcelProperty(value = "17:00", index = 74)
    private BigDecimal electricity1700;

    @ApiModelProperty("17:15")
    @ExcelProperty(value = "17:15", index = 75)
    private BigDecimal electricity1715;

    @ApiModelProperty("17:30")
    @ExcelProperty(value = "17:30", index = 76)
    private BigDecimal electricity1730;

    @ApiModelProperty("17:45")
    @ExcelProperty(value = "17:45", index = 77)
    private BigDecimal electricity1745;

    @ApiModelProperty("18:00")
    @ExcelProperty(value = "18:00", index = 78)
    private BigDecimal electricity1800;

    @ApiModelProperty("18:15")
    @ExcelProperty(value = "18:15", index = 79)
    private BigDecimal electricity1815;

    @ApiModelProperty("18:30")
    @ExcelProperty(value = "18:30", index = 80)
    private BigDecimal electricity1830;

    @ApiModelProperty("18:45")
    @ExcelProperty(value = "18:45", index = 81)
    private BigDecimal electricity1845;

    @ApiModelProperty("19:00")
    @ExcelProperty(value = "19:00", index = 82)
    private BigDecimal electricity1900;

    @ApiModelProperty("19:15")
    @ExcelProperty(value = "19:15", index = 83)
    private BigDecimal electricity1915;

    @ApiModelProperty("19:30")
    @ExcelProperty(value = "19:30", index = 84)
    private BigDecimal electricity1930;

    @ApiModelProperty("19:45")
    @ExcelProperty(value = "19:45", index = 85)
    private BigDecimal electricity1945;

    @ApiModelProperty("20:00")
    @ExcelProperty(value = "20:00", index = 86)
    private BigDecimal electricity2000;

    @ApiModelProperty("20:15")
    @ExcelProperty(value = "20:15", index = 87)
    private BigDecimal electricity2015;

    @ApiModelProperty("20:30")
    @ExcelProperty(value = "20:30", index = 88)
    private BigDecimal electricity2030;

    @ApiModelProperty("20:45")
    @ExcelProperty(value = "20:45", index = 89)
    private BigDecimal electricity2045;

    @ApiModelProperty("21:00")
    @ExcelProperty(value = "21:00", index = 90)
    private BigDecimal electricity2100;

    @ApiModelProperty("21:15")
    @ExcelProperty(value = "21:15", index = 91)
    private BigDecimal electricity2115;

    @ApiModelProperty("21:30")
    @ExcelProperty(value = "21:30", index = 92)
    private BigDecimal electricity2130;

    @ApiModelProperty("21:45")
    @ExcelProperty(value = "21:45", index = 93)
    private BigDecimal electricity2145;

    @ApiModelProperty("22:00")
    @ExcelProperty(value = "22:00", index = 94)
    private BigDecimal electricity2200;

    @ApiModelProperty("22:15")
    @ExcelProperty(value = "22:15", index = 95)
    private BigDecimal electricity2215;

    @ApiModelProperty("22:30")
    @ExcelProperty(value = "22:30", index = 96)
    private BigDecimal electricity2230;

    @ApiModelProperty("22:45")
    @ExcelProperty(value = "22:45", index = 97)
    private BigDecimal electricity2245;

    @ApiModelProperty("23:00")
    @ExcelProperty(value = "23:00", index = 98)
    private BigDecimal electricity2300;

    @ApiModelProperty("23:15")
    @ExcelProperty(value = "23:15", index = 99)
    private BigDecimal electricity2315;

    @ApiModelProperty("23:30")
    @ExcelProperty(value = "23:30", index = 100)
    private BigDecimal electricity2330;

    @ApiModelProperty("23:45")
    @ExcelProperty(value = "23:45", index = 101)
    private BigDecimal electricity2345;

    @ApiModelProperty("24:00")
    @ExcelProperty(value = "24:00", index = 102)
    private BigDecimal electricity2400;
}
