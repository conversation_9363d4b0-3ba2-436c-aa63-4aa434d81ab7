package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("户号信息DTO")
public class VppUserAccountDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;

    @ApiModelProperty("主键id")
    private Long id;

    @NotBlank(message = "负荷用户户号不能为空")
    @ApiModelProperty(value = "负荷用户户号", example = "*************", required = true)
    private String consNo;

    @ApiModelProperty(value = "户号名称", example = "户号名称")
    private String consName;

    @NotNull(message = "额定功率不能为空")
    @ApiModelProperty(value = "额定功率", example = "1.60", required = true)
    private BigDecimal ratedPower;

    @NotNull(message = "电压等级不能为空")
    @ApiModelProperty(value = "电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)", example = "4", required = true)
    private Integer volLevel;

    @NotNull(message = "聚合容量不能为空")
    @ApiModelProperty(value = "聚合容量", example = "1.90", required = true)
    private BigDecimal ratedCapacity;

    @NotBlank(message = "位置code(到区)不能为空")
    @ApiModelProperty(value = "位置code(到区)", example = "140105")
    private String areaCode;

    @NotBlank(message = "所在区县不能为空")
    @ApiModelProperty(value = "所在区县", example = "小店区")
    private String districtAddr;

    @NotBlank(message = "位置code(详细信息)不能为空")
    @ApiModelProperty(value = "位置code(详细信息)", example = "山西省太原市小店区")
    private String areaCodeDetail;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址", example = "平阳路大马社区小学北侧")
    private String deptAddress;

    @NotBlank(message = "经度不能为空")
    @ApiModelProperty(value = "经度", example = "112.56")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty(value = "纬度", example = "37.80")
    private String lat;

    @NotBlank(message = "并网馈线不能为空")
    @ApiModelProperty(value = "并网馈线", example = "经纬1号线")
    private String conFeeder;

    @NotBlank(message = "并网变电站不能为空")
    @ApiModelProperty(value = "并网变电站", example = "南中环环网经纬")
    private String conTransSub;

    @ApiModelProperty(value = "供电单位编码", example = "1440106")
    @NotBlank(message = "供电单位编码不能为空")
    private String powerSupplyOrgNo;

    @ApiModelProperty(value = "供电电压编码", example = "AC00101")
    @NotBlank(message = "供电电压编码不能为空")
    private String voltType;

    @ApiModelProperty(value = "行业类型编码", example = "M000")
    @NotBlank(message = "行业类型编码不能为空")
    private String tradeType;

    @NotBlank(message = "资源类型编码不能为空")
    @ApiModelProperty(value = "包含资源类型编码", example = "5")
    private String resourceType;

    @NotBlank(message = "响应时间级别不能为空")
    @ApiModelProperty(value = "响应时间级别", example = "1")
    private String responseLevel;

    @NotBlank(message = "用电地址不能为空")
    @ApiModelProperty(value = "用电地址", example = "山西省太原市小店区平阳路大马社区小学北侧")
    private String electAddr;

}
