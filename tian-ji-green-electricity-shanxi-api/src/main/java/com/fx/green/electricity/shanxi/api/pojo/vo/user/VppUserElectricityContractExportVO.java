package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户电量合同导出 VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("用户电量合同导出VO")
public class VppUserElectricityContractExportVO {

    @ApiModelProperty("用户名称")
    @ExcelProperty(value = "用户名称", index = 0)
    private String userName;

    @ApiModelProperty("合同时间（格式：2025-01 ~ 2034-12）")
    @ExcelProperty(value = "合同时间", index = 0)
    private String contractTime;

    @ApiModelProperty("关联机组（格式：千佛寺风电场（285.00）,百佛寺风电场（282.00）,万佛寺风电场（280.00））")
    @ExcelProperty(value = "关联机组", index = 0)
    private String relatedUnits;

    @ApiModelProperty("合同价格")
    @ExcelProperty(value = "合同价格", index = 0)
    private BigDecimal contractPrice;

}
