package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 绿电直连合同维护导出 VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("绿电直连合同维护导出VO")
public class GeContractMaintenanceExportVO {

    @ApiModelProperty("新能源公司")
    @ExcelProperty(value = "新能源公司", index = 0)
    private String enterpriseName;

    @ApiModelProperty("机组名称")
    @ExcelProperty(value = "机组名称", index = 1)
    private String unitName;

    @ApiModelProperty("类型")
    @ExcelProperty(value = "类型", index = 2)
    private String unitTypeStr;

    @ApiModelProperty("合同时间（格式：2025-01 ~ 2034-12）")
    @ExcelProperty(value = "合同时间", index = 3)
    private String contractTime;

    @ApiModelProperty("合同价格")
    @ExcelProperty(value = "合同价格", index = 4)
    private BigDecimal contractPrice;

}
