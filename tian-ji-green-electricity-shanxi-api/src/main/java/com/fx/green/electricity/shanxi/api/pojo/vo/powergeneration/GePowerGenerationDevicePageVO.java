package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发电设备分页返回VO
 */
@Data
@ApiModel("发电设备分页返回VO")
public class GePowerGenerationDevicePageVO implements Serializable {

    @ApiModelProperty("设备编号")
    private Long id;

    @ApiModelProperty("发电企业编号")
    private Long enterpriseId;

    @ApiModelProperty("发电企业名称")
    private String enterpriseName;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备编号")
    private String code;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("设备类型(1逆变器、2升压站、3储能设备、4系统站、5汇流站)")
    private Integer type;

    @ApiModelProperty("关联机组数量")
    private Integer unitCount;

    @ApiModelProperty("关联电表数量")
    private Integer meterCount;

    @ApiModelProperty("上网方向电表数量")
    private Integer upMeterCount;

    @ApiModelProperty("下网方向电表数量")
    private Integer downMeterCount;

    @ApiModelProperty("汇流站方向电表数量")
    private Integer convergenceStationMeterCount;

    @ApiModelProperty("逆变站方向电表数量")
    private Integer inverterStationMeterCount;

    @ApiModelProperty("关联储能设备数量")
    private Integer storageDeviceCount;

    @ApiModelProperty("关联系统站数量")
    private Integer systemDeviceCount;

    @ApiModelProperty("关联升压站数量")
    private Integer boosterDeviceCount;

}
