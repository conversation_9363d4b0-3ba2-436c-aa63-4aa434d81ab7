package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("电量数据")
public class VppElectricQuantityVO extends BaseVO {

    private static final long serialVersionUID = 3739235975770782758L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("售电租户ID")
    private Long tenantId;

    @ApiModelProperty("数据类型 （1. 日前申报电量；2. 实际用电量）")
    private Integer type;

    @ApiModelProperty("运行日期")
    private Date runningDate;

    @ApiModelProperty("上传时间")
    private Date uploadTime;

    @ApiModelProperty("上传状态 （0. 未上传；1. 已上传）")
    private Integer status;

    @ApiModelProperty("电量总和")
    private BigDecimal sumElectricity;

    @ApiModelProperty("电量最大值")
    private BigDecimal maxElectricity;

    @ApiModelProperty("电量最小值")
    private BigDecimal minElectricity;

    @ApiModelProperty("电量平均值")
    private BigDecimal avgElectricity;

    /**
     * 获取最大日期以及最大电量
     */
    @ApiModelProperty("最大电量")
    private BigDecimal sumElectric;

    @ApiModelProperty("最大日期")
    private Date maxDate;

    @ApiModelProperty("文件地址")
    private String url;
}