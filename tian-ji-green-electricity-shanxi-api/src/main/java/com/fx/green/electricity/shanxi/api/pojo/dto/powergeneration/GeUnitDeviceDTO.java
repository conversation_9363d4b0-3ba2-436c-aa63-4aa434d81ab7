package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机组设备信息DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("机组设备信息DTO")
public class GeUnitDeviceDTO extends BaseDTO {
    private static final long serialVersionUID = 4802101084838943949L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备编号")
    private String deviceCode;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经纬度")
    private String coordinate;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;
}
