package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发电设备列表查询DTO
 */
@Data
@ApiModel("发电设备列表查询DTO")
public class GePowerGenerationDeviceListDTO implements Serializable {

    private static final long serialVersionUID = -525886433686176840L;

    @ApiModelProperty("发电企业名称")
    private String enterpriseName;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备编号")
    private String code;

    @ApiModelProperty("设备类型(1逆变器、2升压站、3储能设备、4系统站、5汇流站)")
    private Integer type;

}
