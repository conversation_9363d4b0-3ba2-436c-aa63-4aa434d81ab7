package com.fx.green.electricity.shanxi.api.enums.powergeneration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机组类型枚举
 */
@Getter
@AllArgsConstructor
public enum GeUnitTypeEnum {

    THERMAL(1, "火电"),
    HYDRO(2, "水电"),
    PUMPED_STORAGE(3, "抽水蓄能"),
    WIND(4, "风电"),
    SOLAR(5, "光伏"),
    COAL_GAS(6, "煤气层"),
    NATURAL_GAS(7, "天然气"),
    BLAST_FURNACE_GAS(8, "高炉尾气"),
    ENERGY_STORAGE(9, "储能");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String label;

    /**
     * 根据类型值获取枚举
     *
     * @param value 类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static GeUnitTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (GeUnitTypeEnum enumValue : GeUnitTypeEnum.values()) {
            if (enumValue.getValue().equals(value)) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 根据类型值获取描述
     *
     * @param value 类型值
     * @return 对应的描述，如果不存在则返回null
     */
    public static String getLabelByValue(Integer value) {
        GeUnitTypeEnum enumValue = getByValue(value);
        return enumValue != null ? enumValue.getLabel() : null;
    }

    /**
     * 判断给定的值是否为有效的机组类型
     *
     * @param value 类型值
     * @return 是否有效
     */
    public static boolean isValidValue(Integer value) {
        return getByValue(value) != null;
    }
}
