package com.fx.green.electricity.shanxi.api.api.user;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "vppUserElectricityContractApi")
public interface VppUserElectricityContractApi {

    /**
     * 获取用户电量合同列表
     *
     * @param param 用户电量合同DTO
     * @return 用户电量合同列表
     */
    @PostMapping("/vppUserElectricityContract/page")
    DataResult<FxPage<VppUserElectricityContractVO>> getUserElectricityContractPage(@RequestBody VppUserElectricityContractDTO param);

    /**
     * 修改用户电量合同
     *
     * @param param 用户电量合同DTO
     * @return 成功/失败
     */
    @PostMapping("/vppUserElectricityContract/update")
    DataResult<Void> updateUserElectricityContract(@RequestBody VppUserElectricityContractSaveDTO param);

    /**
     * 导出用户电量合同数据
     *
     * @param param 用户电量合同DTO
     * @return 导出数据列表
     */
    @PostMapping("/vppUserElectricityContract/export")
    DataResult<List<VppUserElectricityContractExportVO>> exportUserElectricityContract(@RequestBody VppUserElectricityContractDTO param);
}
