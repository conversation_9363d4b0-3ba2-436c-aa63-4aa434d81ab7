package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户信息详情 VO
 * 
 * <AUTHOR>
 */
@Data
public class VppUserInfoVO implements Serializable {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @ApiModelProperty(value = "负荷用户名称", example = "张三", required = true)
    private String name;

    @ApiModelProperty(value = "统一社会信用代码", example = "1234567890", required = true)
    private String userCode;

    @ApiModelProperty(value = "联系人姓名", example = "张三", required = true)
    private String contactName;

    @ApiModelProperty(value = "联系人电话", example = "1234567890", required = true)
    private String contactPhone;

    @ApiModelProperty(value = "微信绑定ID")
    private String openId;

    @ApiModelProperty(value = "红利系数")
    private BigDecimal dividendRatio;

    @ApiModelProperty(value = "位置code(到区)", example = "140105", required = true)
    private String areaCode;

    @ApiModelProperty(value = "所在区县", example = "小店区", required = true)
    private String districtAddr;

    @ApiModelProperty(value = "位置code(详细信息)", example = "山西省太原市小店区", required = true)
    private String areaCodeDetail;

    @ApiModelProperty(value = "详细地址", example = "平阳路大马社区小学北侧", required = true)
    private String deptAddress;

    @ApiModelProperty(value = "经度", example = "37.80", required = true)
    private String longitude;

    @ApiModelProperty(value = "纬度", example = "112.56", required = true)
    private String lat;

    @ApiModelProperty(value = "用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)", example = "1", required = true)
    private Integer electricityType;

    @ApiModelProperty(value = "企业简介", example = "xxxx")
    private String enterpriseIntroduction;

    @ApiModelProperty(value = "企业照片", example = "xxxx.png,xxx.png")
    private String enterprisePhotos;

    @ApiModelProperty("户号信息")
    private List<VppUserAccountVO> accountList;

}
