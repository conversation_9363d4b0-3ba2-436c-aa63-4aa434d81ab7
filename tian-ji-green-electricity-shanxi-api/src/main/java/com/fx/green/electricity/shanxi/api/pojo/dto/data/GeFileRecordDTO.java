package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件导入记录DTO
 */
@Data
@ApiModel("文件导入记录")
public class GeFileRecordDTO implements Serializable {
    private static final long serialVersionUID = -9210773325440826534L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("文件类型")
    private Integer fileType;

    @ApiModelProperty("数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dataDate;

    @ApiModelProperty("文件记录中所属id ， 机组或者租户")
    private Long belongId;

    @ApiModelProperty("创建用户ID")
    private Long createUser;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改用户ID")
    private Long updateUser;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("租户id")
    private Long tenantId;
}
