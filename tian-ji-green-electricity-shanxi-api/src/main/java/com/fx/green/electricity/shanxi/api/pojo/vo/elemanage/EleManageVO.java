package com.fx.green.electricity.shanxi.api.pojo.vo.elemanage;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("用户用电管理VO")
public class EleManageVO {

    @ApiModelProperty("时段")
    private List<String> timeFrame;

    @ApiModelProperty("数据")
    private List<PredictedElectricityInfo> dataList;

    @Data
    public static class PredictedElectricityInfo {
        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date dateDay;

        @ApiModelProperty("名字")
        private String name;

        @ApiModelProperty("户号")
        private String registered;

        @ApiModelProperty("1.虚拟电厂 2.用户")
        private Long type;

        @ApiModelProperty("实际用电量")
        private List<BigDecimal> actualElectricityList;

        @ApiModelProperty("关口表电量")
        private List<BigDecimal> dataGatherElectricityList;
    }

}
