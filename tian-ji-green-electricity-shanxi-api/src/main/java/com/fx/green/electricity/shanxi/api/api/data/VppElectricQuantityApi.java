package com.fx.green.electricity.shanxi.api.api.data;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 电量数据维护 Api 接口
 *
 * <AUTHOR>
 **/
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppApi")
public interface VppElectricQuantityApi {

    /**
     * 查询实际用电量导入记录
     *
     * @param param 查询参数
     * @return 导入记录
     */
    @PostMapping("vppElectricQuantity/queryImportRecord")
    DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param);

    /**
     * 日前申报数据导入
     *
     * @param vppElectricQuantityDTO 导入参数
     * @return 导入结果
     */
    @PostMapping("vppElectricQuantity/importRecord")
    DataResult<ImportExcelVO> importRecord(@RequestBody VppElectricQuantityDTO vppElectricQuantityDTO);


    /**
     * 获取更新时间
     *
     * @param param 查询参数
     * @return 更新时间
     */
    @PostMapping("vppElectricActual/getUpdateTime")
    DataResult<String> getUpdateTime(@RequestBody GetUpdateTimeDTO param);

    /**
     * 删除日前申报数据
     *
     * @param deleteRecordDTO 删除参数
     * @return 删除结果
     */
    @PostMapping("vppElectricQuantity/deleteRecordData")
    DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载日前申报数据
     *
     * @param param 查询参数
     * @return 下载结果
     */
    @PostMapping("vppElectricQuantity/downloadRecord")
    DataResult<List<SeElectricDeclareVO>> downloadRecord(@RequestBody CommonDTO.DateDTO param);

    /**
     * 导入实际用电量
     *
     * @param file 文件
     * @return 结果
     */
    @PostMapping(value = "/vppElectricQuantity/importActualElectricityConsumption", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<ImportExcelVO> importActualElectricityConsumption(@RequestPart("file") MultipartFile file);

    /**
     * 查询实际用电量详情
     *
     * @param param 数据维护DTO
     * @return 实际用电量详情列表
     */
    @PostMapping("/vppElectricQuantity/getActualElectricityConsumptionDetail")
    DataResult<List<ElectricityDetailVO>> getActualElectricityConsumptionDetail(@RequestBody DataMaintenanceQueryDTO param);

    /**
     * 删除实际用电量
     *
     * @param param 数据维护DTO
     * @return 结果
     */
    @PostMapping("/vppElectricQuantity/deleteActualElectricityConsumption")
    DataResult<Void> deleteActualElectricityConsumption(@RequestBody DataMaintenanceQueryDTO param);

    /**
     * 导入日前申报电量
     *
     * @param file 文件
     * @param time 时间
     * @return 结果
     */
    @PostMapping(value = "/vppElectricQuantity/importRecord", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file, @RequestParam("time") String time);
}
