package com.fx.green.electricity.shanxi.api.pojo.vo.user;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户电量合同 VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("用户电量合同VO")
public class VppUserElectricityContractVO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("合同开始时间")
    private Date contractStart;

    @ApiModelProperty("合同结束时间")
    private Date contractEnd;

    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

    @ApiModelProperty("机组列表")
    private List<VppUserElectricityContractUnitVO> unitList;

    @Data
    @ApiModel("用户电量合同机组VO")
    public static class VppUserElectricityContractUnitVO {

        @ApiModelProperty("机组ID")
        private Long unitId;

        @ApiModelProperty("机组名称")
        private String unitName;

        @ApiModelProperty("合同价格")
        private BigDecimal contractPrice;
    }

}
