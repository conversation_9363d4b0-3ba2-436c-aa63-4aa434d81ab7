package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 数据维护修改入参DTO
 */
@Data
@ApiModel("数据维护修改入参DTO")
@EqualsAndHashCode(callSuper = true)
public class DataMaintenanceUpdateDTO extends BaseDTO {

    @ApiModelProperty("日期")
    @NotNull(message = "日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;

    @ApiModelProperty("中长期维护状态(1.已维护，2.未维护，3.维护不全)")
    private int mediumLongTermStatus;

    @ApiModelProperty("日前申报维护状态(1.已维护，2.未维护)")
    private int declareStatus;

    @ApiModelProperty("实际用电量维护状态(1.已维护，2.未维护，3.维护不全)")
    private int realElectricityStatus;

    @ApiModelProperty("出清结果维护状态(1.已维护，2.未维护)")
    private int clearResultStatus;

    @ApiModelProperty("出清结果数据来源(1.省级智慧能源平台 2.导入 3.灵狐推送)")
    private Integer clearDataSources;

    @ApiModelProperty("日前申报数据来源(1.接口同步 2.导入 3.灵狐推送)")
    private Integer declareSources;

    @ApiModelProperty("实际用电量数据来源(1.导入，2.灵狐推送)")
    private Integer realElectricitySources;

    @ApiModelProperty("中长期数据来源(1.导入，2.灵狐推送)")
    private Integer mediumLongTermSources;

    @ApiModelProperty("机组实际发电曲线维护状态(1.已维护，2.未维护，3.部分维护)")
    private Integer UnitRealElectricityStatus;

    @ApiModelProperty("机组功率预测维护状态(1.已维护，2.未维护，3.部分维护)")
    private Integer UnitPowerPredictStatus;

    @ApiModelProperty("机组节点价格维护状态(1.已维护，2.未维护，3.部分维护)")
    private Integer UnitNodePriceStatus;

}
