package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 发电企业信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("发电企业信息VO")
public class GePowerGenerationEnterpriseVO extends BaseVO {
    private static final long serialVersionUID = -5958520382944945329L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("联系人")
    private String leader;

    @ApiModelProperty("联系方式")
    private String phone;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("地区名称")
    private String areaName;

    @ApiModelProperty("机组信息")
    private List<GeUnitBasicVO> unitBasicList;

    @ApiModelProperty("机组数量")
    private Integer unitCount;

    @ApiModelProperty("设备数量")
    private Integer deviceCount;
}
