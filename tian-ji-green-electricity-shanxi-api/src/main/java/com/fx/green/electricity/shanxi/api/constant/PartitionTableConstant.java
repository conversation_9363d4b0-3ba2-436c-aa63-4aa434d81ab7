package com.fx.green.electricity.shanxi.api.constant;

/**
 * 分区表主表
 *
 * <AUTHOR>
 **/
public interface PartitionTableConstant {

    /**
     * 营销用采数据表
     */
    String VPP_ACQUISITION_DATA = "vpp_acquisition_data";

    /**
     * 现货日前出清信息表
     */
    String VPP_CLEAR_INFORMATION = "vpp_clear_information";

    /**
     * 调控评估结果表
     */
    String VPP_EVALUATION_RESULT = "vpp_evaluation_result";

    /**
     * 调控评估结果统计表
     */
    String VPP_EVALUATION_RESULT_CENSUS = "vpp_evaluation_result_census";

    /**
     * 调控评估结果统计表
     */
    String VPP_USER_RESOLVE = "vpp_user_resolve";

    /**
     * 营销用采数据7天平均数
     */
    String VPP_ACQUISITION_DATA_AVG = "vpp_acquisition_data_avg";

    /**
     * 实际电量
     */
    String VPP_ELECTRIC_ACTUAL = "vpp_electric_actual";
    /**
     * 24点实际电量
     */
    String VPP_ELECTRIC_ACTUAL_24 = "vpp_electric_actual_24";

    /**
     * 用户实际电量聚合数据表
     */
    String VPP_ELECTRIC_ACTUAL_CONVERGE = "vpp_electric_actual_converge";

    /**
     * 实际电量中位数
     */
    String VPP_ELECTRIC_ACTUAL_MIDDLE = "vpp_electric_actual_middle";
}
