package com.fx.green.electricity.shanxi.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fx.green.electricity.shanxi.api.enums.VppSettlementPeriodType;

/**
 * 用户结算 - 类型
 *
 * <AUTHOR>
 */
public class VppSettlementPeriodTypeConverter implements Converter<Integer> {

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.length() == 0) {
            return null;
        }
        VppSettlementPeriodType seSettlementPeriodType = VppSettlementPeriodType.getSeSettlementPeriodTypeByValue(value);
        if (seSettlementPeriodType == null) {
            return null;
        }
        return seSettlementPeriodType.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer i, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }
}
