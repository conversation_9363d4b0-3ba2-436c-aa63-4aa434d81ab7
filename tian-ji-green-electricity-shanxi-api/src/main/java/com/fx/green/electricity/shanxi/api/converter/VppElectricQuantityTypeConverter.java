package com.fx.green.electricity.shanxi.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fx.green.electricity.shanxi.api.enums.VppElectricQuantityTypeEnum;

/**
 * 电量数据
 *
 * <AUTHOR>
 */
public class VppElectricQuantityTypeConverter implements Converter<Integer> {
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.length() == 0) {
            return null;
        }
        VppElectricQuantityTypeEnum vppElectricQuantityTypeEnum = VppElectricQuantityTypeEnum.getVppElectricQuantityTypeByValue(value);
        if (vppElectricQuantityTypeEnum == null) {
            return null;
        }
        return vppElectricQuantityTypeEnum.getCode();
    }


    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }
}
