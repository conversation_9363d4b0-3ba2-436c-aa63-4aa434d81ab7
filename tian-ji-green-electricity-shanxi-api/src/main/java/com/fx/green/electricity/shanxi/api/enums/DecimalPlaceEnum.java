package com.fx.green.electricity.shanxi.api.enums;

/**
 * 保留小数位枚举
 *
 * <AUTHOR>
 */
public enum DecimalPlaceEnum {

    ELECTRIC_QUANTITY(3, "电量"),
    RATIO(2, "比例、百分比"),
    LOAD(3, "负荷"),
    FOUR_RATIO(4, "四位小数(计算百分比时百分比)"),
    ADJUST_DECLARE(3,"用户申报调节能力"),
    COEFFICIENT(1,"系数"),
    PRICE(2,"价格"),
    TRANSITION(7,"计算过渡使用"),
    INTEGER(0, "整数"),

    COMPUTE(12,"计算过渡使用"),
    COMPUTE_30(30,"计算过渡使用"),
    ;

    public final Integer length;
    public final String value;

    DecimalPlaceEnum(Integer length, String value) {
        this.length = length;
        this.value = value;
    }
}
