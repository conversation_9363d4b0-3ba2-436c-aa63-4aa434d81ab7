package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 零售合同管理表
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("零售合同管理表")
public class VppRetailContractsManageVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -1614751121821809212L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("签约月份")
    @JsonFormat(pattern = "yyyy-MM")
    private Date signingMonth;

    @ApiModelProperty("零售合同表对应月份id")
    private Long retailContractsId;

    @ApiModelProperty("商品名称")
    private String tradeName;

    @ApiModelProperty("用户名字")
    private String name;

    @ApiModelProperty("红利分享系数")
    private BigDecimal dividendSharCoefficient;

    @ApiModelProperty("周期 1按月 ")
    private Integer cycle;

    @ApiModelProperty("类型 1电量 2电价")
    private Integer type;

    @ApiModelProperty("时点")
    private String timeFrame;

    @ApiModelProperty("价格值名称")
    private String priceName;

    @ApiModelProperty("价差值")
    private BigDecimal priceDifference;

    @ApiModelProperty("电量/电价")
    private BigDecimal val;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

}