package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserElectricityContractDTO extends BaseDTO {

    @ApiModelProperty("用户名称")
    private String userName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("合同开始时间")
    private Date contractStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("合同结束时间")
    private Date contractEnd;

}
