package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发电电表VO
 */
@Data
@ApiModel("发电电表VO")
public class GePowerGenerationMeterVO implements Serializable {

    private static final long serialVersionUID = -525886433686176839L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("发电设备id")
    private Long deviceId;

    @ApiModelProperty("电表名称")
    private String meterName;

    @ApiModelProperty("电表表号")
    private String meterNo;

    @ApiModelProperty("方向类型(0 无方向、1汇流站方向/上网方向、2逆变站方向/下网方向)")
    private Integer directionType;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
