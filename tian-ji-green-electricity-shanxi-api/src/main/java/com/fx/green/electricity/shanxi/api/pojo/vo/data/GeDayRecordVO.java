package com.fx.green.electricity.shanxi.api.pojo.vo.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数据维护每日数据情况
 */
@Data
@ApiModel("数据维护每日数据情况")
public class GeDayRecordVO implements Serializable {
    private static final long serialVersionUID = 71466337336466255L;

    @ApiModelProperty("数据日期")
    private String infoDate;

    @ApiModelProperty("文件维护情况")
    private List<FileRecordVO> fileRecordList;

    /**
     * 文件维护情况
     */
    @Data
    @ApiModel("文件维护情况")
    public static class FileRecordVO implements Serializable {

        private static final long serialVersionUID = 1776932492068503528L;

        @ApiModelProperty("文件类型")
        private Integer fileType;

        @ApiModelProperty("维护状态：1-已维护，2-未维护，3-部分维护")
        private Integer maintenanceStatus;
    }
}
