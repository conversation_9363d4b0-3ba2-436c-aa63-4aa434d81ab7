package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 文件导入类型枚举
 */
@Getter
public enum GeFileImportEnum {

    UNIT_REAL_ELECTRICITY(1, "机组实际发电曲线"),
    UNIT_NODE_PRICE(2, "机组节点价格"),
    UNIT_POWER_PREDICT(3, "机组功率预测"),
    FRONT_DAY_REPORT(4, "日前申报"),
    TENANT_REAL_ELECTRICITY(5, "实际用电量");

    private final Integer fileType;
    private final String fileName;

    GeFileImportEnum(Integer fileType, String fileName) {
        this.fileType = fileType;
        this.fileName = fileName;
    }

    /**
     * 根据文件类型获取枚举
     */
    public static GeFileImportEnum getFileTypeEnum(Integer fileType) {
        for (GeFileImportEnum enumValue : GeFileImportEnum.values()) {
            if (enumValue.getFileType().equals(fileType)) {
                return enumValue;
            }
        }
        return null;
    }

}
