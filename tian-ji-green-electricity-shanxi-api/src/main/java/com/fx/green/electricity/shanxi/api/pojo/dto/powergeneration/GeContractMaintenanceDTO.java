package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发电合同数据维护
 */
@Data
@ApiModel("发电合同数据维护")
public class GeContractMaintenanceDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 941043937853678233L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("机组名称")
    private String unitName;

    @ApiModelProperty("机组类型")
    private Integer unitType;

    @ApiModelProperty("合同标的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetStartDate;

    @ApiModelProperty("合同标的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetEndDate;

    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;
}
