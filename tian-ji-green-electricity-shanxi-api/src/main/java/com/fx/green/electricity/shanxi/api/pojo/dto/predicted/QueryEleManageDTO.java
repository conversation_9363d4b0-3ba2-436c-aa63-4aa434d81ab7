package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("预测电量dto")
public class QueryEleManageDTO extends BaseDTO {

    @ApiModelProperty("分时维度 1-96点 2-24点 3-日 4-月 5-年")
    @NotNull(message = "分时维度不能为空")
    private Integer timeSharing;

    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("1.获取绑定周期用户 2.获取全部用户")
    private Integer type;

    @ApiModelProperty("1.预测数据 2.对比数据")
    private Integer flag;

    @ApiModelProperty("户号")
    private String registered;

}
