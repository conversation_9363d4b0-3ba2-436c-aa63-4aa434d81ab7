package com.fx.green.electricity.shanxi.api.enums.powergeneration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电表方向类型枚举
 */
@Getter
@AllArgsConstructor
public enum MeterDirectionTypeEnum {

    /**
     * 系统站
     */
    UP(1, "上网方向"),
    DOWN(2, "下网方向"),

    /**
     * 升压站
     */
    CONVERGENCE_STATION(3, "汇流站方向"),
    INVERTER_STATION(4, "逆变站方向"),

    /**
     * 用户站
     */
    GRID_METER(5, "电网电表"),
    DC_METER(6, "直流电表");

    /**
     * 方向类型
     */
    private final Integer directionType;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据方向类型获取枚举
     */
    public static MeterDirectionTypeEnum getByDirectionType(Integer directionType) {
        for (MeterDirectionTypeEnum enumValue : MeterDirectionTypeEnum.values()) {
            if (enumValue.getDirectionType().equals(directionType)) {
                return enumValue;
            }
        }
        return null;
    }
}
