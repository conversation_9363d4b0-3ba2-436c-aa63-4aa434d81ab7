package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 日前申报电量导入Excel实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel("日前申报电量导入Excel")
public class VppElectricDeclareImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer serialNumber;

    @ApiModelProperty("时段")
    @ExcelProperty(value = "时段", index = 1)
    private String timeFrame;

    @ApiModelProperty("电量(MW)")
    @ExcelProperty(value = "电量(MW)", index = 2)
    private BigDecimal electricity;
}
