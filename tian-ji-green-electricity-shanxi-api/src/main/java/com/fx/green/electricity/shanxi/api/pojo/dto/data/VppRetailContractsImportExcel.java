package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 零售合同导入Excel实体
 *
 * <AUTHOR>
 */
@Data
@ApiModel("零售合同导入Excel实体")
public class VppRetailContractsImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer serialNumber;

    @ApiModelProperty("电力用户名称")
    @ExcelProperty(value = "电力用户名称", index = 1)
    @NotBlank(message = "电力用户名称不能为空")
    private String userName;

    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份", index = 2)
    @DateTimeFormat("yyyy-MM")
    @NotNull(message = "月份不能为空")
    private Date month;

    @ApiModelProperty("状态")
    @ExcelProperty(value = "状态", index = 3)
    @NotBlank(message = "状态不能为空")
    private String status;

    @ApiModelProperty("商品名称")
    @ExcelProperty(value = "商品名称", index = 4)
    @NotBlank(message = "商品名称不能为空")
    private String tradeName;

    @ApiModelProperty("货号")
    @ExcelProperty(value = "货号", index = 5)
    private String sku;

    @ApiModelProperty("板块")
    @ExcelProperty(value = "板块", index = 6)
    private String board;

    @ApiModelProperty("是否中小板块套餐")
    @ExcelProperty(value = "是否中小板块套餐", index = 7)
    private String isSmallBoardPackage;

    @ApiModelProperty("套餐类型")
    @ExcelProperty(value = "套餐类型", index = 8)
    private String packageType;

    @ApiModelProperty("是否约定电量")
    @ExcelProperty(value = "是否约定电量", index = 9)
    private String isAgreementElectricity;

    @ApiModelProperty("是否分时")
    @ExcelProperty(value = "是否分时", index = 10)
    @NotBlank(message = "是否分时不能为空")
    @Pattern(regexp = "^(是|否)$", message = "是否分时只能是'是'或'否'")
    private String isTimeSharing;

    @ApiModelProperty("时段")
    @ExcelProperty(value = "时段", index = 11)
    @NotBlank(message = "时段不能为空")
    @Pattern(regexp = "^([1-9]|1[0-9]|2[0-4])$", message = "时段必须是1-24之间的数字")
    private String timeSlot;

    @ApiModelProperty("约定电量")
    @ExcelProperty(value = "约定电量", index = 12)
    private String agreementElectricity;

    @ApiModelProperty("基础价格套餐/基准套餐 - 不约定/约定电量电价 - 价格值名称")
    @ExcelProperty(value = {"基础价格套餐/基准套餐", "不约定/约定电量电价", "价格值名称"}, index = 13)
    private String priceValueName;

    @ApiModelProperty("基础价格套餐/基准套餐 - 不约定/约定电量电价 - 价格值")
    @ExcelProperty(value = {"基础价格套餐/基准套餐", "不约定/约定电量电价", "价格值"}, index = 14)
    private String priceValue;

    @ApiModelProperty("基础价格套餐/基准套餐 - 不约定/约定电量电价 - 价差值")
    @ExcelProperty(value = {"基础价格套餐/基准套餐", "不约定/约定电量电价", "价差值"}, index = 15)
    private String priceDifference;

    /**
     * 获取价格值的BigDecimal类型
     */
    public BigDecimal getPriceValueDecimal() {
        if (priceValue == null || priceValue.trim().isEmpty() || "-".equals(priceValue.trim())) {
            return null;
        }
        try {
            return new BigDecimal(priceValue.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("价格值格式不正确: " + priceValue);
        }
    }

    /**
     * 获取价差值的BigDecimal类型
     */
    public BigDecimal getPriceDifferenceDecimal() {
        if (priceDifference == null || priceDifference.trim().isEmpty() || "-".equals(priceDifference.trim())) {
            return null;
        }
        try {
            return new BigDecimal(priceDifference.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("价差值格式不正确: " + priceDifference);
        }
    }

    /**
     * 获取时段的整数值
     */
    public Integer getTimeSlotInteger() {
        if (timeSlot == null || timeSlot.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(timeSlot.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("时段格式不正确: " + timeSlot);
        }
    }

    /**
     * 判断是否为有效的合同数据
     */
    public boolean isValidContract() {
        return !"已解约".equals(status) && "是".equals(isTimeSharing);
    }

    /**
     * 获取24小时制时间点
     */
    public String getTimeFrame() {
        Integer slot = getTimeSlotInteger();
        if (slot == null || slot < 1 || slot > 24) {
            throw new IllegalArgumentException("时段值超出范围: " + slot);
        }
        return com.fx.green.electricity.shanxi.api.constant.VppConstant.TWENTY_FOUR_TIMES[slot - 1];
    }
}
