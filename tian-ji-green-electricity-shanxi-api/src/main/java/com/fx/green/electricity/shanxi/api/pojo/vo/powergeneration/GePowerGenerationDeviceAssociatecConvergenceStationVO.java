package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 汇总站关联发电设备DTO
 */
@Data
@ApiModel("汇总站关联发电设备DTO")
public class GePowerGenerationDeviceAssociatecConvergenceStationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("汇总站id")
    private Long convergenceStationId;

    @ApiModelProperty("储能设备DTO")
    private List<StorageDeviceDTO> storageDevices;

    @ApiModelProperty("系统站DTO")
    private List<SystemDeviceDTO> systemDevices;

    @ApiModelProperty("升压站DTO")
    private List<BoosterDeviceDTO> boosterDevices;

    @ApiModelProperty("用户站DTO")
    private List<MeterDeviceDTO> gridDevices;

    @Data
    @ApiModel("储能设备DTO")
    public static class StorageDeviceDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("储能设备id")
        private Long deviceId;

        @ApiModelProperty("电表DTO")
        private List<MeterDeviceDTO> meterDevices;
    }


    @Data
    @ApiModel("系统站DTO")
    public static class SystemDeviceDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("系统站id")
        private Long deviceId;
    }

    @Data
    @ApiModel("升压站DTO")
    public static class BoosterDeviceDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("升压站id")
        private Long deviceId;

        @ApiModelProperty("电表DTO")
        private List<MeterDeviceDTO> meterDevices;
    }

    @Data
    @ApiModel("电表DTO")
    public static class MeterDeviceDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("电表名称")
        @NotBlank(message = "电表名称不能为空")
        private String meterName;

        @ApiModelProperty("电表表号")
        @NotBlank(message = "电表表号不能为空")
        private String meterNo;

        @ApiModelProperty("方向类型(0 无方向、1上网方向、2下网方向、3汇流站方向、4逆变站方向、5电网电表、6直流电表)")
        @NotNull(message = "方向类型不能为空")
        private Integer directionType;
    }
}
