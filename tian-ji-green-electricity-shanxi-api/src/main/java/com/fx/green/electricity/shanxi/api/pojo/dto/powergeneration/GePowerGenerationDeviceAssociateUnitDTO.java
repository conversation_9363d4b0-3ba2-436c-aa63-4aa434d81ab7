package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("发电设备关联机组DTO")
public class GePowerGenerationDeviceAssociateUnitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发电设备id")
    private Long deviceId;

    @ApiModelProperty("机组id")
    private List<Long> unitIds;
}
