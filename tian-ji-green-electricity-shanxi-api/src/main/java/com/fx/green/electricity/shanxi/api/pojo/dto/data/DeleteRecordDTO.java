package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据维护DTO
 */
@Data
@ApiModel("数据维护DTO")
@EqualsAndHashCode(callSuper = true)
public class DeleteRecordDTO extends BaseDTO {

    @ApiModelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;
    
}
