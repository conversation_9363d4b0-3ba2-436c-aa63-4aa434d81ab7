package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发电设备表
 */
@Data
@ApiModel("发电设备表")
public class GePowerGenerationDeviceSaveDTO implements Serializable {

    private static final long serialVersionUID = -525886433686176836L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("设备名称")
    @NotBlank(message = "设备名称不能为空")
    private String name;

    @ApiModelProperty("设备编号")
    @NotBlank(message = "设备编号不能为空")
    private String code;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("设备类型(1逆变器、2升压站、3储能设备、4系统站、5汇流站)")
    @NotNull(message = "设备类型不能为空")
    private Integer type;

}
