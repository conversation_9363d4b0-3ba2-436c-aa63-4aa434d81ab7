package com.fx.green.electricity.shanxi.api.api.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 绿电直连合同维护 Api 接口
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "geContractMaintenanceApi")
public interface GeContractMaintenanceApi {

    /**
     * 新增合同数据
     *
     * @param param 合同数据
     */
    @PostMapping("/geContractMaintenance/insertContract")
    DataResult<Void> insertContract(@RequestBody GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param 合同数据
     */
    @PostMapping("/geContractMaintenance/deleteContract")
    DataResult<Void> deleteContract(@RequestBody IdDTO param);

    /**
     * 合同数据列表
     *
     * @param param 合同数据
     * @return 合同数据列表
     */
    @PostMapping("/geContractMaintenance/contractList")
    DataResult<List<GeContractMaintenanceVO>> contractList(@RequestBody GeContractMaintenanceDTO param);

    /**
     * 合同数据列表分页
     *
     * @param param 合同数据
     * @return 合同数据列表分页
     */
    @PostMapping("/geContractMaintenance/page")
    DataResult<FxPage<GeContractMaintenanceVO>> getPowerGenerationContractPage(@RequestBody GeContractMaintenanceDTO param);

    /**
     * 导出合同数据
     *
     * @param param 合同数据
     * @return 合同数据列表
     */
    @PostMapping("/geContractMaintenance/export")
    DataResult<List<GeContractMaintenanceExportVO>> exportContract(@RequestBody GeContractMaintenanceDTO param);
}
