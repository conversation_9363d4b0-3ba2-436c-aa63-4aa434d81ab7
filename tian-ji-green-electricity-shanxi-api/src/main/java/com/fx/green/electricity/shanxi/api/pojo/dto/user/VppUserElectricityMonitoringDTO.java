package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel("用户用电监测DTO")
public class VppUserElectricityMonitoringDTO {

    @Data
    @ApiModel("查询用户树形结构DTO")
    public static class QueryUserTreeDTO {

        @NotNull(message = "请选择时间")
        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date queryDate;

        @ApiModelProperty("类型(1-绑定用户，2-全部)")
        private Integer type;

    }
}
