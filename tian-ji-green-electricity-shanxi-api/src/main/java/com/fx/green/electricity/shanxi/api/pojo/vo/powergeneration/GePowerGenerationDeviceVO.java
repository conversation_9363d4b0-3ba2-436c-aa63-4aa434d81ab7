package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发电设备表
 */
@Data
@ApiModel("发电设备表")
public class GePowerGenerationDeviceVO implements Serializable {

    private static final long serialVersionUID = -525886433686176836L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备编号")
    private String code;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("设备类型(1逆变器、2升压站、3储能设备、4系统站、5汇流站)")
    private Integer type;

}
