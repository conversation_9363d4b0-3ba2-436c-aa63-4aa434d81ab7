package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 维护状态枚举
 */
@Getter
public enum MaintenanceStatusEnum {

    FULLY_MAINTAINED(1, "已维护"),
    NOT_MAINTAINED(2, "未维护"),
    PARTIALLY_MAINTAINED(3, "部分维护");

    private final Integer status;
    private final String description;

    MaintenanceStatusEnum(Integer status, String description) {
        this.status = status;
        this.description = description;
    }
}
