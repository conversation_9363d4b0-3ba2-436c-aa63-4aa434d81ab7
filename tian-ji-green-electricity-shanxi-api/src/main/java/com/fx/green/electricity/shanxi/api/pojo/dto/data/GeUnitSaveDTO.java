package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 机组导入信息存储DTO
 */
@Data
@ApiModel("机组导入信息存储DTO")
public class GeUnitSaveDTO implements Serializable {
    private static final long serialVersionUID = -6250758115445555885L;

    @ApiModelProperty("文件类型")
    private Integer fileType;

    @ApiModelProperty("文件内容")
    private List<GeUnitImportDTO> fileDataList;
}
