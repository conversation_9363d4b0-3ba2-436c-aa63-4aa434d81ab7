package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 用户结算 - 定价方式
 *
 * <AUTHOR>
 **/
@Getter
public enum VppTimeSharingEnum {
    /**
     * 定价方式
     */
    POINT_96(1, 15, "96点"),
    POINT_24(2, 15, "24点"),
    <PERSON>A<PERSON>(3, 1440, "日"),
    <PERSON>ON<PERSON>(4, 30, "月"),
    YEAR(5, 365, "年");

    private final int code;
    private final int groupType;
    private final String value;

    VppTimeSharingEnum(int code, int groupType,String value) {
        this.code = code;
        this.groupType = groupType;
        this.value = value;
    }

    public static int getGroupTypeByCode(int code) {
        for (VppTimeSharingEnum timeSharingEnum : VppTimeSharingEnum.values()) {
            if (timeSharingEnum.code == code) {
                return timeSharingEnum.getGroupType();
            }
        }
        return 0;
    }
    //根据code判断是否是groupType15
    public static boolean isGroupType15(int code) {
        for (VppTimeSharingEnum timeSharingEnum : VppTimeSharingEnum.values()) {
            if (timeSharingEnum.getCode() == code && timeSharingEnum.getGroupType() == 15) {
                return true;
            }
        }
        return false;
    }

}
