package com.fx.green.electricity.shanxi.api.api.elemanage;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户实际用电量 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppEleManageApi")
public interface VppEleManageApi {

    /**
     * 获取虚拟电厂用户户号三层树状列表
     *
     * @param param 查询参数
     * @return 树状列表
     */
    @PostMapping("vppEleManage/queryThreeFloorsTreeList")
    DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(@RequestBody QueryUserTreeListDTO param);

    /**
     * 获取数据列表
     *
     * @param param 查询参数
     * @return 数据列表
     */
    @PostMapping("vppEleManage/queryEleManage")
    DataResult<EleManageVO> queryEleManage(@RequestBody QueryEleManageDTO param);
}
