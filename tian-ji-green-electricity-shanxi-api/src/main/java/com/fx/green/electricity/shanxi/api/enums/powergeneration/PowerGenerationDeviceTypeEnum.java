package com.fx.green.electricity.shanxi.api.enums.powergeneration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发电设备类型枚举
 */
@Getter
@AllArgsConstructor
public enum PowerGenerationDeviceTypeEnum {

    INVERTER(1, "逆变器"),
    BOOSTER(2, "升压站"),
    STORAGE(3, "储能设备"),
    SYSTEM(4, "系统站"),
    METER(5, "汇流站"),
    USER(6, "用户站");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据类型获取枚举
     */
    public static PowerGenerationDeviceTypeEnum getByType(Integer type) {
        for (PowerGenerationDeviceTypeEnum enumValue : PowerGenerationDeviceTypeEnum.values()) {
            if (enumValue.getType().equals(type)) {
                return enumValue;
            }
        }
        return null;
    }
}
