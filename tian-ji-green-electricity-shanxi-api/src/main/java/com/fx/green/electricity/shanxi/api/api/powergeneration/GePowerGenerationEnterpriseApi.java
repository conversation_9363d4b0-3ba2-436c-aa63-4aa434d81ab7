package com.fx.green.electricity.shanxi.api.api.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 发电企业信息和机组相关维护 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "GePowerGenerationEnterpriseApi")
public interface GePowerGenerationEnterpriseApi {

    /**
     * 新增机组信息
     *
     * @param param 机组信息
     */
    @PostMapping("geUnitBasic/insertUnitBase")
    DataResult<Void> insertUnitBase(@RequestBody GeUnitBasicDTO param);

    /**
     * 删除机组信息
     *
     * @param param 机组信息
     */
    @PostMapping("geUnitBasic/deleteUnitBase")
    DataResult<Void> deleteUnitBase(@RequestBody IdDTO param);

    /**
     * 根据租户id查询对应机组
     *
     * @param param 机组信息
     * @return 机组信息列表
     */
    @PostMapping("geUnitBasic/unitBaseList")
    DataResult<List<GeUnitBasicVO>> unitBaseList(@RequestBody IdDTO param);

    /**
     * 新增机组设备信息
     *
     * @param param 机组设备信息
     */
    @PostMapping("/geUnitDevice/create")
    DataResult<Void> insertDevice(@RequestBody GeUnitDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param 机组设备信息
     */
    @PostMapping("/geUnitDevice/delete")
    DataResult<Void> deleteDevice(@RequestBody IdDTO param);

    /**
     * 新增发电企业信息
     *
     * @param param 发电企业信息
     */
    @PostMapping("/gePowerGenerationEnterprise/create")
    DataResult<Void> createPowerGenerationEnterprise(@RequestBody GePowerGenerationEnterpriseDTO param);

    /**
     * 删除发电企业信息
     *
     * @param param 发电企业信息
     */
    @PostMapping("/gePowerGenerationEnterprise/delete")
    DataResult<Void> deletePowerGenerationEnterprise(@RequestBody IdDTO param);

    /**
     * 发电企业信息分页
     *
     * @param param 发电企业信息
     * @return 发电企业信息列表
     */
    @PostMapping("/gePowerGenerationEnterprise/page")
    DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterprisePage(@RequestBody GePowerGenerationEnterpriseDTO param);

    /**
     * 获取所有发电企业信息和机组对应信息
     *
     * @return 发电企业信息和机组对应信息列表
     */
    @PostMapping("/gePowerGenerationEnterprise/list-all")
    DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterpriseList();

    /**
     * 获取企业机组树
     *
     * @return 企业机组树列表
     */
    @PostMapping("/gePowerGenerationEnterprise/unit-tree")
    DataResult<List<GePowerGenerationEnterpriseUnitTreeVO>> getEnterpriseUnitTree(@RequestBody GePowerGenerationEnterpriseUnitTreeDTO param);
}
