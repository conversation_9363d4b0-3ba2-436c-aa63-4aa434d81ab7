package com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机组信息DTO
 */
@Data
@ApiModel("机组信息DTO")
public class GeUnitBasicDTO implements Serializable {
    private static final long serialVersionUID = -6274396898301944175L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("机组名称")
    private String unitName;

    @ApiModelProperty("装机容量")
    private BigDecimal capacity;

    @ApiModelProperty("机组类型")
    private Integer type;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;
}
