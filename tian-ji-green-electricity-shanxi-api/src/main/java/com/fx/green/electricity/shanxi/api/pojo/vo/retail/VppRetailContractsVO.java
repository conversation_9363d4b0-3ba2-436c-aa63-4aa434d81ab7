package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 **/
@Data
@ApiModel("零售合同表")
public class VppRetailContractsVO implements Serializable {

    private static final long serialVersionUID = -5072049082851048405L;
    @ApiModelProperty("主键")
    private Long id;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("运行月份")
    private Date runMonth;

    @ApiModelProperty("数据项")
    private String dataItem;

    @ApiModelProperty("0未导入 1导入")
    private Integer status;

    @ApiModelProperty("导入时间")
    private Date importDate;

    @ApiModelProperty("附件")
    private String annex;

    @ApiModelProperty("附件地址")
    private String url;

}