package com.fx.green.electricity.shanxi.api.api.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceAssociatecConvergenceStationVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDevicePageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 发电设备信息 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "GePowerGenerationDeviceApi")
public interface GePowerGenerationDeviceApi {

    /**
     * 新增发电设备信息
     * @param param 发电设备信息
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/create")
    DataResult<Void> createPowerGenerationDevice(@RequestBody GePowerGenerationDeviceSaveDTO param);

    /**
     * 删除发电设备信息
     * @param param 发电设备信息
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/delete")
    DataResult<Void> deletePowerGenerationDevice(@RequestBody IdDTO param);

    /**
     * 获得发电设备信息详情
     *
     * @param param 发电设备信息
     * @return 发电设备信息
     */
    @PostMapping("/gePowerGenerationDevice/get")
    DataResult<GePowerGenerationDeviceVO> getPowerGenerationDevice(@RequestBody IdDTO param);

    /**
     * 更新发电设备信息
     *
     * @param param 发电设备信息
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/update")
    DataResult<Void> updatePowerGenerationDevice(@RequestBody GePowerGenerationDeviceSaveDTO param);

    /**
     * 获得发电设备信息分页
     * @param param 发电设备信息
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/page")
    DataResult<FxPage<GePowerGenerationDevicePageVO>> getPowerGenerationDevicePage(@RequestBody GePowerGenerationDevicePageDTO param);

    /**
     * 关联机组
     * @param param 发电设备关联机组DTO
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/associate-unit")
    DataResult<Void> associateUnit(@RequestBody GePowerGenerationDeviceAssociateUnitDTO param);

    /**
     * 查询关联机组列表
     * @param param 发电设备id
     * @return 机组 id 列表
     */
    @PostMapping("/gePowerGenerationDevice/unit-list")
    DataResult<List<Long>> getUnitList(@RequestBody IdDTO param);

    /**
     * 批量新增发电电表信息
     * @param param 发电电表批量保存DTO
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/create-meter")
    DataResult<Void> createPowerGenerationMeter(@RequestBody @Valid GePowerGenerationMeterSaveDTO param);

    /**
     * 查询关联电表列表
     * @param param 发电设备id
     * @return 电表信息列表
     */
    @PostMapping("/gePowerGenerationDevice/meter-list")
    DataResult<List<GePowerGenerationMeterVO>> getMeterList(@RequestBody IdDTO param);

    /**
     * 汇总站关联发电设备
     *
     * @param param 汇总站关联发电设备DTO
     * @return 结果
     */
    @PostMapping("/gePowerGenerationDevice/convergence-station-associate")
    DataResult<Void> convergenceStationAssociate(@RequestBody GePowerGenerationDeviceAssociatecConvergenceStationDTO param);

    /**
     * 查询汇总站关联的发电设备信息
     *
     * @param param 汇总站ID
     * @return 汇总站关联发电设备信息
     */
    @PostMapping("/gePowerGenerationDevice/get-convergence-station-associated")
    DataResult<GePowerGenerationDeviceAssociatecConvergenceStationVO> getConvergenceStationAssociated(@RequestBody IdDTO param);

    /**
     * 查询发电设备信息列表
     *
     * @param param 查询参数
     * @return 发电设备信息列表
     */
    @PostMapping("/gePowerGenerationDevice/list")
    DataResult<List<GePowerGenerationDeviceVO>> getPowerGenerationDeviceList(@RequestBody GePowerGenerationDeviceListDTO param);
}
