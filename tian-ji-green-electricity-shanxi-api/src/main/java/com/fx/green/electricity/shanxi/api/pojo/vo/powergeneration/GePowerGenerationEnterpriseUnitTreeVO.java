package com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发电企业机组树VO
 */
@Data
@ApiModel("发电企业机组树VO")
public class GePowerGenerationEnterpriseUnitTreeVO implements Serializable {

    private static final long serialVersionUID = -525886433686176842L;

    @ApiModelProperty("企业id")
    private Long id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("机组列表")
    private List<UnitInfo> unitList;

    @Data
    @ApiModel("机组信息")
    public static class UnitInfo implements Serializable {

        private static final long serialVersionUID = -525886433686176843L;

        @ApiModelProperty("机组id")
        private Long id;

        @ApiModelProperty("机组名称")
        private String name;

        @ApiModelProperty("是否绑定发电合同(1:已绑定,2:未绑定)")
        private Integer isBind;
    }

}
