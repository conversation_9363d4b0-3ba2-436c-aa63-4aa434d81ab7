package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserElectricityContractSaveDTO extends BaseDTO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("机组ID 集合")
    private List<Long> unitIds;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("合同开始时间")
    private Date contractStart;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("合同结束时间")
    private Date contractEnd;

}
