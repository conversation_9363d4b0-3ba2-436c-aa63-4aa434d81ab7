package com.fx.green.electricity.shanxi.api.api.data;

import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 数据维护 Api 接口
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "geMaintenanceApi", url = "${tj-dev.geMaintenanceApi:}")
public interface GeMaintenanceApi {

    /**
     * 通过不同类型导入机组数据
     *
     * @param param 单位数据
     */
    @PostMapping("/geUnitNodePrice/importUnitData")
    DataResult<Void> importUnitData(@RequestBody GeUnitSaveDTO param);

    /**
     * 导入文件记录表
     *
     * @param param 单位数据
     */
    @PostMapping("/geFileRecord/insertFileRecord")
    DataResult<Void> insertFileRecord(@RequestBody List<GeFileRecordDTO> param);

    /**
     * 文件维护记录
     *
     * @param param 文件记录数据
     * @return 文件维护记录
     */
    @PostMapping("/geFileRecord/getFileRecordByDay")
    DataResult<List<GeDayRecordVO>> getFileRecordByDay(@RequestBody GeFileRecordDTO param);

    /**
     * 文件列表状态
     *
     * @param param 文件记录数据
     * @return 文件列表状态
     */
    @PostMapping("/geFileRecord/fileStatus")
    DataResult<List<GeFileRecordVO>> fileStatus(@RequestBody GeFileRecordDTO param);

    /**
     * 获取当日下数据
     *
     * @param param 文件记录数据
     * @return 当日下数据
     */
    @PostMapping("/geFileRecord/getInfoDataValue")
    DataResult<Map<String, String>> getInfoDataValue(@RequestBody GeFileRecordDTO param);

    /**
     * 通过不同类型导入机组数据
     *
     * @param file 文件
     * @param type 类型
     */
    @PostMapping(value = "/gePowerGenerationData/importPowerGenerationData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<ImportExcelVO> importPowerGenerationData(@RequestPart("file") MultipartFile file, @RequestParam("type") Integer type);


}
