<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.user.VppUserElectricityContractMapper">

    <select id="getUserElectricityContractList"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO">
        select uuc.id,
        vu.id as user_id,
        vu.name as user_name,
        uuc.contract_start,
        uuc.contract_end,
        uuc.contract_price
        from vpp_user vu
        left join vpp_user_electricity_contract uuc on vu.id = uuc.user_id
        where vu.tenant_id = #{tenantId}
        and vu.is_delete = 0
        <if test="userName != null and userName != ''">
            AND vu.name like concat('%',#{userName},'%')
        </if>
        <if test="contractStart != null and contractEnd != null">
            AND ((uuc.contract_start &gt;= #{contractStart} AND uuc.contract_start &lt;= #{contractEnd})
            OR (uuc.contract_end &gt;= #{contractStart} AND uuc.contract_end &lt;= #{contractEnd})
            OR (uuc.contract_start &lt;= #{contractStart} AND uuc.contract_end &gt;= #{contractEnd}))
        </if>
        order by uuc.contract_start desc
    </select>

    <select id="getUserElectricityContractPage"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO">
        select uuc.id,
        vu.id as user_id,
        vu.name as user_name,
        uuc.contract_start,
        uuc.contract_end,
        uuc.contract_price
        from vpp_user vu
        left join vpp_user_electricity_contract uuc on vu.id = uuc.user_id
        where vu.tenant_id = #{tenantId}
        and vu.is_delete = 0
        <if test="userName != null and userName != ''">
            AND vu.name like concat('%',#{userName},'%')
        </if>
        <if test="contractStart != null and contractEnd != null">
            AND ((uuc.contract_start &gt;= #{contractStart} AND uuc.contract_start &lt;= #{contractEnd})
            OR (uuc.contract_end &gt;= #{contractStart} AND uuc.contract_end &lt;= #{contractEnd})
            OR (uuc.contract_start &lt;= #{contractStart} AND uuc.contract_end &gt;= #{contractEnd}))
        </if>
        order by uuc.contract_start desc
    </select>


</mapper>
