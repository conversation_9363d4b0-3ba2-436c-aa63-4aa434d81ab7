<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.data.GeFileRecordMapper">

    <select id="getFileRecordByUnit" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO">
        SELECT gu."id" unit_id,
               gu.unit_name,
               gt."id" enterprise_id,
               gt."name" enterprise_name,
               gf.data_date
        FROM "ge_file_record" gf
                 RIGHT JOIN ge_unit_basic gu ON gu."id" = gf.belong_id AND ${ew.sqlSegment}
                 LEFT JOIN ge_power_generation_enterprise gt ON gt."id" = gu.enterprise_id

    </select>
    <select id="getFileRecordByTenant" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO">
        SELECT gt."id" enterprise_id,
               gt."name" enterprise_name,
               gf.data_date
        FROM "ge_file_record" gf
                 RIGHT JOIN ge_power_generation_enterprise gt ON gt."id" = gf.belong_id AND ${ew.sqlSegment}
    </select>
</mapper>