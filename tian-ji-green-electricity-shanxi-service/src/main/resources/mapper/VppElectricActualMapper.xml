<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO vpp_electric_actual ( id, name, tenant_id, user_code, electric_id,
        registered, metering_code, date_day, time_frame, electricity, is_delete, user_id )
        VALUES
        <foreach collection="eleList" item="item" index="index" separator=",">
            (#{item.id}, #{item.name}, #{item.tenantId}, #{item.userCode}, #{item.electricId}, #{item.registered},
            #{item.meteringCode},#{item.dateDay},#{item.timeFrame},#{item.electricity},#{item.isDelete}, #{item.userId})
        </foreach>
    </insert>

    <delete id="deleteByElectricId">
        delete
        from vpp_electric_actual
        where electric_id = #{electricId}
    </delete>

    <select id="importElectricCalculate">
        call import_electric_calculate(#{tenantId, mode=IN, jdbcType=BIGINT}, #{dateDay, mode=IN, jdbcType=DATE}, #{electricId, mode=IN, jdbcType=BIGINT})
    </select>

    <select id="getList"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppBiddingClearVO$ResolveLoad">
        SELECT
        t1.time_frame period,
        t1.electricity val
        FROM
        vpp_electric_actual_converge t1
        WHERE
        t1.is_delete = 0
        AND t1.date_day = #{dateDay}
        AND user_id = #{param.userId}
        <if test="startTime != null and endTime != null">
            AND time_frame &gt;= #{startTime}
            AND time_frame &lt;= #{endTime}
        </if>
        ORDER BY
        t1.time_frame
    </select>

    <select id="getUserRealityPower"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        SELECT
        *
        FROM
        vpp_electric_actual_converge t1
        LEFT JOIN vpp_base_period_detail t2 ON t2.ninety_six = t1.time_frame
        LEFT JOIN vpp_base_period_info t3 ON t2.vpp_id = t3.vpp_id
        WHERE
        t1.is_delete = 0
        AND t1.date_day &gt;= #{param.startDate}
        AND t1.date_day &lt;= #{param.endDate}
        <if test="startTime != null and endTime != null">
            AND time_frame &gt;= #{startTime}
            AND time_frame &lt;= #{endTime}
        </if>
        AND user_id = #{param.userId}
        AND t2.is_delete = 0
        AND t3.is_delete = 0
        AND t3.status = 2
        AND to_char( t3.use_date, 'yyyy-MM' ) = #{month}
        AND t2.use_date = #{param.startDate}
        AND t3.tenant_id = ( SELECT tenant_id FROM vpp_user WHERE ID = #{param.userId} )
    </select>
    <select id="selectElectricListByDateTimeUser"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualSimpleVO">
        SELECT sea.user_code,
        to_char(sea.date_day,'yyyy-mm-dd') date_day,
        sea.time_frame,
        SUM(sea.electricity) electricity
        FROM vpp_electric_actual sea
        INNER JOIN (SELECT DISTINCT user_code
        FROM vpp_user
        WHERE is_delete = 0
        <!--        <if test="tradingUnit != null">-->
        <!--            AND trading_unit = #{tradingUnit}-->
        <!--        </if>-->
        AND tenant_id = #{tenantId}
        <!--        <if test="minMonthDate != null">-->
        <!--            AND date_month &gt;= #{minMonthDate}-->
        <!--        </if>-->
        <!--        <if test="maxMonthDate != null">-->
        <!--            AND date_month &lt;= #{maxMonthDate}-->
        <!--        </if>-->
        ) su
        on sea.user_code = su.user_code
        ${ew.customSqlSegment}
    </select>

    <select id="select24ElectricListByDateTimeUser"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualSimpleVO">
        SELECT sea.user_code,
        to_char(sea.date_day,'yyyy-mm-dd') date_day,
        stc.twenty_four time_frame,
        SUM(sea.electricity) electricity
        FROM vpp_electric_actual sea
        INNER JOIN (SELECT DISTINCT user_code
        FROM vpp_user
        WHERE is_delete = 0
        <!--        <if test="tradingUnit != null">-->
        <!--            AND trading_unit = #{tradingUnit}-->
        <!--        </if>-->
        AND tenant_id = #{tenantId}
        <!--        <if test="minMonthDate != null">-->
        <!--            AND date_month &gt;= #{minMonthDate}-->
        <!--        </if>-->
        <!--        <if test="maxMonthDate != null">-->
        <!--            AND date_month &lt;= #{maxMonthDate}-->
        <!--        </if>-->
        ) su
        on sea.user_code = su.user_code
        INNER JOIN vpp_time_cycle stc ON sea.time_frame = stc.ninety_six
        ${ew.customSqlSegment}
    </select>

    <select id="electricActualList"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        SELECT date_day,
        time_frame,
        electricity
        FROM vpp_electric_actual_converge
        WHERE user_id = #{param.userId}
        AND is_delete = 0
        AND date_day &gt;= #{param.startDate}
        AND date_day &lt;= #{param.endDate}
        AND tenant_id = #{param.tenantId}
        <if test="startTime != null and endTime != null">
            AND time_frame &gt;= #{startTime}
            AND time_frame &lt;= #{endTime}
        </if>
    </select>

    <select id="getAllInfo" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.PieChartOfUserVO$RatioVO">
        SELECT t3.period_name AS name,
        Round(SUM(t3.electricity),3) AS num
        FROM (
        SELECT t1.NAME,
        t1.time_frame,
        SUM(t1.electricity) electricity,
        t2.period,
        t2.period_name
        FROM vpp_electric_actual_converge t1
        LEFT JOIN vpp_time_cycle t2 ON t1.time_frame = t2.ninety_six
        ${ew.customSqlSegment}
        AND t1.tenant_id = #{param.tenantId}
        AND t1.date_day &gt;= #{param.startTime}
        AND t1.date_day &lt;= #{param.endTime}
        AND t1.is_delete = 0
        <if test="userId != null">
            AND t1.user_id = #{userId}
        </if>
        GROUP BY t1.time_frame,
        t1.NAME,
        t2.period,
        t2.period_name
        ORDER BY t1.time_frame
        ) t3
        GROUP BY t3.period,
        t3.period_name
        ORDER BY t3.period
    </select>

    <select id="getAllPower" resultType="java.math.BigDecimal">
        SELECT Round(SUM(t3.electricity), 3) power
        FROM (SELECT t1.NAME,
                     t1.time_frame,
                     SUM(t1.electricity) electricity,
                     t2.period,
                     t2.period_name
              FROM vpp_electric_actual_converge t1
                       LEFT JOIN vpp_time_cycle t2 ON t1.time_frame = t2.ninety_six
                       ${ew.customSqlSegment}
                  AND t1.tenant_id = #{param.tenantId}
                  AND t1.date_day &gt;= #{param.startTime}
                  AND t1.date_day &lt;= #{param.endTime}
                  AND t1.is_delete = 0
              GROUP BY t1.time_frame,
                  t1.NAME,
                  t2.period,
                  t2.period_name
              ORDER BY t1.time_frame) t3
    </select>

    <select id="getElectricActual"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualForUserVO">
        SELECT time_frame,
               ROUND(AVG(electricity), 3) electricity
        FROM vpp_electric_actual_24
        WHERE user_id = #{userId}
          AND date_day &gt;= #{monthStart}
          AND date_day &lt;= #{monthEnd}
          AND is_delete = 0
        GROUP BY time_frame
        ORDER BY time_frame
    </select>

    <select id="electricActualAllList"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        WITH user_ids AS (
        SELECT id
        FROM unnest(
        ARRAY[
        <foreach collection='userId' item='id' open='' separator=',' close=''>
            #{id}
        </foreach>
        ]
        ) AS id
        )
        SELECT e.date_day,
        e.time_frame,
        e.electricity,
        e.user_id
        FROM vpp_electric_actual_converge e
        JOIN user_ids u ON e.user_id = u.id
        where e.is_delete = 0
        AND e.date_day &gt;= #{param.startDate}
        AND e.date_day &lt;= #{param.endDate}
        AND e.tenant_id = #{param.tenantId}
        <if test="startTime != null and endTime != null">
            AND e.time_frame &gt;= #{startTime}
            AND e.time_frame &lt;= #{endTime}
        </if>
        ORDER BY e.date_day,e.time_frame
    </select>


    <select id="electricActualAll"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.DividendVO$UserElectricSqlVO">
        SELECT t1.date_day,
               t1.time_frame,
               t1.electricity realElectricSum,
               t1.user_id
        FROM vpp_electric_actual_converge t1
        WHERE t1.is_delete = 0
          AND t1.date_day &gt;= #{param.startDate}
          AND t1.date_day &lt;= #{param.endDate}
          AND t1.tenant_id = #{param.tenantId}
        ORDER BY date_day, time_frame
    </select>


    <select id="getUpdateTime" resultType="string">
        SELECT date_day
        FROM vpp_electric_actual_converge
        where tenant_id = #{param.tenantId}
        GROUP BY date_day
        ORDER BY date_day DESC LIMIT 1
    </select>

    <select id="getListByTenantId"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppBiddingClearVO$ResolveLoad">
        SELECT t1.time_frame  period,
               t1.electricity val,
               t1.user_id     userId
        FROM vpp_electric_actual_converge t1
        WHERE t1.is_delete = 0
          AND t1.date_day = #{dateDay}
          AND tenant_id = #{tenantId}
        ORDER BY t1.time_frame
    </select>
    <select id="getActualTwentyFourForUser"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        SELECT
        user_id,
            time_frame,
               ROUND(SUM(electricity), 3) electricity
        FROM vpp_electric_actual_24
        WHERE
        user_id IN
        <foreach collection="userId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
          AND date_day &gt;= #{startDay}
          AND date_day &lt;= #{endDay}
          AND tenant_id = #{tenantId}
          AND is_delete = 0
        GROUP BY time_frame,user_id
        ORDER BY time_frame

    </select>

    <delete id="removeByParam">
        delete
        from vpp_electric_actual
        where tenant_id = #{tenantId}
          and date_day = #{dateDay}
    </delete>

    <delete id="remove24ByParam">
        delete
        from vpp_electric_actual_24
        where tenant_id = #{tenantId}
          and date_day = #{dateDay}
    </delete>
    <delete id="removeByIdList" >
        delete
        from vpp_electric_actual
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <!-- 插入24小时数据：从96点数据聚合到24点 -->
    <insert id="insert24HourData">
        INSERT INTO vpp_electric_actual_24 (user_id, tenant_id, electric_id, date_day, time_frame, electricity,
                                            is_delete)
        SELECT vea.user_id, #{tenantId}, vea.electric_id, #{dateDay}, vtc.twenty_four, SUM(vea.electricity), 0
        FROM vpp_electric_actual vea
                 INNER JOIN vpp_time_cycle vtc ON vea.time_frame = vtc.ninety_six
        WHERE vea.date_day = #{dateDay}
          AND vea.tenant_id = #{tenantId}
        GROUP BY vea.user_id, vea.electric_id, vtc.twenty_four
        ORDER BY vea.user_id, vtc.twenty_four
    </insert>

    <!-- 插入96点聚合数据：按用户和时点聚合实际用电量数据 -->
    <insert id="insert96PointConvergeData">
        INSERT INTO vpp_electric_actual_converge (user_id, tenant_id, date_day, time_frame, electricity)
        SELECT user_id, #{tenantId}, #{dateDay}, time_frame, SUM(electricity)
        FROM vpp_electric_actual
        WHERE date_day = #{dateDay}
          AND tenant_id = #{tenantId}
        GROUP BY user_id, time_frame
        ORDER BY user_id, time_frame
    </insert>

    <!-- 删除中位数预测数据 -->
    <delete id="deleteMiddlePredictionData">
        DELETE
        FROM vpp_electric_actual_middle
        WHERE tenant_id = #{tenantId}
          AND date_day = #{middleDay}
    </delete>

    <!-- 插入中位数预测数据 -->
    <insert id="insertMiddlePredictionData">
        INSERT INTO vpp_electric_actual_middle (user_id, tenant_id, date_day, time_frame, electricity)
        SELECT user_id,
               #{tenantId},
               #{middleDay},
               time_frame,
               PERCENTILE_DISC(0.5) WITHIN GROUP (ORDER BY electricity) as electricity
        FROM vpp_electric_actual_converge
        WHERE date_day &gt;= #{startDay}
          AND date_day &lt;= #{endDay}
        GROUP BY user_id, time_frame
        ORDER BY user_id, time_frame
    </insert>

    <select id="get96DataGroupByRegistered"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        SELECT
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            date_day as dateDay,
        </if>
        <if test="timeSharing == 4">
            substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)||'-01' as dateDay,
        </if>
        <if test="timeSharing == 5">
            substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)||'-01-01' as dateDay,
        </if>
        user_code as userCode,
        registered as registered,
        <if test="timeSharing == 1 or timeSharing == 2">
            time_frame as timeFrame,
        </if>
        ROUND(SUM(electricity), 3) electricity
        FROM vpp_electric_actual
        WHERE
        date_day &gt;= #{startDay}
        AND date_day &lt;= #{endDay}
        AND tenant_id = #{tenantId}
        AND is_delete = 0
        <if test="registeredList != null and registeredList.size() > 0">
            AND  registered IN
            <foreach collection="registeredList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY user_code,registered
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            ,date_day
        </if>
        <if test="timeSharing == 4">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)
        </if>
        <if test="timeSharing == 5">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)
        </if>
        <if test="timeSharing == 1 or timeSharing == 2">
            ,time_frame
        </if>
        ORDER BY user_code,registered
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            ,date_day
        </if>
        <if test="timeSharing == 4">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)
        </if>
        <if test="timeSharing == 5">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)
        </if>
        <if test="timeSharing == 1 or timeSharing == 2">
            ,time_frame
        </if>
    </select>

    <select id="get96DataGroupByTenantId"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO">
        SELECT
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            date_day as dateDay,
        </if>
        <if test="timeSharing == 4">
            substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)||'-01' as dateDay,
        </if>
        <if test="timeSharing == 5">
            substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)||'-01-01' as dateDay,
        </if>
        tenant_id as tenantId,
        <if test="timeSharing == 1 or timeSharing == 2">
            time_frame as timeFrame,
        </if>
        ROUND(SUM(electricity), 3) electricity
        FROM vpp_electric_actual
        WHERE
        date_day &gt;= #{startDay}
        AND date_day &lt;= #{endDay}
        AND tenant_id = #{tenantId}
        AND is_delete = 0
        GROUP BY tenant_id
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            ,date_day
        </if>
        <if test="timeSharing == 4">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)
        </if>
        <if test="timeSharing == 5">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)
        </if>
        <if test="timeSharing == 1 or timeSharing == 2">
            ,time_frame
        </if>
        ORDER BY tenant_id
        <if test="timeSharing == 1 or timeSharing == 2 or timeSharing == 3">
            ,date_day
        </if>
        <if test="timeSharing == 4">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 7)
        </if>
        <if test="timeSharing == 5">
            ,substring(to_char(date_day, 'YYYY-MM-DD') from 1 for 4)
        </if>
        <if test="timeSharing == 1 or timeSharing == 2">
            ,time_frame
        </if>
    </select>
</mapper>