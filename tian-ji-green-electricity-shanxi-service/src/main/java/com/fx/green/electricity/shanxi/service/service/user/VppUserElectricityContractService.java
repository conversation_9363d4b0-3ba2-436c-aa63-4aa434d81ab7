package com.fx.green.electricity.shanxi.service.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserElectricityContract;

import java.util.List;

/**
 * 用户电量合同 Service
 *
 * <AUTHOR>
 **/
public interface VppUserElectricityContractService extends IService<VppUserElectricityContract> {

    /**
     * 获取用户电量合同列表
     *
     * @param param 用户电量合同DTO
     * @return 用户电量合同列表
     */
    FxPage<VppUserElectricityContractVO> getUserElectricityContractPage(VppUserElectricityContractDTO param);

    /**
     * 修改用户电量合同
     *
     * @param param 用户电量合同DTO
     */
    void updateUserElectricityContract(VppUserElectricityContractSaveDTO param);

    /**
     * 导出用户电量合同数据
     *
     * @param param 用户电量合同DTO
     * @return 导出数据列表
     */
    List<VppUserElectricityContractExportVO> exportUserElectricityContract(VppUserElectricityContractDTO param);
}
