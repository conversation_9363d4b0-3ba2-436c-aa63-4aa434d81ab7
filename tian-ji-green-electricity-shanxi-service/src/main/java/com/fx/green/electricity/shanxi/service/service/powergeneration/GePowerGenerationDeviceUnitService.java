package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationDeviceUnit;

import java.util.List;
import java.util.Map;

/**
 * 发电设备和机组关联表 Service 接口
 *
 * <AUTHOR>
 */
public interface GePowerGenerationDeviceUnitService extends IService<GePowerGenerationDeviceUnit> {

    /**
     * 根据设备ID查询关联的机组列表
     *
     * @param deviceId 设备ID
     * @return 机组ID列表
     */
    List<Long> getUnitIdsByDeviceId(Long deviceId);

    /**
     * 根据机组ID查询关联的设备列表
     *
     * @param unitId 机组ID
     * @return 设备ID列表
     */
    List<Long> getDeviceIdsByUnitId(Long unitId);

    /**
     * 根据企业ID查询设备机组关联关系
     *
     * @param enterpriseId 企业ID
     * @return 设备机组关联关系列表
     */
    List<GePowerGenerationDeviceUnit> getByEnterpriseId(Long enterpriseId);

    /**
     * 批量保存设备机组关联关系
     *
     * @param deviceUnitList 设备机组关联关系列表
     */
    void batchSave(List<GePowerGenerationDeviceUnit> deviceUnitList);

    /**
     * 根据设备ID删除关联关系
     *
     * @param deviceId 设备ID
     */
    void deleteByDeviceId(Long deviceId);

    /**
     * 根据机组ID删除关联关系
     *
     * @param unitId 机组ID
     */
    void deleteByUnitId(Long unitId);

    /**
     * 批量查询设备关联的机组数量
     *
     * @param deviceIds 设备ID列表
     * @return 设备ID -> 机组数量的映射
     */
    Map<Long, Integer> getUnitCountsByDeviceIds(List<Long> deviceIds);

}
