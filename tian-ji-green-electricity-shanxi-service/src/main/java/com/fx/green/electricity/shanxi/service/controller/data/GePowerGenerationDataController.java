package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.service.service.data.GePowerGenerationDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 发电数据 Controller
 */
@RestController
@Api(tags = "数据维护 - 发电数据维护")
@ApiOperation("发电机组数据维护")
@RequestMapping("/gePowerGenerationData")
public class GePowerGenerationDataController {

    @Resource
    private GePowerGenerationDataService gePowerGenerationDataService;

    @ApiOperation("通过不同类型导入发电机组数据")
    @PostMapping("/importPowerGenerationData")
    public DataResult<ImportExcelVO> importPowerGenerationData(@RequestPart("file") MultipartFile file, @RequestParam("type") Integer type) {
        return DataResult.success(gePowerGenerationDataService.importPowerGenerationData(file, type));
    }
}
