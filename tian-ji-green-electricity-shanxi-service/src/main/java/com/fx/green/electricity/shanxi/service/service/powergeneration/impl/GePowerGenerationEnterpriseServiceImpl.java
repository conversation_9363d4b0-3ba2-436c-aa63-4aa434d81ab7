package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeContractMaintenance;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationEnterprise;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitDevice;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GePowerGenerationEnterpriseMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationEnterpriseService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitBasicService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发电企业信息 Service 实现类
 */
@Service
public class GePowerGenerationEnterpriseServiceImpl extends ServiceImpl<GePowerGenerationEnterpriseMapper, GePowerGenerationEnterprise> implements GePowerGenerationEnterpriseService {

    @Resource
    private GeUnitBasicService geUnitBasicService;

    @Resource
    private GeUnitDeviceService geUnitDeviceService;

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @Override
    public void createPowerGenerationEnterprise(GePowerGenerationEnterpriseDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

        // 1. 校验企业名称是否重复
        validateEnterpriseNameUnique(param.getName(), tenantId);

        // 2. 保存发电企业信息
        GePowerGenerationEnterprise powerGenerationEnterprise = new GePowerGenerationEnterprise();
        BeanUtil.copyProperties(param, powerGenerationEnterprise);
        baseMapper.insert(powerGenerationEnterprise);
    }

    /**
     * 校验企业名称是否重复
     *
     * @param name     企业名称
     * @param tenantId 租户ID
     */
    private void validateEnterpriseNameUnique(String name, Long tenantId) {
        LambdaQueryWrapper<GePowerGenerationEnterprise> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationEnterprise::getName, name);
        wrapper.eq(GePowerGenerationEnterprise::getTenantId, tenantId);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_ENTERPRISE_INSERT_REPEAT_ERROR.getMessage());
        }
    }

    @Override
    public void deletePowerGenerationEnterprise(IdDTO param) {
        // 1. 校验企业下是否存在机组
        validateUnitBasicExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 删除发电企业信息
        baseMapper.deleteById(param.getId());
    }

    /**
     * 校验企业下是否存在机组
     *
     * @param id       企业ID
     * @param tenantId 租户ID
     */
    private void validateUnitBasicExists(Long id, Long tenantId) {
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getTenantId, tenantId);
        wrapper.eq(GeUnitBasic::getEnterpriseId, id);
        Integer integer = geUnitBasicService.getBaseMapper().selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_ENTERPRISE_UNIT_DELETE_ERROR.getMessage());
        }
    }

    @Override
    public List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterprisePage(GePowerGenerationEnterpriseDTO param) {
        // 1. 查询发电企业信息
        LambdaQueryWrapper<GePowerGenerationEnterprise> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ObjectUtil.isNotEmpty(param.getName()), GePowerGenerationEnterprise::getName, param.getName());
        wrapper.orderByDesc(GePowerGenerationEnterprise::getUpdateTime);
        List<GePowerGenerationEnterprise> powerGenerationEnterprises = baseMapper.selectList(wrapper);

        if (ObjectUtil.isEmpty(powerGenerationEnterprises)) {
            return Collections.emptyList();
        }
        List<Long> ids = powerGenerationEnterprises.stream().map(GePowerGenerationEnterprise::getId).collect(Collectors.toList());

        // 2. 查询机组和设备信息
        LambdaQueryWrapper<GeUnitBasic> unitWrapper = new LambdaQueryWrapper<>();
        unitWrapper.in(GeUnitBasic::getEnterpriseId, ids);
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(unitWrapper);

        LambdaQueryWrapper<GeUnitDevice> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(GeUnitDevice::getEnterpriseId, ids);
        List<GeUnitDevice> geUnitDevices = geUnitDeviceService.getBaseMapper().selectList(deviceWrapper);

        // 3. 组装返回结果
        return powerGenerationEnterprises.stream().map(t -> {
            GePowerGenerationEnterpriseVO powerGenerationEnterpriseVO = new GePowerGenerationEnterpriseVO();
            BeanUtil.copyProperties(t, powerGenerationEnterpriseVO);
            powerGenerationEnterpriseVO.setUnitCount((int) geUnitBasics.stream().filter(p -> Objects.equals(p.getEnterpriseId(), t.getId())).count());
            powerGenerationEnterpriseVO.setDeviceCount((int) geUnitDevices.stream().filter(p -> Objects.equals(p.getEnterpriseId(), t.getId())).count());
            return powerGenerationEnterpriseVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseList() {
        // 1. 查询发电企业信息
        List<GePowerGenerationEnterprise> gePowerGenerationEnterprises = baseMapper.selectList(null);
        if (ObjectUtil.isEmpty(gePowerGenerationEnterprises)) {
            return Collections.emptyList();
        }

        // 2. 查询机组信息`
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeUnitBasic::getEnterpriseId, gePowerGenerationEnterprises.stream().map(GePowerGenerationEnterprise::getId).collect(Collectors.toList()));
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(wrapper);
        Map<Long, List<GeUnitBasicVO>> unitMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(gePowerGenerationEnterprises)) {
            unitMap = geUnitBasics.stream().map(t -> {
                GeUnitBasicVO geUnitBasicVO = new GeUnitBasicVO();
                BeanUtil.copyProperties(t, geUnitBasicVO);
                return geUnitBasicVO;
            }).collect(Collectors.groupingBy(GeUnitBasicVO::getEnterpriseId));
        }

        // 3. 组装返回结果
        List<GePowerGenerationEnterpriseVO> resultList = new ArrayList<>();
        for (GePowerGenerationEnterprise powerGenerationEnterprise : gePowerGenerationEnterprises) {
            GePowerGenerationEnterpriseVO powerGenerationEnterpriseVO = new GePowerGenerationEnterpriseVO();
            BeanUtil.copyProperties(powerGenerationEnterprise, powerGenerationEnterpriseVO);
            powerGenerationEnterpriseVO.setUnitBasicList(unitMap.getOrDefault(powerGenerationEnterprise.getId(), Collections.emptyList()));
            resultList.add(powerGenerationEnterpriseVO);
        }
        return resultList;
    }

    @Override
    public List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseListByIds(List<Long> enterpriseIds) {
        // 1. 查询发电企业信息
        LambdaQueryWrapper<GePowerGenerationEnterprise> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GePowerGenerationEnterprise::getId, enterpriseIds);
        List<GePowerGenerationEnterprise> gePowerGenerationEnterprises = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(gePowerGenerationEnterprises)) {
            return Collections.emptyList();
        }
        return gePowerGenerationEnterprises.stream().map(t -> {
            GePowerGenerationEnterpriseVO gePowerGenerationEnterpriseVO = new GePowerGenerationEnterpriseVO();
            BeanUtil.copyProperties(t, gePowerGenerationEnterpriseVO);
            return gePowerGenerationEnterpriseVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseListByName(String enterpriseName) {
        LambdaQueryWrapper<GePowerGenerationEnterprise> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(GePowerGenerationEnterprise::getName, enterpriseName);
        List<GePowerGenerationEnterprise> gePowerGenerationEnterprises = baseMapper.selectList(wrapper);
        return gePowerGenerationEnterprises.stream().map(t -> {
            GePowerGenerationEnterpriseVO gePowerGenerationEnterpriseVO = new GePowerGenerationEnterpriseVO();
            BeanUtil.copyProperties(t, gePowerGenerationEnterpriseVO);
            return gePowerGenerationEnterpriseVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GePowerGenerationEnterpriseUnitTreeVO> getEnterpriseUnitTree(GePowerGenerationEnterpriseUnitTreeDTO param) {
        // 1. 查询所有企业
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        LambdaQueryWrapper<GePowerGenerationEnterprise> enterpriseWrapper = new LambdaQueryWrapper<>();
        enterpriseWrapper.eq(GePowerGenerationEnterprise::getTenantId, tenantId);
        enterpriseWrapper.orderByDesc(GePowerGenerationEnterprise::getCreateTime);
        List<GePowerGenerationEnterprise> enterprises = list(enterpriseWrapper);

        if (ObjectUtil.isEmpty(enterprises)) {
            return Collections.emptyList();
        }

        // 2. 查询所有机组
        LambdaQueryWrapper<GeUnitBasic> unitWrapper = new LambdaQueryWrapper<>();
        unitWrapper.eq(GeUnitBasic::getTenantId, tenantId);
        unitWrapper.orderByDesc(GeUnitBasic::getCreateTime);
        List<GeUnitBasic> units = geUnitBasicService.list(unitWrapper);

        Map<Long, List<GeUnitBasic>> unitsByEnterprise = units.stream()
                .collect(Collectors.groupingBy(GeUnitBasic::getEnterpriseId));

        // 3. 查询所有机组关联的绿电直连合同
        LambdaQueryWrapper<GeContractMaintenance> contractWrapper = new LambdaQueryWrapper<>();
        contractWrapper.in(GeContractMaintenance::getUnitId, units.stream().map(GeUnitBasic::getId).collect(Collectors.toList()));
        List<GeContractMaintenance> contracts = geContractMaintenanceService.list(contractWrapper);

        Map<Long, List<GeContractMaintenance>> contractsByUnit = contracts.stream()
                .collect(Collectors.groupingBy(GeContractMaintenance::getUnitId));

        // 4. 构建企业机组树
        return enterprises.stream().map(enterprise -> {
            GePowerGenerationEnterpriseUnitTreeVO treeVO = new GePowerGenerationEnterpriseUnitTreeVO();
            treeVO.setId(enterprise.getId());
            treeVO.setName(enterprise.getName());

            // 获取该企业下的机组列表
            List<GeUnitBasic> enterpriseUnits = unitsByEnterprise.getOrDefault(enterprise.getId(), Collections.emptyList());
            List<GePowerGenerationEnterpriseUnitTreeVO.UnitInfo> unitInfoList = enterpriseUnits.stream()
                    .map(unit -> {
                        GePowerGenerationEnterpriseUnitTreeVO.UnitInfo unitInfo = new GePowerGenerationEnterpriseUnitTreeVO.UnitInfo();
                        unitInfo.setId(unit.getId());
                        unitInfo.setName(unit.getUnitName());
                        unitInfo.setIsBind(!contractsByUnit.getOrDefault(unit.getId(), Collections.emptyList()).isEmpty() ? 1 : 2);
                        return unitInfo;
                    })
                    // 根据 isBind 参数过滤机组
                    .filter(unitInfo -> {
                        if (param.getIsBind() == null || param.getIsBind() == 0) {
                            // 0:全部 或 null，不过滤
                            return true;
                        } else if (param.getIsBind() == 1) {
                            // 1:已绑定
                            return unitInfo.getIsBind() == 1;
                        } else if (param.getIsBind() == 2) {
                            // 2:未绑定
                            return unitInfo.getIsBind() == 2;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            treeVO.setUnitList(unitInfoList);
            return treeVO;
                })
                // 过滤掉没有机组的企业（当过滤条件导致某个企业下没有符合条件的机组时）
                .filter(treeVO -> !treeVO.getUnitList().isEmpty())
                .collect(Collectors.toList());
    }
}
