package com.fx.green.electricity.shanxi.service.controller.elemanage;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.service.elemanage.VppEleManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 **/
@RestController
@Api(tags = "用户用电管理")
@RequestMapping("vppEleManage")
public class VppEleManageController {

    @Resource
    private VppEleManageService vppEleManageService;

    @ApiOperation("获取虚拟电厂用户户号三层树状列表")
    @PostMapping("queryThreeFloorsTreeList")
    public DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(@RequestBody QueryUserTreeListDTO param) {
        return vppEleManageService.queryThreeFloorsTreeList(param);
    }

    @ApiOperation("获取数据列表")
    @PostMapping("queryEleManage")
    public DataResult<EleManageVO> queryEleManage(@RequestBody QueryEleManageDTO param) {
        param.setFlag(1);
        return vppEleManageService.queryEleManage(param);
    }

}
