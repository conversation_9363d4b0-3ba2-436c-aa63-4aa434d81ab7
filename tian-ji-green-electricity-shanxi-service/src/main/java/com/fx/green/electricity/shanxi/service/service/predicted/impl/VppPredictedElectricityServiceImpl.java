package com.fx.green.electricity.shanxi.service.service.predicted.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.data.gather.api.api.DataGatherVppApi;
import com.fx.data.gather.api.dto.unify.TimeDTO;
import com.fx.data.gather.api.dto.vpp.VppDateListDataQueryDTO;
import com.fx.data.gather.api.vo.vpp.PubVppDataVO;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.enums.DecimalPlaceEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricity;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricityConfirm;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedExcludeDay;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedReferenceDate;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.mapper.predicted.VppPredictedElectricityMapper;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityConfirmService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedExcludeDayService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedReferenceDateService;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionStatisticsService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import com.fx.green.electricity.shanxi.service.utils.DecimalPlaceUtil;
import com.fx.green.electricity.shanxi.service.utils.PointConvertUtil;
import com.fx.operation.api.api.OperationSystemApi;
import com.fx.operation.api.vo.SysDayInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.service.utils.MonthDaysUtil.getDecadeDays;


/**
 *
 **/
@Slf4j
@Service
public class VppPredictedElectricityServiceImpl extends ServiceImpl<VppPredictedElectricityMapper, VppPredictedElectricity> implements VppPredictedElectricityService {
    @Resource
    private OperationSystemApi operationSystemApi;


    @Resource
    private DataGatherVppApi dataGatherVppApi;
    @Resource
    private VppLoadUserService vppLoadUserService;
    @Resource
    private VppPredictedReferenceDateService vppPredictedReferenceDateService;
    @Resource
    private VppPredictedElectricityConfirmService vppPredictedElectricityConfirmService;

    @Resource
    private VppUserProductionStatisticsService vppUserProductionStatisticsService;

    @Resource
    private VppElectricService vppElectricService;
    @Resource
    private VppPredictedExcludeDayService vppPredictedExcludeDayService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PredictedElectricityVO> getPredictionElectricity(Date calDate,
                                                                 Long tenantId,
                                                                 Long userId,
                                                                 Integer type,
                                                                 List<String> predictionDayLists,
                                                                 Integer dimension) {
        return doPredictionLogic(calDate, tenantId, userId, type, predictionDayLists, dimension);
    }

    private List<PredictedElectricityVO> doPredictionLogic(Date calDate, Long tenantId,
                                                           Long userId,
                                                           Integer type,
                                                           List<String> predictionDayLists,
                                                           Integer dimension) {
        List<PredictedElectricityVO> predictedElectricityVOList = new ArrayList<>();
        List<VppPredictedReferenceDate> predictedReferenceDateList = new ArrayList<>();
        ExcessMLDeclareDateNumVO numVO = getDecadeDays(calDate);
        if (dimension.equals(2)) {
            //上旬
            List<String> firstDecadeRange = numVO.getFirstDecadeRange();
            calDate = DateUtil.parseDate(firstDecadeRange.get(0));
        } else if (dimension.equals(3)) {
            //中旬
            List<String> secondDecadeRange = numVO.getSecondDecadeRange();
            calDate = DateUtil.parseDate(secondDecadeRange.get(0));
        } else if (dimension.equals(4)) {
            //下旬
            List<String> thirdDecadeRange = numVO.getThirdDecadeRange();
            calDate = DateUtil.parseDate(thirdDecadeRange.get(0));
        } else if (dimension.equals(5)) {
            //月
            calDate = DateUtil.beginOfMonth(calDate);
        }


        //获取虚拟电厂下的全部用户
        List<VppUser> userListByVppId = vppLoadUserService.getListByTenantId(tenantId, "");
        if (ObjectUtil.isNotNull(userId)) {
            userListByVppId = userListByVppId.stream().filter(o -> o.getId().equals(userId)).collect(Collectors.toList());
        }
        //获取典型日期天数和预测天数
        List<GetDataTypeVO> getDataTypeVO = getList(calDate, tenantId, userListByVppId);
        Map<Long, List<GetDataTypeVO>> userDayTypeMap = getDataTypeVO.stream().collect(Collectors.groupingBy(GetDataTypeVO::getUserId));

        //获取全部的典型日和预测日 组装到一个list中
        List<Date> allDateDayList = new ArrayList<>();
        for (GetDataTypeVO dataTypeVO : getDataTypeVO) {
            if (ObjectUtil.isNotEmpty(predictionDayLists)) {
                dataTypeVO.setPredictionDayList(predictionDayLists);
            }
            List<String> predictionDayList = dataTypeVO.getPredictionDayList();
            List<String> typicalCurveDayList = dataTypeVO.getTypicalCurveDayList();
            if (ObjectUtil.isNotEmpty(predictionDayList)) {
                for (String dateDay : predictionDayList) {
                    DateTime dateTime = DateUtil.parseDate(dateDay);
                    allDateDayList.add(dateTime);
                }
            }
            if (ObjectUtil.isNotEmpty(typicalCurveDayList)) {
                for (String dateDay : typicalCurveDayList) {
                    DateTime dateTime = DateUtil.parseDate(dateDay);
                    allDateDayList.add(dateTime);
                }
            }
        }
        allDateDayList = allDateDayList.stream().distinct().collect(Collectors.toList());
        Collections.sort(allDateDayList);
        if (ObjectUtil.isNotEmpty(allDateDayList)) {
            //获取到全部的日期查询一次
            VppDateListDataQueryDTO vppDateListDataQueryDTO = new VppDateListDataQueryDTO();
            if (ObjectUtil.isNotNull(userId)) {
                String userCode = userListByVppId.get(0).getUserCode();
                vppDateListDataQueryDTO.setUserCode(userCode);
            }
            vppDateListDataQueryDTO.setDateList(allDateDayList);
            vppDateListDataQueryDTO.setTenantId(tenantId);
            DataResult<List<PubVppDataVO>> dataResult = dataGatherVppApi.queryUser15Electricity(vppDateListDataQueryDTO);
            System.out.println(dataResult.getData());
            for (VppUser vppUser : userListByVppId) {
                Long id = vppUser.getId();
                List<GetDataTypeVO> userDayTypeList = userDayTypeMap.get(id);
                if (ObjectUtil.isNotEmpty(userDayTypeList)) {
                    GetDataTypeVO getDataType = userDayTypeList.get(0);
                    List<String> allDateList = new ArrayList<>();
                    if (ObjectUtil.isNotNull(predictionDayLists) && ObjectUtil.isNotEmpty(predictionDayLists)) {
                        allDateList.addAll(predictionDayLists);
                        getDataType.setPredictionDayList(predictionDayLists);
                    }
                    if (ObjectUtil.isNotNull(getDataType)) {
                        allDateList.addAll(getDataType.getTypicalCurveDayList());
                    }

                    Collections.sort(allDateList);
                    //获取典型曲线天数的用电量
                    List<PubVppDataVO> data = dataResult.getData();
                    data = data.stream().filter(o -> o.getUserCode().equals(vppUser.getUserCode())).filter(o -> allDateList.contains(o.getDateStr())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(data)) {
                        //保存每个用户的预测日期
                        VppPredictedReferenceDate vppPredictedReferenceDate = new VppPredictedReferenceDate();
                        vppPredictedReferenceDate.setDateDay(calDate);
                        vppPredictedReferenceDate.setTenantId(tenantId);

                        List<String> typicalCurveDayList = getDataType.getTypicalCurveDayList();
                        if (ObjectUtil.isNotEmpty(typicalCurveDayList)) {
                            String typicalDay = null;
                            for (String s : typicalCurveDayList) {
                                if (ObjectUtil.isNull(typicalDay)) {
                                    typicalDay = s + ",";
                                } else {
                                    typicalDay += s + ",";
                                }
                            }
                            // 去掉最后一个逗号
                            if (ObjectUtil.isNotNull(typicalDay)) {
                                typicalDay = typicalDay.replaceAll(",$", "");
                                vppPredictedReferenceDate.setTypicalDay(typicalDay.toString());
                            }
                        }

                        List<String> predictionDayList = getDataType.getPredictionDayList();
                        if (ObjectUtil.isNotEmpty(predictionDayList)) {
                            String referenceDate = null;
                            for (String s : predictionDayList) {
                                if (ObjectUtil.isNull(referenceDate)) {
                                    referenceDate = s + ",";
                                } else {
                                    referenceDate += s + ",";
                                }
                            }
                            // 去掉最后一个逗号
                            if (ObjectUtil.isNotNull(referenceDate)) {
                                referenceDate = referenceDate.replaceAll(",$", "");
                                vppPredictedReferenceDate.setReferenceDate(referenceDate.toString());
                            }
                        }


                        vppPredictedReferenceDate.setUserId(vppUser.getId());
                        vppPredictedReferenceDate.setDimension(dimension);
                        vppPredictedReferenceDate.setName(vppUser.getName());
                        predictedReferenceDateList.add(vppPredictedReferenceDate);


                        List<PubVppDataVO> listData = data.stream().filter(o -> allDateList.contains(o.getDateStr())).collect(Collectors.toList());
                        //计算典型曲线电量总数
                        BigDecimal totalElectricity = listData.stream().map(PubVppDataVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (ObjectUtil.isNotNull(totalElectricity)) {
                            totalElectricity = totalElectricity.setScale(3, RoundingMode.HALF_UP);
                        }
                        //计算每个15点的比例
                        Map<String, List<PubVppDataVO>> timeMap = listData.stream().collect(Collectors.groupingBy(TimeDTO::getTimeFrame96));
                        BigDecimal ratioTotal = BigDecimal.ZERO;
                        Map<String, BigDecimal> ratioMap = new HashMap<>();
                        for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
                            String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
                            if (i == VppConstant.NINETY_SIX_TIMES.length - 1) {
                                ratioMap.put(ninetySixTime, DecimalPlaceUtil.sub(new BigDecimal(1), ratioTotal, DecimalPlaceEnum.COMPUTE));
                            } else {
                                List<PubVppDataVO> pubVppDataVOS = timeMap.get(ninetySixTime);
                                BigDecimal electricity = null;
                                if (ObjectUtil.isNotNull(pubVppDataVOS)) {
                                    electricity = pubVppDataVOS.stream().map(PubVppDataVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                                BigDecimal ratio = DecimalPlaceUtil.divide(electricity, totalElectricity, DecimalPlaceEnum.COMPUTE_30);
                                if (ObjectUtil.isNotNull(ratio)) {
                                    ratio = ratio.setScale(12, RoundingMode.HALF_UP);
                                }
                                ratioTotal = ratioTotal.add(ratio);
                                ratioMap.put(ninetySixTime, ratio);
                            }
                        }
                        //获取预测用电量7天的数据
                        List<PubVppDataVO> predictionData = data.stream().filter(o -> getDataType.getPredictionDayList().contains(o.getDateStr())).collect(Collectors.toList());
                        Map<String, List<PubVppDataVO>> collect = predictionData.stream().collect(Collectors.groupingBy(PubVppDataVO::getDateStr));
                        System.out.println(collect);
                        //获取每一天的总数 然后获取中位数
                        List<BigDecimal> allElectricityList = new ArrayList<>();
                        Map<String, List<PubVppDataVO>> dayMap = predictionData.stream().collect(Collectors.groupingBy(TimeDTO::getDateStr));
                        for (Map.Entry<String, List<PubVppDataVO>> entry : dayMap.entrySet()) {
                            List<PubVppDataVO> pubVppDataVOS = entry.getValue();
                            BigDecimal electricity = pubVppDataVOS.stream().map(PubVppDataVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (ObjectUtil.isNotNull(electricity)) {
                                electricity = electricity.setScale(3, RoundingMode.HALF_UP);
                            }
                            allElectricityList.add(electricity);
                        }
                        Collections.sort(allElectricityList);
                        // 计算中位数
                        if (ObjectUtil.isNotEmpty(allElectricityList)) {
                            BigDecimal median = calculateMedian(allElectricityList);
                            if (dimension.equals(2)) {
                                //上旬
                                Integer firstDecadeDays = numVO.getFirstDecadeDays();
                                median = DecimalPlaceUtil.multiply(median, new BigDecimal(firstDecadeDays), DecimalPlaceEnum.LOAD);
                            } else if (dimension.equals(3)) {
                                //中旬
                                Integer secondDecadeDays = numVO.getSecondDecadeDays();
                                median = DecimalPlaceUtil.multiply(median, new BigDecimal(secondDecadeDays), DecimalPlaceEnum.LOAD);
                            } else if (dimension.equals(4)) {
                                //下旬
                                Integer thirdDecadeDays = numVO.getThirdDecadeDays();
                                median = DecimalPlaceUtil.multiply(median, new BigDecimal(thirdDecadeDays), DecimalPlaceEnum.LOAD);
                            } else if (dimension.equals(5)) {
                                //月
                                Integer totalDays = numVO.getTotalDays();
                                median = DecimalPlaceUtil.multiply(median, new BigDecimal(totalDays), DecimalPlaceEnum.LOAD);
                            }

                            //开始计算每个点的预测用电量
                            BigDecimal allElectricity = BigDecimal.ZERO;
                            for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
                                PredictedElectricityVO predictedElectricityVO = new PredictedElectricityVO();
                                predictedElectricityVO.setUserId(vppUser.getId());
                                predictedElectricityVO.setName(vppUser.getName());
                                predictedElectricityVO.setTenantId(tenantId);
                                predictedElectricityVO.setDateDay(calDate);
                                String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
                                BigDecimal ratio = ratioMap.get(ninetySixTime);
                                predictedElectricityVO.setTimeFrame(ninetySixTime);
                                if (i == VppConstant.NINETY_SIX_TIMES.length - 1) {
                                    BigDecimal electricity = DecimalPlaceUtil.sub(median, allElectricity, DecimalPlaceEnum.ELECTRIC_QUANTITY);
                                    predictedElectricityVO.setElectricity(electricity);
                                } else {
                                    BigDecimal electricity = DecimalPlaceUtil.multiply(median, ratio, DecimalPlaceEnum.ELECTRIC_QUANTITY);
                                    allElectricity = allElectricity.add(electricity);
                                    predictedElectricityVO.setElectricity(electricity);
                                }
                                predictedElectricityVO.setDimension(dimension);
                                predictedElectricityVOList.add(predictedElectricityVO);
                            }
                        }
                    }
                }
            }
        }

        if (ObjectUtil.isNotNull(type) && type.equals(1)) {
            //入库保存
            List<VppPredictedElectricity> vppPredictedElectricityList = BeanCopyUtils.copyList(predictedElectricityVOList, VppPredictedElectricity.class);
            //判断当前日期是否有数据
            LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedElectricity::getDateDay, calDate);
            queryWrapper.eq(VppPredictedElectricity::getDimension, dimension);
            if (ObjectUtil.isNotNull(userId)) {
                queryWrapper.eq(VppPredictedElectricity::getUserId, userId);
            }
            baseMapper.delete(queryWrapper);
            saveBatch(vppPredictedElectricityList);
            //保存参考日数据
            if (ObjectUtil.isNotNull(userId)) {
                vppPredictedReferenceDateService.addData(predictedReferenceDateList, 2);
            } else {
                vppPredictedReferenceDateService.addData(predictedReferenceDateList, 1);
            }
        }
        return predictedElectricityVOList;
    }


    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(QueryTreeListDTO param) {
        VppLoadUserVO.TreeVO treeVO = new VppLoadUserVO.TreeVO();
        if (param.getType().equals(1)) {
            AdjustDeclareDTO.QueryUserTreeDTO queryUserTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
            queryUserTreeDTO.setTenantId(param.getTenantId());
            queryUserTreeDTO.setQueryDate(param.getDateDay());
            treeVO = vppLoadUserService.queryTreeList(queryUserTreeDTO);
        } else if (param.getType().equals(2)) {
            AdjustDeclareDTO.QueryUserTreeDTO queryUserTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
            queryUserTreeDTO.setTenantId(param.getTenantId());
            queryUserTreeDTO.setQueryDate(param.getDateDay());
            treeVO = vppLoadUserService.queryTreeAllList(queryUserTreeDTO);
        }
        if (ObjectUtil.isNotNull(param.getDimension())) {
            //查询用户是否修改了预测电量
            VppPredictedElectricityConfirmDTO vppPredictedElectricityConfirmDTO = new VppPredictedElectricityConfirmDTO();
            vppPredictedElectricityConfirmDTO.setDateDay(param.getDateDay());
            vppPredictedElectricityConfirmDTO.setTenantId(param.getTenantId());
            vppPredictedElectricityConfirmDTO.setDimension(param.getDimension());
            List<VppPredictedElectricityConfirm> list = vppPredictedElectricityConfirmService.getDataList(vppPredictedElectricityConfirmDTO);
            if (ObjectUtil.isNotEmpty(list)) {
                Map<Long, List<VppPredictedElectricityConfirm>> userMap = list.stream().collect(Collectors.groupingBy(VppPredictedElectricityConfirm::getUserId));
                for (VppLoadUserVO.TreeUserVO treeUserVO : treeVO.getTreeUserVOList()) {
                    Long id = treeUserVO.getId();
                    if (ObjectUtil.isNotNull(userMap.get(id))) {
                        treeUserVO.setIsUpdate(1);
                    } else {
                        treeUserVO.setIsUpdate(0);
                    }
                }
            }
        }
        return DataResult.success(treeVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(QueryPredictedElectricityDTO param) {
        QueryPredictedElectricityVO queryPredictedElectricityVO = new QueryPredictedElectricityVO();
        ExcessMLDeclareDateNumVO numVO = getDecadeDays(param.getQueryDate());
        if (param.getDimension().equals(3)) {
            //获取中旬数据
            List<String> secondDecadeRange = numVO.getSecondDecadeRange();
            Date calDate = DateUtil.parseDate(secondDecadeRange.get(0));
            param.setQueryDate(calDate);
        } else if (param.getDimension().equals(4)) {
            //获取下旬数据
            List<String> thirdDecadeRange = numVO.getThirdDecadeRange();
            Date calDate = DateUtil.parseDate(thirdDecadeRange.get(0));
            param.setQueryDate(calDate);
        }
        getDayData(param, queryPredictedElectricityVO);
        return DataResult.success(queryPredictedElectricityVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void getDayData(QueryPredictedElectricityDTO param, QueryPredictedElectricityVO queryPredictedElectricityVO) {
        List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList = new ArrayList<>();
        List<PredictedElectricityVO> predictionElectricity;
        if (param.getFlag().equals(1)) {
            predictionElectricity = getALLData(param);
        } else {
            predictionElectricity = getAllContrastData(param);
        }
        //获取编辑保存的预测电量
        VppPredictedElectricityConfirmDTO vppPredictedElectricityConfirmDTO = new VppPredictedElectricityConfirmDTO();
        vppPredictedElectricityConfirmDTO.setDateDay(param.getQueryDate());
        vppPredictedElectricityConfirmDTO.setTenantId(param.getTenantId());
        vppPredictedElectricityConfirmDTO.setDimension(param.getDimension());
        List<VppPredictedElectricityConfirm> vppPredictedElectricityConfirmList = vppPredictedElectricityConfirmService.getDataList(vppPredictedElectricityConfirmDTO);

        //组合数据
        queryPredictedElectricityVO.setTimeFrame(new ArrayList<>(Arrays.asList(VppConstant.NINETY_SIX_TIMES)));
        QueryTreeListDTO queryTreeListDTO = new QueryTreeListDTO();
        queryTreeListDTO.setType(param.getType());
        queryTreeListDTO.setDateDay(param.getQueryDate());
        queryTreeListDTO.setTenantId(param.getTenantId());
        DataResult<VppLoadUserVO.TreeVO> treeVODataResult = this.queryTreeListNew(queryTreeListDTO);
        VppLoadUserVO.TreeVO data = treeVODataResult.getData();
        if (ObjectUtil.isNotNull(data) && ObjectUtil.isNotNull(predictionElectricity)) {
            Map<String, List<VppLoadUserVO.TreeUserVO>> userMap = data.getTreeUserVOList().stream().collect(Collectors.groupingBy(VppLoadUserVO.TreeUserVO::getName));
            List<String> userNameList = userMap.keySet().stream().collect(Collectors.toList());
            predictionElectricity = predictionElectricity.stream().filter(o -> userNameList.contains(o.getName())).collect(Collectors.toList());

            //组装虚拟电厂的数据
            if (ObjectUtil.isNull(param.getUserId())) {
                getAllData( predictionElectricity, dataList, userMap, vppPredictedElectricityConfirmList);
            } else {
                getUserData(predictionElectricity, userMap, dataList, param.getUserId(), vppPredictedElectricityConfirmList);
            }

        }
        if (param.getTimeSharing().equals(2)) {
            queryPredictedElectricityVO.setTimeFrame(new ArrayList<>(Arrays.asList(VppConstant.TWENTY_FOUR_TIMES)));
            for (QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfo : dataList) {
                List<BigDecimal> electricityList = predictedElectricityInfo.getElectricityList();
                List<BigDecimal> predictedElectricityList = predictedElectricityInfo.getPredictedElectricityList();

                List<BigDecimal> list = PointConvertUtil.make96To24(electricityList, "SUM");
                List<BigDecimal> list2 = PointConvertUtil.make96To24(predictedElectricityList, "SUM");
                predictedElectricityInfo.setElectricityList(list);
                predictedElectricityInfo.setPredictedElectricityList(list2);
            }
            dataList = dataList.stream().sorted(Comparator.comparing(QueryPredictedElectricityVO.PredictedElectricityInfo::getType)).collect(Collectors.toList());
        } else {
            dataList = dataList.stream().sorted(Comparator.comparing(QueryPredictedElectricityVO.PredictedElectricityInfo::getType)).collect(Collectors.toList());
        }

        //计算总电量
        for (QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfo : dataList) {
            List<BigDecimal> electricityList = predictedElectricityInfo.getElectricityList();
            if (ObjectUtil.isNotNull(electricityList) && ObjectUtil.isNotEmpty(electricityList)) {
                BigDecimal reduce = electricityList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                electricityList.add(0, reduce);
                predictedElectricityInfo.setElectricityList(electricityList);
            }

            List<BigDecimal> predictedElectricityLists = predictedElectricityInfo.getPredictedElectricityList();
            if (ObjectUtil.isNotNull(predictedElectricityLists) && ObjectUtil.isNotEmpty(predictedElectricityLists)) {
                BigDecimal reduce = predictedElectricityLists.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                predictedElectricityLists.add(0, reduce);
                predictedElectricityInfo.setPredictedElectricityList(predictedElectricityLists);
            }

        }
        queryPredictedElectricityVO.setDataList(dataList);
    }

    private List<PredictedElectricityVO> getAllContrastData(QueryPredictedElectricityDTO param) {
        List<PredictedElectricityVO> predictionElectricity;
        if (ObjectUtil.isNull(param.getUserId())) {
            //获取虚拟电厂的数据（数据库获取）
            LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getQueryDate());
            queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
            queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
            List<VppPredictedElectricity> vppPredictedElectricityList = baseMapper.selectList(queryWrapper);
            predictionElectricity = BeanCopyUtils.copyList(vppPredictedElectricityList, PredictedElectricityVO.class);
        } else {
            LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getQueryDate());
            queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
            queryWrapper.eq(VppPredictedElectricity::getUserId, param.getUserId());
            queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
            List<VppPredictedElectricity> vppPredictedElectricity = baseMapper.selectList(queryWrapper);
            predictionElectricity = BeanCopyUtils.copyList(vppPredictedElectricity, PredictedElectricityVO.class);
        }
        return predictionElectricity;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<PredictedElectricityVO> getALLData(QueryPredictedElectricityDTO param) {
        List<PredictedElectricityVO> predictionElectricity;
        if (ObjectUtil.isNull(param.getUserId())) {
            //获取虚拟电厂的数据（数据库获取）
            LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getQueryDate());
            queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
            queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
            List<VppPredictedElectricity> vppPredictedElectricityList = baseMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(vppPredictedElectricityList)) {
                predictionElectricity = BeanCopyUtils.copyList(vppPredictedElectricityList, PredictedElectricityVO.class);
            } else {
                predictionElectricity = this.getPredictionElectricity(param.getQueryDate(), param.getTenantId(), param.getUserId(), 1, param.getReferenceDate(), param.getDimension());
            }
        } else {
            //获取用户的数据
            //判断参考日是否有值 无值去重新计算 有值判断参考日是否相同 不同的话重新去计算,相同取数据库数据
            if (ObjectUtil.isNotNull(param.getReferenceDate()) && ObjectUtil.isNotEmpty(param.getReferenceDate())) {
                QueryDateOneDTO queryDateOneDTO = new QueryDateOneDTO();
                queryDateOneDTO.setQueryDate(param.getQueryDate());
                queryDateOneDTO.setTenantId(param.getTenantId());
                queryDateOneDTO.setUserId(param.getUserId());
                queryDateOneDTO.setDimension(param.getDimension());
                VppPredictedReferenceDate vppPredictedReferenceDate = vppPredictedReferenceDateService.getData(queryDateOneDTO);
                if (ObjectUtil.isNotNull(vppPredictedReferenceDate)) {
                    String referenceDate = vppPredictedReferenceDate.getReferenceDate();
                    String[] split = referenceDate.split(",");
                    List<String> referenceDate1 = new ArrayList<>(Arrays.asList(split));
                    List<String> referenceDate2 = param.getReferenceDate();
                    if (referenceDate1.equals(referenceDate2)) {
                        LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getQueryDate());
                        queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
                        queryWrapper.eq(VppPredictedElectricity::getUserId, param.getUserId());
                        queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
                        List<VppPredictedElectricity> vppPredictedElectricity = baseMapper.selectList(queryWrapper);
                        predictionElectricity = BeanCopyUtils.copyList(vppPredictedElectricity, PredictedElectricityVO.class);
                    } else {
                        predictionElectricity = this.getPredictionElectricity(param.getQueryDate(), param.getTenantId(), param.getUserId(), 2, param.getReferenceDate(), param.getDimension());
                    }
                } else {
                    predictionElectricity = this.getPredictionElectricity(param.getQueryDate(), param.getTenantId(), param.getUserId(), 1, param.getReferenceDate(), param.getDimension());
                }
            } else {
                LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getQueryDate());
                queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
                queryWrapper.eq(VppPredictedElectricity::getUserId, param.getUserId());
                queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
                List<VppPredictedElectricity> vppPredictedElectricity = baseMapper.selectList(queryWrapper);
                if (ObjectUtil.isEmpty(vppPredictedElectricity)) {
                    predictionElectricity = this.getPredictionElectricity(param.getQueryDate(), param.getTenantId(), param.getUserId(), 1, param.getReferenceDate(), param.getDimension());
                } else {
                    predictionElectricity = BeanCopyUtils.copyList(vppPredictedElectricity, PredictedElectricityVO.class);
                }
            }
        }
        return predictionElectricity;
    }

    @Override
    public DataResult<VppPredictedReferenceDateVO> getReferenceDate(QueryDateOneDTO param) {
        VppPredictedReferenceDate vppPredictedReferenceDate = vppPredictedReferenceDateService.getData(param);
        return DataResult.success(BeanCopyUtils.copy(vppPredictedReferenceDate, VppPredictedReferenceDateVO.class));
    }

    @Override
    public DataResult<Void> saveOrUpdateElectricity(UpdatePredictedElectricityDTO param) {
        vppPredictedElectricityConfirmService.saveOrUpdateElectricity(param);
        return DataResult.success();
    }

    @Override
    public DataResult<Void> confirmPrediction(ConfirmPredictionDTO param) {
        //组装数据
        //入库保存
        List<VppPredictedElectricity> vppPredictedElectricityList = new ArrayList<>();
        for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
            String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
            VppPredictedElectricity vppPredictedElectricity = new VppPredictedElectricity();
            vppPredictedElectricity.setName(param.getName());
            vppPredictedElectricity.setDateDay(param.getDateDay());
            vppPredictedElectricity.setDimension(param.getDimension());
            vppPredictedElectricity.setTenantId(param.getTenantId());
            vppPredictedElectricity.setUserId(param.getUserId());
            vppPredictedElectricity.setTimeFrame(ninetySixTime);
            vppPredictedElectricity.setElectricity(param.getElectricityList().get(i));
            vppPredictedElectricityList.add(vppPredictedElectricity);
        }

        //判断当前日期是否有数据
        LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedElectricity::getDateDay, param.getDateDay());
        queryWrapper.eq(VppPredictedElectricity::getTenantId, param.getTenantId());
        queryWrapper.eq(VppPredictedElectricity::getUserId, param.getUserId());
        queryWrapper.eq(VppPredictedElectricity::getDimension, param.getDimension());
        baseMapper.delete(queryWrapper);
        saveBatch(vppPredictedElectricityList);
        //保存参考日数据
        List<VppPredictedReferenceDate> predictedReferenceDateList = new ArrayList<>();
        VppPredictedReferenceDate vppPredictedReferenceDate = new VppPredictedReferenceDate();
        vppPredictedReferenceDate.setName(param.getName());
        vppPredictedReferenceDate.setUserId(param.getUserId());
        vppPredictedReferenceDate.setReferenceDate(param.getPredictionDate());
        vppPredictedReferenceDate.setDateDay(param.getDateDay());
        vppPredictedReferenceDate.setTenantId(param.getTenantId());
        vppPredictedReferenceDate.setDimension(param.getDimension());
        predictedReferenceDateList.add(vppPredictedReferenceDate);
        vppPredictedReferenceDateService.addData(predictedReferenceDateList, 2);
        return DataResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> resetPrediction(ResetPredictedElectricityDTO param) {
        ExcessMLDeclareDateNumVO numVO = getDecadeDays(param.getDateDay());
        if (param.getDimension().equals(3)) {
            //获取中旬数据
            List<String> secondDecadeRange = numVO.getSecondDecadeRange();
            Date calDate = DateUtil.parseDate(secondDecadeRange.get(0));
            param.setDateDay(calDate);
        } else if (param.getDimension().equals(4)) {
            //获取下旬数据
            List<String> thirdDecadeRange = numVO.getThirdDecadeRange();
            Date calDate = DateUtil.parseDate(thirdDecadeRange.get(0));
            param.setDateDay(calDate);
        }
        this.getPredictionElectricity(param.getDateDay(), param.getTenantId(), param.getUserId(), 1, null, param.getDimension());
        return DataResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(QueryPredictedElectricityDTO param) {
        param.setFlag(2);
        DataResult<QueryPredictedElectricityVO> dataResult = queryPredictedElectricity(param);
        if (ObjectUtil.isNotNull(dataResult.getData())) {
            QueryPredictedElectricityVO data = dataResult.getData();
            QueryPredictedElectricityContrastVO queryPredictedElectricityContrast = BeanUtil.copyProperties(data, QueryPredictedElectricityContrastVO.class);

            List<VppUserDTO> vppUserDTOS = new ArrayList<>();
            if (param.getType().equals(1)) {
                List<VppUser> vppUserList = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
                //绑定周期判断
                vppUserDTOS = BeanCopyUtils.copyList(vppLoadUserService.getUserList(param.getQueryDate(), vppUserList), VppUserDTO.class);
            } else if (param.getType().equals(2)) {
                List<VppUser> vppUserList = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
                vppUserDTOS = BeanCopyUtils.copyList(vppUserList, VppUserDTO.class);
            }
            if (ObjectUtil.isNotNull(param.getUserId())) {
                vppUserDTOS = vppUserDTOS.stream().filter(o -> o.getId().equals(param.getUserId())).collect(Collectors.toList());
            }
            Map<Long, List<VppUserDTO>> userMap = vppUserDTOS.stream().collect(Collectors.groupingBy(VppUserDTO::getId));
            //获取全部用户实际用电量
            ExcessMLDeclareDateNumVO numVO = getDecadeDays(param.getQueryDate());
            Date stratTime = null;
            Date endTime = null;
            if (param.getDimension().equals(1)) {
                stratTime = param.getQueryDate();
                endTime = param.getQueryDate();
            }
            if (param.getDimension().equals(2)) {
                //获取中旬数据
                List<String> firstDecadeRange = numVO.getFirstDecadeRange();
                stratTime = DateUtil.parseDate(firstDecadeRange.get(0));
                endTime = DateUtil.parseDate(firstDecadeRange.get(firstDecadeRange.size() - 1));
            } else if (param.getDimension().equals(3)) {
                //获取下旬数据
                List<String> thirdDecadeRange = numVO.getSecondDecadeRange();
                stratTime = DateUtil.parseDate(thirdDecadeRange.get(0));
                endTime = DateUtil.parseDate(thirdDecadeRange.get(thirdDecadeRange.size() - 1));
            } else if (param.getDimension().equals(4)) {
                //获取下旬数据
                List<String> thirdDecadeRange = numVO.getThirdDecadeRange();
                stratTime = DateUtil.parseDate(thirdDecadeRange.get(0));
                endTime = DateUtil.parseDate(thirdDecadeRange.get(thirdDecadeRange.size() - 1));
            } else if (param.getDimension().equals(5)) {
                stratTime = DateUtil.beginOfMonth(param.getQueryDate());
                endTime = DateUtil.endOfMonth(param.getQueryDate());
            }
            if (ObjectUtil.isNull(stratTime) || ObjectUtil.isNull(endTime)) {
                return DataResult.failed("未找到查询时间");
            }
            Map<Long, Map<String, Map<String, BigDecimal>>> electricMap = vppElectricService.getElectricNewMap(
                    param.getTenantId(), vppUserDTOS, stratTime, endTime);
            List<VppElectricActualVO> electricActualList = new ArrayList<>();

            for (Map.Entry<Long, Map<String, Map<String, BigDecimal>>> entry : electricMap.entrySet()) {
                Long userId = entry.getKey();
                Map<String, Map<String, BigDecimal>> value = entry.getValue();
                for (Map.Entry<String, Map<String, BigDecimal>> mapEntry : value.entrySet()) {
                    String dateDay = mapEntry.getKey();
                    Map<String, BigDecimal> dateMap = mapEntry.getValue();
                    for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
                        VppElectricActualVO vo = new VppElectricActualVO();
                        BigDecimal electricity = dateMap.get(ninetySixTime);
                        if (ObjectUtil.isNotNull(electricity)) {
                            vo.setElectricity(electricity.setScale(VppConstant.THREE_NUMBER, RoundingMode.HALF_UP));
                        }
                        vo.setTimeFrame(ninetySixTime);
                        vo.setDateDay(dateDay);
                        vo.setUserId(userId);
                        String name = userMap.get(userId).get(0).getName();
                        vo.setName(name);
                        electricActualList.add(vo);
                    }
                }
            }


            List<QueryPredictedElectricityContrastVO.PredictedElectricityInfo> dataList = queryPredictedElectricityContrast.getDataList();
            if (ObjectUtil.isNotNull(dataList) && ObjectUtil.isNotEmpty(dataList)) {
                for (QueryPredictedElectricityContrastVO.PredictedElectricityInfo predictedElectricityInfo : dataList) {
                    Long type = predictedElectricityInfo.getType();
                    List<BigDecimal> realElectricityLists = new ArrayList<>();
                    if (type == 1) {
                        Map<String, List<VppElectricActualVO>> electricityMap = electricActualList.stream().collect(Collectors.groupingBy(o -> o.getTimeFrame()));
                        Map<String, List<VppElectricActualVO>> finalMap = new LinkedHashMap<>();
                        //map排序
                        electricityMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> finalMap.put(x.getKey(), x.getValue()));

                        for (Map.Entry<String, List<VppElectricActualVO>> entry : finalMap.entrySet()) {
                            List<VppElectricActualVO> electricActualVOS = entry.getValue();
                            Optional<BigDecimal> reduce = electricActualVOS.stream().map(VppElectricActualVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal::add);
                            reduce.ifPresent(realElectricityLists::add);
                        }
                    } else {
                        Map<Long, List<VppElectricActualVO>> electricityUserMap = electricActualList.stream().collect(Collectors.groupingBy(o -> o.getUserId()));
                        List<VppElectricActualVO> electricActualVOS = electricityUserMap.get(predictedElectricityInfo.getUserId());
                        Map<String, List<VppElectricActualVO>> electricityMap = electricActualVOS.stream().collect(Collectors.groupingBy(o -> o.getTimeFrame()));

                        Map<String, List<VppElectricActualVO>> finalMap = new LinkedHashMap<>();
                        //map排序
                        electricityMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> finalMap.put(x.getKey(), x.getValue()));
                        for (Map.Entry<String, List<VppElectricActualVO>> entry : finalMap.entrySet()) {
                            List<VppElectricActualVO> electricActualUser = entry.getValue();
                            Optional<BigDecimal> reduce = electricActualUser.stream().map(VppElectricActualVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal::add);
                            reduce.ifPresent(realElectricityLists::add);
                        }
                    }

                    if (param.getTimeSharing().equals(2)) {
                        List<BigDecimal> list = PointConvertUtil.make96To24(realElectricityLists, "SUM");
                        Optional<BigDecimal> totalElectricity = list.stream().reduce(BigDecimal::add);
                        totalElectricity.ifPresent(bigDecimal -> list.add(0, bigDecimal));
                        predictedElectricityInfo.setRealElectricityList(list);
                    } else {
                        Optional<BigDecimal> totalElectricity = realElectricityLists.stream().reduce(BigDecimal::add);
                        totalElectricity.ifPresent(bigDecimal -> realElectricityLists.add(0, bigDecimal));
                        predictedElectricityInfo.setRealElectricityList(realElectricityLists);
                    }

                    //虚拟电厂预测和实际用电量比例
                    List<BigDecimal> predictedRatioList = new ArrayList<>();
                    //修改预测电量和实际用电量比例
                    List<BigDecimal> electricityRatioList = new ArrayList<>();

                    if (ObjectUtil.isNotNull(predictedElectricityInfo.getPredictedElectricityList()) && ObjectUtil.isNotEmpty(predictedElectricityInfo.getPredictedElectricityList())
                            && ObjectUtil.isNotNull(predictedElectricityInfo.getRealElectricityList()) && ObjectUtil.isNotEmpty(predictedElectricityInfo.getRealElectricityList())) {
                        List<BigDecimal> predictedElectricityList = predictedElectricityInfo.getPredictedElectricityList();
                        List<BigDecimal> realElectricityList = predictedElectricityInfo.getRealElectricityList();
                        for (int i = 0; i < predictedElectricityList.size(); i++) {
                            BigDecimal bigDecimal = predictedElectricityList.get(i);
                            BigDecimal realElectricity = realElectricityList.get(i);
                            BigDecimal sub = Objects.requireNonNull(DecimalPlaceUtil.sub(bigDecimal, realElectricity, DecimalPlaceEnum.COMPUTE)).abs();
                            BigDecimal divide = DecimalPlaceUtil.divide(sub, realElectricityList.get(i), DecimalPlaceEnum.COMPUTE);
                            if (ObjectUtil.isNotNull(divide) && divide.compareTo(BigDecimal.ZERO) != 0) {
                                BigDecimal sub1 = DecimalPlaceUtil.sub(new BigDecimal("1"), divide, DecimalPlaceEnum.COMPUTE);
                                BigDecimal ratio = DecimalPlaceUtil.multiply(sub1, new BigDecimal("100"), DecimalPlaceEnum.RATIO);
                                predictedRatioList.add(ratio);
                            } else {
                                predictedRatioList.add(new BigDecimal("0.00"));
                            }
                        }
                    }

                    if (ObjectUtil.isNotNull(predictedElectricityInfo.getElectricityList()) && ObjectUtil.isNotEmpty(predictedElectricityInfo.getElectricityList())
                            && ObjectUtil.isNotNull(predictedElectricityInfo.getRealElectricityList()) && ObjectUtil.isNotEmpty(predictedElectricityInfo.getRealElectricityList())) {
                        List<BigDecimal> electricityList = predictedElectricityInfo.getElectricityList();
                        List<BigDecimal> realElectricityList = predictedElectricityInfo.getRealElectricityList();
                        for (int i = 0; i < electricityList.size(); i++) {
                            BigDecimal bigDecimal = electricityList.get(i);
                            BigDecimal realElectricity = realElectricityList.get(i);
                            BigDecimal sub = Objects.requireNonNull(DecimalPlaceUtil.sub(bigDecimal, realElectricity, DecimalPlaceEnum.COMPUTE)).abs();
                            BigDecimal divide = DecimalPlaceUtil.divide(sub, realElectricity, DecimalPlaceEnum.COMPUTE);
                            if (ObjectUtil.isNotNull(divide) && divide.compareTo(BigDecimal.ZERO) != 0) {
                                BigDecimal sub1 = DecimalPlaceUtil.sub(new BigDecimal("1"), divide, DecimalPlaceEnum.COMPUTE);
                                BigDecimal ratio = DecimalPlaceUtil.multiply(sub1, new BigDecimal("100"), DecimalPlaceEnum.RATIO);
                                electricityRatioList.add(ratio);
                            } else {
                                electricityRatioList.add(new BigDecimal("0.00"));
                            }
                        }
                    }
                    predictedElectricityInfo.setElectricityRatioList(electricityRatioList);
                    predictedElectricityInfo.setPredictedRatioList(predictedRatioList);
                }
            }
            queryPredictedElectricityContrast.setDataList(dataList);
            return DataResult.success(queryPredictedElectricityContrast);
        } else {
            return DataResult.success();
        }
    }

    @Override
    public DataResult<VppPredictedExcludeDateVO> getExcludeDay(QueryDateOneDTO param) {
        List<VppPredictedExcludeDay> dataListByUser = vppPredictedExcludeDayService.getDataListByUser(param);
        VppPredictedExcludeDateVO vppPredictedExcludeDateVO = new VppPredictedExcludeDateVO();
        vppPredictedExcludeDateVO.setTenantId(param.getTenantId());
        vppPredictedExcludeDateVO.setUserId(param.getUserId());
        List<String> dateDay = new ArrayList<>();
        for (VppPredictedExcludeDay vppPredictedExcludeDay : dataListByUser) {
            dateDay.add(DateUtil.formatDate(vppPredictedExcludeDay.getDateDay()));
        }
        vppPredictedExcludeDateVO.setDateDay(dateDay);
        return DataResult.success(vppPredictedExcludeDateVO);
    }


    @Override
    public void deleteData(Long userId, Date dateDay) {
        LambdaQueryWrapper<VppPredictedElectricity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedElectricity::getUserId, userId);
        queryWrapper.ge(VppPredictedElectricity::getDateDay, dateDay);
        baseMapper.delete(queryWrapper);
        vppPredictedElectricityConfirmService.deleteData(userId, dateDay);
        vppPredictedReferenceDateService.deleteData(userId, dateDay);
    }


    private void getUserData(List<PredictedElectricityVO> predictionElectricity,
                             Map<String, List<VppLoadUserVO.TreeUserVO>> userMap,
                             List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList,
                             Long userId,
                             List<VppPredictedElectricityConfirm> VppPredictedElectricityConfirmList) {

        Map<Long, List<VppPredictedElectricityConfirm>> userElectricitMap = VppPredictedElectricityConfirmList.stream().collect(Collectors.groupingBy(o -> o.getUserId()));


        IdDTO idDTO = new IdDTO();
        idDTO.setId(userId);
        VppUserInfoVO loadUser = vppLoadUserService.findById(idDTO);

        Map<String, List<PredictedElectricityVO>> userDataMap = predictionElectricity.stream().collect(Collectors.groupingBy(PredictedElectricityVO::getName));
        //组装用户的信息
        List<VppLoadUserVO.TreeUserVO> treeUserVOList = userMap.get(loadUser.getName());
        for (VppLoadUserVO.TreeUserVO treeUserVO : treeUserVOList) {
            QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfoUser = new QueryPredictedElectricityVO.PredictedElectricityInfo();
            List<BigDecimal> predictedElectricityListUser = new ArrayList<>();
            List<BigDecimal> bigDataPredictionListUser = new ArrayList<>();
            List<BigDecimal> electricityListUser = new ArrayList<>();

            predictedElectricityInfoUser.setType(treeUserVO.getId());
            predictedElectricityInfoUser.setName(treeUserVO.getName());
            predictedElectricityInfoUser.setUserId(treeUserVO.getId());

            String name = treeUserVO.getName();
            if (ObjectUtil.isNotNull(userDataMap.get(name))) {
                List<PredictedElectricityVO> predictedElectricityVOList = userDataMap.get(name);
                predictedElectricityVOList = predictedElectricityVOList.stream().sorted(Comparator.comparing(PredictedElectricityVO::getTimeFrame)).collect(Collectors.toList());
                for (PredictedElectricityVO predictedElectricityVO : predictedElectricityVOList) {
                    predictedElectricityListUser.add(predictedElectricityVO.getElectricity());

                    if (ObjectUtil.isNotNull(userElectricitMap.get(treeUserVO.getId()))) {
                        List<VppPredictedElectricityConfirm> vppPredictedElectricityConfirms = userElectricitMap.get(treeUserVO.getId());
                        List<VppPredictedElectricityConfirm> collect = vppPredictedElectricityConfirms.stream().filter(o -> o.getTimeFrame().equals(predictedElectricityVO.getTimeFrame())).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(collect)) {
                            BigDecimal electricity = collect.get(0).getElectricity();
                            if (ObjectUtil.isNotNull(electricity)) {
                                electricityListUser.add(electricity.setScale(3, RoundingMode.HALF_UP));
                            }
                        }
                    } else {
                        electricityListUser.add(predictedElectricityVO.getElectricity());
                    }
                }
            } else {
                if (ObjectUtil.isNotNull(userElectricitMap.get(treeUserVO.getId()))) {
                    List<VppPredictedElectricityConfirm> vppPredictedElectricityConfirms = userElectricitMap.get(treeUserVO.getId());
                    for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
                        List<VppPredictedElectricityConfirm> collect = vppPredictedElectricityConfirms.stream().filter(o -> o.getTimeFrame().equals(ninetySixTime)).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(collect)) {
                            BigDecimal electricity = collect.get(0).getElectricity();
                            if (ObjectUtil.isNotNull(electricity)) {
                                electricityListUser.add(electricity.setScale(3, RoundingMode.HALF_UP));
                            }
                        }
                    }
                }
            }
            predictedElectricityInfoUser.setPredictedElectricityList(predictedElectricityListUser);
            predictedElectricityInfoUser.setElectricityList(electricityListUser);
            dataList.add(predictedElectricityInfoUser);
        }
    }


    private static void getAllData(
                                   List<PredictedElectricityVO> predictionElectricity,
                                   List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList,
                                   Map<String, List<VppLoadUserVO.TreeUserVO>> userMap,
                                   List<VppPredictedElectricityConfirm> vppPredictedElectricityConfirmList) {

        // 按用户ID分组
        Map<Long, List<VppPredictedElectricityConfirm>> userElectricityMap = vppPredictedElectricityConfirmList.stream()
                .collect(Collectors.groupingBy(VppPredictedElectricityConfirm::getUserId));

        // 创建虚拟预测电力信息
        QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfoVir = new QueryPredictedElectricityVO.PredictedElectricityInfo();
        predictedElectricityInfoVir.setType(1L);
        predictedElectricityInfoVir.setName("虚拟电厂");

        // 按时间段分组
        Map<String, List<PredictedElectricityVO>> userDataTimeFrameMap = predictionElectricity.stream()
                .collect(Collectors.groupingBy(PredictedElectricityVO::getTimeFrame));

        // 计算虚拟预测电力列表
        List<BigDecimal> predictedElectricityList = new ArrayList<>();
        for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
            List<PredictedElectricityVO> predictedElectricityVOList = userDataTimeFrameMap.get(ninetySixTime);
            if (ObjectUtil.isNotEmpty(predictedElectricityVOList)) {
                BigDecimal electricity = predictedElectricityVOList.stream().map(PredictedElectricityVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                predictedElectricityList.add(electricity);
            }
        }
        predictedElectricityInfoVir.setPredictedElectricityList(predictedElectricityList);

        // 按名称分组
        Map<String, List<PredictedElectricityVO>> userDataMap = predictionElectricity.stream()
                .collect(Collectors.groupingBy(PredictedElectricityVO::getName));

        // 组装用户信息
        assembleUserData(userMap, userDataMap, userElectricityMap, dataList);

        // 计算所有用户的预测电力数据
        Map<String, BigDecimal> calMap = calculateAllUserElectricity(dataList);

        // 设置虚拟电力列表
        predictedElectricityInfoVir.setElectricityList(new ArrayList<>(calMap.values()));
        dataList.add(predictedElectricityInfoVir);
    }


    private static void assembleUserData(Map<String, List<VppLoadUserVO.TreeUserVO>> userMap,
                                         Map<String, List<PredictedElectricityVO>> userDataMap,
                                         Map<Long, List<VppPredictedElectricityConfirm>> userElectricityMap,
                                         List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList) {
        userMap.forEach((key, userVOList) -> {
            QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfoUser = new QueryPredictedElectricityVO.PredictedElectricityInfo();
            List<PredictedElectricityVO> predictedElectricityVOList = userDataMap.get(key);

            if (ObjectUtil.isNotEmpty(predictedElectricityVOList)) {
                predictedElectricityVOList.sort(Comparator.comparing(PredictedElectricityVO::getTimeFrame));
                List<BigDecimal> predictedElectricityListUser = new ArrayList<>();
                List<BigDecimal> electricityListUser = new ArrayList<>();

                if (ObjectUtil.isNotEmpty(predictedElectricityVOList)) {
                    predictedElectricityVOList.forEach(predictedElectricityVO -> {
                        predictedElectricityListUser.add(predictedElectricityVO.getElectricity());
                        BigDecimal electricity = getConfirmedElectricity(userElectricityMap, userVOList.get(0).getId(), predictedElectricityVO.getTimeFrame());
                        electricityListUser.add(electricity != null ? electricity.setScale(3, RoundingMode.HALF_UP) : predictedElectricityVO.getElectricity());
                    });
                }
                predictedElectricityInfoUser.setType(userVOList.get(0).getId());
                predictedElectricityInfoUser.setName(key);
                predictedElectricityInfoUser.setUserId(userVOList.get(0).getId());
                predictedElectricityInfoUser.setPredictedElectricityList(predictedElectricityListUser);
                predictedElectricityInfoUser.setElectricityList(electricityListUser);
            } else {
                List<BigDecimal> electricityListUser = new ArrayList<>();
                VppLoadUserVO.TreeUserVO treeUserVO = userVOList.get(0);
                predictedElectricityInfoUser.setType(treeUserVO.getId());
                predictedElectricityInfoUser.setName(treeUserVO.getName());
                predictedElectricityInfoUser.setUserId(treeUserVO.getId());
                if (ObjectUtil.isNotNull(userElectricityMap.get(treeUserVO.getId()))) {
                    for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
                        BigDecimal electricity = getConfirmedElectricity(userElectricityMap, treeUserVO.getId(), ninetySixTime);
                        electricityListUser.add(electricity.setScale(3, RoundingMode.HALF_UP));
                    }
                    predictedElectricityInfoUser.setElectricityList(electricityListUser);
                }
            }
            dataList.add(predictedElectricityInfoUser);
        });
    }

    private static BigDecimal getConfirmedElectricity
            (Map<Long, List<VppPredictedElectricityConfirm>> userElectricityMap, Long userId, String timeFrame) {
        return Optional.ofNullable(userElectricityMap.get(userId))
                .flatMap(list -> list.stream()
                        .filter(o -> o.getTimeFrame().equals(timeFrame))
                        .findFirst()
                        .map(VppPredictedElectricityConfirm::getElectricity))
                .orElse(null);
    }

    private static Map<String, BigDecimal> calculateAllUserElectricity
            (List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList) {
        Map<String, BigDecimal> calMap = new LinkedHashMap<>();
        dataList.stream()
                .filter(o -> o.getType() != 1)
                .forEach(allUserDatum -> {
                    if (ObjectUtil.isNotNull(allUserDatum.getElectricityList())) {
                        for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
                            String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
                            BigDecimal electricity = allUserDatum.getElectricityList().get(i);
                            calMap.merge(ninetySixTime, electricity, DecimalPlaceUtil::add);
                        }
                    }
                });
        return calMap;
    }

//    private static void getAllData(VppBase vppBase,
//                                   List<PredictedElectricityVO> predictionElectricity,
//                                   List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList,
//                                   Map<String, List<VppLoadUserVO.TreeUserVO>> userMap,
//                                   List<VppPredictedElectricityConfirm> VppPredictedElectricityConfirmList) {
//
//        Map<Long, List<VppPredictedElectricityConfirm>> userElectricitMap = VppPredictedElectricityConfirmList.stream().collect(Collectors.groupingBy(o -> o.getUserId()));
//
//
//        QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfoVir = new QueryPredictedElectricityVO.PredictedElectricityInfo();
//        predictedElectricityInfoVir.setType(1L);
//        predictedElectricityInfoVir.setName(vppBase.getName());
//        List<BigDecimal> predictedElectricityList = new ArrayList<>();
//        List<BigDecimal> bigDataPredictionList = new ArrayList<>();
//        List<BigDecimal> electricityList = new ArrayList<>();
//        Map<String, List<PredictedElectricityVO>> userDataTimeFrameMap = predictionElectricity.stream().collect(Collectors.groupingBy(PredictedElectricityVO::getTimeFrame));
//        for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
//            List<PredictedElectricityVO> predictedElectricityVOList = userDataTimeFrameMap.get(ninetySixTime);
//            BigDecimal electricity = predictedElectricityVOList.stream().map(PredictedElectricityVO::getElectricity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//            predictedElectricityList.add(electricity);
//        }
//        predictedElectricityInfoVir.setPredictedElectricityList(predictedElectricityList);
//
//
//        Map<String, List<PredictedElectricityVO>> userDataMap = predictionElectricity.stream().collect(Collectors.groupingBy(PredictedElectricityVO::getName));
//        //组装用户的信息
//        for (Map.Entry<String, List<VppLoadUserVO.TreeUserVO>> entry : userMap.entrySet()) {
//            QueryPredictedElectricityVO.PredictedElectricityInfo predictedElectricityInfoUser = new QueryPredictedElectricityVO.PredictedElectricityInfo();
//            List<BigDecimal> predictedElectricityListUser = new ArrayList<>();
//            List<BigDecimal> bigDataPredictionListUser = new ArrayList<>();
//            List<BigDecimal> electricityListUser = new ArrayList<>();
//
//            String key = entry.getKey();
//            List<VppLoadUserVO.TreeUserVO> userVOList = entry.getValue();
//            if (ObjectUtil.isNotEmpty(userDataMap.get(key))) {
//                List<PredictedElectricityVO> predictedElectricityVOList = userDataMap.get(key);
//                predictedElectricityVOList = predictedElectricityVOList.stream().sorted(Comparator.comparing(PredictedElectricityVO::getTimeFrame)).collect(Collectors.toList());
//                for (PredictedElectricityVO predictedElectricityVO : predictedElectricityVOList) {
//                    predictedElectricityListUser.add(predictedElectricityVO.getElectricity());
//                    if (ObjectUtil.isNotNull(userElectricitMap.get(userVOList.get(0).getId()))) {
//                        List<VppPredictedElectricityConfirm> vppPredictedElectricityConfirms = userElectricitMap.get(userVOList.get(0).getId());
//                        List<VppPredictedElectricityConfirm> collect = vppPredictedElectricityConfirms.stream().filter(o -> o.getTimeFrame().equals(predictedElectricityVO.getTimeFrame())).collect(Collectors.toList());
//                        if (ObjectUtil.isNotEmpty(collect)) {
//                            BigDecimal electricity = collect.get(0).getElectricity();
//                            if (ObjectUtil.isNotNull(electricity)) {
//                                electricityListUser.add(electricity.setScale(3, RoundingMode.HALF_UP));
//                            }
//                        }
//                    } else {
//                        electricityListUser.add(predictedElectricityVO.getElectricity());
//                    }
//                }
//                predictedElectricityInfoUser.setType(userVOList.get(0).getId());
//                predictedElectricityInfoUser.setName(key);
//            } else {
//                VppLoadUserVO.TreeUserVO treeUserVO = userVOList.get(0);
//                predictedElectricityInfoUser.setType(treeUserVO.getId());
//                predictedElectricityInfoUser.setName(treeUserVO.getName());
//            }
//            predictedElectricityInfoUser.setPredictedElectricityList(predictedElectricityListUser);
//            predictedElectricityInfoUser.setElectricityList(electricityListUser);
//            dataList.add(predictedElectricityInfoUser);
//        }
//
//        List<QueryPredictedElectricityVO.PredictedElectricityInfo> allUserData = dataList.stream().filter(o -> o.getType() != 1).collect(Collectors.toList());
//
//        Map<String, BigDecimal> calMap = new LinkedHashMap<>();
//        for (QueryPredictedElectricityVO.PredictedElectricityInfo allUserDatum : allUserData) {
//            if (ObjectUtil.isNotNull(allUserDatum.getElectricityList())) {
//                for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
//                    String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
//                    BigDecimal electricity = allUserDatum.getElectricityList().get(i);
//                    if (ObjectUtil.isNotNull(calMap.get(ninetySixTime)) && ObjectUtil.isNotNull(electricity)) {
//                        calMap.put(ninetySixTime, electricity);
//                    } else {
//                        DecimalPlaceUtil.add(calMap.get(ninetySixTime), electricity);
//                    }
//                }
//            }
//        }
//        predictedElectricityInfoVir.setElectricityList(new ArrayList<>(calMap.values()));
//        dataList.add(predictedElectricityInfoVir);
//    }


//    private GetDataTypeVO getList(Date date, Long tenantId, List<VppUser> userListByVppId) {
//        GetDataTypeVO getDataTypeVO = new GetDataTypeVO();
//        //获取当前日期这一年的全部日期
//
//        List<SysDayInfoVO> allDate = new ArrayList<>();
//        DateTime dateTime = DateUtil.offsetDay(date, -180);
//        if (DateUtil.format(dateTime, "yyyyMM").equals(DateUtil.format(date, "yyyyMM"))) {
//            DataResult<List<SysDayInfoVO>> allDates = operationSystemApi.getAllDate(DateUtil.formatDate(date));
//            allDate.addAll(allDates.getData());
//        } else {
//            DataResult<List<SysDayInfoVO>> startDate = operationSystemApi.getAllDate(DateUtil.formatDate(date));
//            DataResult<List<SysDayInfoVO>> endDate = operationSystemApi.getAllDate(DateUtil.formatDate(dateTime));
//            allDate.addAll(startDate.getData());
//            allDate.addAll(endDate.getData());
//        }
//
//        for (VppUser vppUser : userListByVppId) {
//
//        }
//
//        if (ObjectUtil.isNotNull(allDate)) {
//            List<SysDayInfoVO> data = allDate;
//            List<String> weekday = new ArrayList<>();
//            List<String> restDay = new ArrayList<>();
//            List<String> redLetterDay = new ArrayList<>();
//            for (SysDayInfoVO sysDayInfoVO : data) {
//                //判断是工作日还是休息日
//                if (sysDayInfoVO.getType() == 1 || sysDayInfoVO.getType() == 3) {
//                    weekday.add(sysDayInfoVO.getDay());
//                } else if (sysDayInfoVO.getType() == 2) {
//                    redLetterDay.add(sysDayInfoVO.getDay());
//                } else {
//                    restDay.add(sysDayInfoVO.getDay());
//                }
//            }
//            Collections.sort(weekday);
//            Collections.sort(restDay);
//            List<String> typicalCurveDayList = new ArrayList<>();
//            List<String> predictionDay = new ArrayList<>();
//            //判断当前日期是工作日还是非工作日还是节假日
//            if (weekday.contains(DateUtil.formatDate(date))) {
//                assignment(date, weekday, typicalCurveDayList, predictionDay, tenantId,null);
//            } else if (restDay.contains(DateUtil.formatDate(date))) {
//                assignment(date, restDay, typicalCurveDayList, predictionDay, tenantId,null);
//            } else if (redLetterDay.contains(DateUtil.formatDate(date))) {
//                assignment(date, redLetterDay, typicalCurveDayList, predictionDay, tenantId,null);
//            }
//            getDataTypeVO.setPredictionDayList(predictionDay);
//            getDataTypeVO.setTypicalCurveDayList(typicalCurveDayList);
//            return getDataTypeVO;
//        } else {
//            return getDataTypeVO;
//        }
//    }

    private List<GetDataTypeVO> getList(Date date, Long tenantId, List<VppUser> userListByVppId) {
        List<GetDataTypeVO> list = new ArrayList<>();
        //获取当前日期这一年的全部日期

        List<SysDayInfoVO> allDate = new ArrayList<>();
        DateTime dateTime = DateUtil.offsetDay(date, -180);
        if (DateUtil.format(dateTime, "yyyy").equals(DateUtil.format(date, "yyyy"))) {
            DataResult<List<SysDayInfoVO>> allDates = operationSystemApi.getAllDate(DateUtil.formatDate(date));
            allDate.addAll(allDates.getData());
        } else {
            DataResult<List<SysDayInfoVO>> startDate = operationSystemApi.getAllDate(DateUtil.formatDate(date));
            DataResult<List<SysDayInfoVO>> endDate = operationSystemApi.getAllDate(DateUtil.formatDate(dateTime));
            allDate.addAll(startDate.getData());
            allDate.addAll(endDate.getData());
        }
        List<String> weekday = new ArrayList<>();
        List<String> restDay = new ArrayList<>();
        List<String> redLetterDay = new ArrayList<>();
        //单休工作日
        List<String> singleDay = new ArrayList<>();
        //单休休息日
        List<String> singleRestDay = new ArrayList<>();

        log.info("运营平台获取的时间{}", JSON.toJSONString(allDate));
        if (ObjectUtil.isNotNull(allDate)) {
            for (SysDayInfoVO sysDayInfoVO : allDate) {
                //判断是工作日还是休息日
                if (sysDayInfoVO.getType() == 1 || sysDayInfoVO.getType() == 3) {
                    weekday.add(sysDayInfoVO.getDay());
                } else if (sysDayInfoVO.getType() == 2) {
                    redLetterDay.add(sysDayInfoVO.getDay());
                } else {
                    restDay.add(sysDayInfoVO.getDay());
                }
            }
            for (SysDayInfoVO sysDayInfoVO : allDate) {
                //判断是工作日还是休息日
                if (sysDayInfoVO.getType() == 1 || sysDayInfoVO.getType() == 3 || sysDayInfoVO.getType() == 4) {
                    singleDay.add(sysDayInfoVO.getDay());
                } else if (sysDayInfoVO.getType() == 5) {
                    singleRestDay.add(sysDayInfoVO.getDay());
                }
            }

            Collections.sort(weekday);
            Collections.sort(restDay);
            Collections.sort(redLetterDay);
            Collections.sort(singleDay);
            Collections.sort(singleRestDay);
        }
        weekday = weekday.stream().distinct().collect(Collectors.toList());
        restDay = restDay.stream().distinct().collect(Collectors.toList());
        redLetterDay = redLetterDay.stream().distinct().collect(Collectors.toList());
        singleDay = singleDay.stream().distinct().collect(Collectors.toList());
        singleRestDay = singleRestDay.stream().distinct().collect(Collectors.toList());

        log.info("工作日{}", JSON.toJSONString(weekday));
        log.info("非工作日{}", JSON.toJSONString(restDay));
        log.info("假期{}", JSON.toJSONString(redLetterDay));
        log.info("单休工作日{}", JSON.toJSONString(singleDay));
        log.info("单休休息日{}", JSON.toJSONString(singleRestDay));

        IdDTO idDTO = new IdDTO();
        idDTO.setTenantId(tenantId);
        List<VppUserProductionStatusVO> userProductionStatus = vppUserProductionStatisticsService.getUserProductionStatusByTenantId(idDTO);
        List<VppPredictedExcludeDay> excludeDayList = vppPredictedExcludeDayService.getDataList(tenantId);
        if (ObjectUtil.isNotEmpty(userProductionStatus)) {
            for (VppUser vppUser : userListByVppId) {
                List<VppUserProductionStatusVO> userProductionStatusList = userProductionStatus.stream().filter(o -> o.getUserId().equals(vppUser.getId())).collect(Collectors.toList());
                List<VppPredictedExcludeDay> excludeDayUserList = excludeDayList.stream().filter(o -> o.getUserId().equals(vppUser.getId())).collect(Collectors.toList());
                GetDataTypeVO getDataTypeVO = new GetDataTypeVO();
                getDataTypeVO.setName(vppUser.getName());
                getDataTypeVO.setUserId(vppUser.getId());
                List<String> typicalCurveDayList = new ArrayList<>();
                List<String> predictionDay = new ArrayList<>();
                //判断当前日期是工作日还是非工作日还是节假日
                if (vppUser.getName().contains("鸿富晋精密工业（太原）有限公司") || vppUser.getName().equals("富联科技（晋城）有限公司")) {
                    if (singleDay.contains(DateUtil.formatDate(date))) {
                        assignment(date, singleDay, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    } else if (singleRestDay.contains(DateUtil.formatDate(date))) {
                        assignment(date, singleRestDay, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    } else if (redLetterDay.contains(DateUtil.formatDate(date))) {
                        assignment(date, redLetterDay, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    }
                } else {
                    if (weekday.contains(DateUtil.formatDate(date))) {
                        assignment(date, weekday, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    } else if (restDay.contains(DateUtil.formatDate(date))) {
                        assignment(date, restDay, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    } else if (redLetterDay.contains(DateUtil.formatDate(date))) {
                        assignment(date, redLetterDay, typicalCurveDayList, predictionDay, userProductionStatusList, excludeDayUserList);
                    }
                }
                getDataTypeVO.setPredictionDayList(predictionDay);
                getDataTypeVO.setTypicalCurveDayList(typicalCurveDayList);
                list.add(getDataTypeVO);
            }
        }
        return list;
    }

    private void assignment(Date date,
                            List<String> day,
                            List<String> typicalCurveDayList, List<String> predictionDay,
                            List<VppUserProductionStatusVO> userProductionStatusList,
                            List<VppPredictedExcludeDay> excludeDayList) {
        // 创建一个新的列表，用于存储从今天开始往前推的 180 天数据
        List<String> last180Days = new ArrayList<>();

        log.info("使用的日期{}", JSON.toJSONString(day));

        // 遍历 day 列表，找到当前日期的位置
        int currentIndex = -1;
        for (int i = 0; i < day.size(); i++) {
            String dayStr = day.get(i);
            Date dayDate = DateUtil.parse(dayStr); // 假设 DateUtil.parse 可以将字符串转换为 Date
            if (DateUtil.isSameDay(dayDate, date)) { // 假设 DateUtil.isSameDay 用于比较日期是否相同
                currentIndex = i;
                break;
            }
        }

        // 如果找到当前日期
        if (currentIndex != -1) {
            // 从当前日期往前推 180 天
            int startIndex = Math.max(0, currentIndex - 180); // 确保不越界
            for (int i = startIndex; i <= currentIndex; i++) {
                String dayStr = day.get(i);
                last180Days.add(dayStr); // 添加到新的列表中
            }
        } else {
            // 如果没有找到当前日期，可以根据需求处理
            System.out.println("当前日期不在 day 列表中");
        }
        log.info("last180Days{}", JSON.toJSONString(last180Days));
        if (ObjectUtil.isNotEmpty(last180Days)) {
            if (ObjectUtil.isNotEmpty(userProductionStatusList)) {
                Map<String, Integer> actualMap = userProductionStatusList.get(0).getActualMap();
                //map排序
                Map<String, Integer> actualFinalMap = new LinkedHashMap<>();
                actualMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> actualFinalMap.put(x.getKey(), x.getValue()));

                Map<String, Integer> expectedMap = userProductionStatusList.get(0).getExpectedMap();

                //当天的生产情况
                Integer status = 1;
                if (ObjectUtil.isNotNull(expectedMap.get(DateUtil.formatDate(date)))) {
                    status = expectedMap.get(DateUtil.formatDate(date));
                } else {
                    if (ObjectUtil.isNotNull(actualMap.get(DateUtil.formatDate(date)))) {
                        status = actualMap.get(DateUtil.formatDate(date));
                    }
                }


                List<String> statusDayList = new ArrayList<>();
                for (Map.Entry<String, Integer> entry : actualFinalMap.entrySet()) {
                    String dateDay = entry.getKey();
                    Integer type = entry.getValue();
                    if (status.equals(type)) {
                        statusDayList.add(dateDay);
                    }
                }
                List<String> dayList = last180Days.stream().filter(statusDayList::contains).collect(Collectors.toList());
                //todo 在加判断去掉异常的日期
                if (ObjectUtil.isNotEmpty(excludeDayList)) {
                    List<String> excludeDayStringList = new ArrayList<>();
                    for (VppPredictedExcludeDay vppPredictedExcludeDay : excludeDayList) {
                        String formatDate = DateUtil.formatDate(vppPredictedExcludeDay.getDateDay());
                        if (!formatDate.equals(DateUtil.formatDate(date))) {
                            excludeDayStringList.add(formatDate);
                        }
                    }
                    dayList = dayList.stream().filter(o -> !excludeDayStringList.contains(o)).collect(Collectors.toList());
                }
                log.info("终筛选的日期{}", JSON.toJSONString(dayList));

                if (ObjectUtil.isNotEmpty(dayList)) {
                    int compare = DateUtil.compare(date, DateUtil.parseDate(DateUtil.formatDate(new Date())));
                    List<String> typicalCurve = new ArrayList<>();
                    List<String> prediction = new ArrayList<>();
                    Collections.sort(dayList);
                    int index = dayList.indexOf(DateUtil.formatDate(date));

                    if (compare >= 0 || index < 0) {
                        // date 大于当前日期
                        for (int i = dayList.size() - 1; i >= 0; i--) {
                            String dayStr = dayList.get(i);
                            Date dayDate = DateUtil.parse(dayStr); // 假设 DateUtil.parse 可以将字符串转换为 Date
                            if (DateUtil.compare(dayDate, new Date()) < 0) {
                                // 找到小于当前日期的日期
                                date = dayDate;
                                break; // 找到后跳出循环
                            }
                        }
                        //从list中去除当前日期往前30天当做典型曲线 7天当做预测
                        //获取当前日期往前30天的数据（包括今天）
                        int index1 = dayList.indexOf(DateUtil.formatDate(date));
                        if (dayList.size() > 30) {
                            for (int i = Math.max(0, index1 - 29); i <= index1; i++) { // 注意：i <= index，包括今天
                                typicalCurve.add(dayList.get(i));
                            }
                        } else {
                            List<String> typicalCurveList = new ArrayList<>(dayList);
                            if (index > 0) {
                                typicalCurveList.remove(index);
                            }
                            typicalCurve = typicalCurveList;
                        }

                        //获取当前日期往前7天的数据（包括今天）
                        if (dayList.size() > 7) {
                            for (int i = Math.max(0, index1 - 6); i <= index1; i++) { // 注意：i <= index，包括今天
                                prediction.add(dayList.get(i));
                            }
                        } else {
                            List<String> predictionList = new ArrayList<>(dayList);
                            if (index >= 0) {
                                predictionList.remove(index);
                            }
                            prediction = predictionList;
                        }
                    } else {
                        if (index != 0) {
                            //从list中去除当前日期往前30天当做典型曲线 7天当做预测
                            //获取当前日期往前30天的数据（不包括今天）
                            if (dayList.size() > 30) {
                                for (int i = Math.max(0, index - 30); i < index; i++) { // 注意：i < index，不包括今天
                                    typicalCurve.add(dayList.get(i));
                                }
                            } else {
                                List<String> typicalCurveList = new ArrayList<>(dayList);
                                if (index > 0) {
                                    typicalCurveList.remove(index);
                                }
                                typicalCurve = typicalCurveList;
                            }

                            //获取当前日期往前7天的数据（不包括今天）
                            if (dayList.size() > 7) {
                                for (int i = Math.max(0, index - 7); i < index; i++) { // 注意：i < index，不包括今天
                                    prediction.add(dayList.get(i));
                                }
                            } else {
                                List<String> predictionList = new ArrayList<>(dayList);
                                if (index > 0) {
                                    predictionList.remove(index);
                                }
                                prediction = predictionList;
                            }
                        }
                    }
                    //将结果添加到resultDay中
                    log.info("典型日{}", JSON.toJSONString(typicalCurve));
                    log.info("预测日{}", JSON.toJSONString(prediction));
                    typicalCurveDayList.addAll(typicalCurve);
                    predictionDay.addAll(prediction);
                }
            }
        }
    }

    public static void main(String[] args) {
        int compare = DateUtil.compare(DateUtil.parseDate("2025-03-14"), DateUtil.parseDate(DateUtil.formatDate(new Date())));
        System.out.println(compare);


//        String referenceDate = "2025-02-08";
//        String[] split = referenceDate.split(",");
//        List<String> referenceDate1 = new ArrayList<>(Arrays.asList(split));
//        System.out.println(referenceDate1);

        if (new BigDecimal(1).compareTo(BigDecimal.ZERO) > 0) {
            System.out.println(1);
        } else {
            System.out.println(2);
        }
    }


    public static BigDecimal calculateMedian(List<BigDecimal> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }

        int size = list.size();
        int middle = size / 2;

        if (size % 2 == 1) {
            // 列表长度为奇数，直接返回中间元素
            return list.get(middle);
        } else {
            // 列表长度为偶数，返回中间两个元素的平均值
            BigDecimal sum = list.get(middle - 1).add(list.get(middle));
            return sum.divide(new BigDecimal("2"), 3, RoundingMode.HALF_UP); // 保留两位小数
        }
    }
}
