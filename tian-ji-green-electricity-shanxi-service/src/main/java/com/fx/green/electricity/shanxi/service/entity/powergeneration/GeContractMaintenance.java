package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同数据维护
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("合同数据维护")
public class GeContractMaintenance extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1998363951172656142L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("合同标的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetStartDate;

    @ApiModelProperty("合同标的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetEndDate;

    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

}
