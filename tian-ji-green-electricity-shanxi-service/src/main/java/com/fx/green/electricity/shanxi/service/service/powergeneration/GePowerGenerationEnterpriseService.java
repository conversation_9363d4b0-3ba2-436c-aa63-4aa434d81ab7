package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationEnterprise;

import java.util.List;

/**
 * 发电企业信息 Service 接口
 */
public interface GePowerGenerationEnterpriseService extends IService<GePowerGenerationEnterprise> {

    /**
     * 新增发电企业信息
     *
     * @param param 发电企业信息
     */
    void createPowerGenerationEnterprise(GePowerGenerationEnterpriseDTO param);

    /**
     * 删除发电企业信息
     *
     * @param param 发电企业信息
     */
    void deletePowerGenerationEnterprise(IdDTO param);

    /**
     * 发电企业信息分页
     *
     * @param param 发电企业信息
     * @return 发电企业信息
     */
    List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterprisePage(GePowerGenerationEnterpriseDTO param);

    /**
     * 获取所有发电企业信息和机组对应信息
     *
     * @return 发电企业信息和机组对应信息
     */
    List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseList();

    /**
     * 根据发电企业 id 列表查询发电企业信息
     *
     * @param enterpriseIds 发电企业 id 列表
     * @return 发电企业信息
     */
    List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseListByIds(List<Long> enterpriseIds);

    /**
     * 根据发电企业名称查询发电企业信息
     *
     * @param enterpriseName 发电企业名称
     * @return 发电企业信息
     */
    List<GePowerGenerationEnterpriseVO> getPowerGenerationEnterpriseListByName(String enterpriseName);

    /**
     * 获取企业机组树
     *
     * @param param 企业机组树DTO
     * @return 企业机组树列表
     */
    List<GePowerGenerationEnterpriseUnitTreeVO> getEnterpriseUnitTree(GePowerGenerationEnterpriseUnitTreeDTO param);
}
