package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitDevice;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GeUnitDeviceMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitDeviceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备信息
 */
@Service
public class GeUnitDeviceServiceImpl extends ServiceImpl<GeUnitDeviceMapper, GeUnitDevice> implements GeUnitDeviceService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUnitDevice(GeUnitDeviceSaveDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

        // 1. 校验设备名称是否重复
        validateDeviceNameUnique(param.getDeviceList().stream().map(GeUnitDeviceDTO::getDeviceName).collect(Collectors.toList()), tenantId);

        // 2. 校验设备编号是否重复
        validateDeviceCodeUnique(param.getDeviceList().stream().map(GeUnitDeviceDTO::getDeviceCode).collect(Collectors.toList()), tenantId);

        // 2. 保存设备信息
        List<GeUnitDevice> collect = param.getDeviceList().stream().map(t -> {
            GeUnitDevice geUnitDevice = new GeUnitDevice();
            BeanUtil.copyProperties(t, geUnitDevice);
            geUnitDevice.setTenantId(tenantId);
            geUnitDevice.setEnterpriseId(param.getEnterpriseId());
            geUnitDevice.setUnitId(param.getUnitId());
            return geUnitDevice;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    @Override
    public void deleteUnitDevice(IdDTO param) {
        // 1. 校验设备是否存在
        validateDeviceExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 删除设备
        baseMapper.deleteById(param.getId());
    }

    /**
     * 校验设备名称是否重复
     *
     * @param deviceNameList 设备名称列表
     * @param tenantId       租户ID
     */
    private void validateDeviceNameUnique(List<String> deviceNameList, Long tenantId) {
        LambdaQueryWrapper<GeUnitDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitDevice::getTenantId, tenantId);
        wrapper.in(GeUnitDevice::getDeviceName, deviceNameList);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_DEVICE_INSERT_REPEAT_ERROR.getMessage());
        }
    }

    /**
     * 校验设备是否存在
     *
     * @param id       设备ID
     * @param tenantId 租户ID
     */
    private void validateDeviceExists(Long id, Long tenantId) {
        LambdaQueryWrapper<GeUnitDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitDevice::getTenantId, tenantId);
        wrapper.eq(GeUnitDevice::getId, id);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer == 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_DEVICE_NOT_EXIST_ERROR.getMessage());
        }
    }

    /**
     * 校验设备编号是否重复
     *
     * @param deviceCodeList 设备编号列表
     * @param tenantId       租户ID
     */
    private void validateDeviceCodeUnique(List<String> deviceCodeList, Long tenantId) {
        LambdaQueryWrapper<GeUnitDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitDevice::getTenantId, tenantId);
        wrapper.in(GeUnitDevice::getDeviceCode, deviceCodeList);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_DEVICE_INSERT_REPEAT_ERROR.getMessage());
        }
    }
}
