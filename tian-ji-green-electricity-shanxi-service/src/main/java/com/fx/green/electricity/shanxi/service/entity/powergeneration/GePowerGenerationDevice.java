package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 发电设备表
 */
@TableName("ge_power_generation_device")
@EqualsAndHashCode(callSuper = true)
@Data
public class GePowerGenerationDevice extends TenantBaseEntity {

    /**
     * 发电设备编号
     */
    private Long id;

    /**
     * 父级编号
     * <p>
     * 关联 {@link GePowerGenerationDevice#getId()}
     */
    private Long parentId;

    /**
     * 发电企业编号
     * <p>
     * 关联 {@link GePowerGenerationEnterprise#getId()}
     */
    private Long enterpriseId;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备编号
     */
    private String code;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 设备类型(1逆变器、2升压站、3储能设备、4系统站、5汇流站)
     * <p>
     * 枚举 {@link com.fx.green.electricity.shanxi.api.enums.powergeneration.PowerGenerationDeviceTypeEnum}
     */
    private Integer type;

}
