package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationMeterSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationMeter;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GePowerGenerationMeterMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationMeterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 发电电表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class GePowerGenerationMeterServiceImpl extends ServiceImpl<GePowerGenerationMeterMapper, GePowerGenerationMeter> implements GePowerGenerationMeterService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPowerGenerationMeter(GePowerGenerationMeterSaveDTO param) {
        GePowerGenerationMeter meter = BeanUtil.copyProperties(param, GePowerGenerationMeter.class);
        meter.setTenantId(RequestHeadersUtil.getRequestHeaders().getTenantId());
        save(meter);
    }

    @Override
    public List<GePowerGenerationMeterVO> getMeterListByDeviceId(Long deviceId) {
        LambdaQueryWrapper<GePowerGenerationMeter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationMeter::getDeviceId, deviceId);
        wrapper.eq(GePowerGenerationMeter::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());
        List<GePowerGenerationMeter> list = baseMapper.selectList(wrapper);
        return list.stream().map(t -> BeanUtil.copyProperties(t, GePowerGenerationMeterVO.class)).collect(Collectors.toList());
    }

}
