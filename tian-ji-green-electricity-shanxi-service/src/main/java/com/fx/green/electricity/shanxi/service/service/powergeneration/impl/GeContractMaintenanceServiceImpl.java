package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.enums.powergeneration.GeUnitTypeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeContractMaintenance;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GeContractMaintenanceMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationEnterpriseService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitBasicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绿电直连合同数据维护 Service 实现类
 */
@Slf4j
@Service
public class GeContractMaintenanceServiceImpl extends ServiceImpl<GeContractMaintenanceMapper, GeContractMaintenance> implements GeContractMaintenanceService {

    @Resource
    private GePowerGenerationEnterpriseService gePowerGenerationEnterpriseService;

    @Resource
    private GeUnitBasicService geUnitBasicService;

    @Override
    public void insertContract(GeContractMaintenanceDTO param) {
        // 1. 校验合同标的是否重复
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        LambdaQueryWrapper<GeContractMaintenance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GeContractMaintenance::getTenantId, tenantId);
        queryWrapper.eq(GeContractMaintenance::getUnitId, param.getUnitId());
        queryWrapper.and(
                wrapper -> wrapper.le(GeContractMaintenance::getTargetStartDate, param.getTargetStartDate()).ge(GeContractMaintenance::getTargetEndDate, param.getTargetStartDate())
                        .or().ge(GeContractMaintenance::getTargetStartDate, param.getTargetStartDate()).le(GeContractMaintenance::getTargetEndDate, param.getTargetEndDate())
                        .or().le(GeContractMaintenance::getTargetStartDate, param.getTargetEndDate()).ge(GeContractMaintenance::getTargetEndDate, param.getTargetEndDate()));
        Integer integer = baseMapper.selectCount(queryWrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.CONTRACT_MAINTENANCE_TARGET_DATE_REPEAT_ERROR.getMessage());
        }

        // 2. 保存合同信息
        GeContractMaintenance geContractMaintenance = new GeContractMaintenance();
        BeanUtil.copyProperties(param, geContractMaintenance);
        geContractMaintenance.setTenantId(tenantId);
        baseMapper.insert(geContractMaintenance);
    }

    @Override
    public void deleteContract(IdDTO param) {
        baseMapper.deleteById(param.getId());
    }

    @Override
    public List<GeContractMaintenanceVO> contractList(GeContractMaintenanceDTO param) {
        try {
            // 1. 构建查询条件
            LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            wrapper.eq(GeContractMaintenance::getTenantId, tenantId);

            // 添加查询条件过滤
            if (ObjectUtil.isNotNull(param)) {
                if (ObjectUtil.isNotNull(param.getEnterpriseId())) {
                    wrapper.eq(GeContractMaintenance::getEnterpriseId, param.getEnterpriseId());
                }
                if (ObjectUtil.isNotNull(param.getUnitId())) {
                    wrapper.eq(GeContractMaintenance::getUnitId, param.getUnitId());
                }
                if (ObjectUtil.isNotNull(param.getTargetStartDate())) {
                    wrapper.ge(GeContractMaintenance::getTargetEndDate, param.getTargetStartDate());
                }
                if (ObjectUtil.isNotNull(param.getTargetEndDate())) {
                    wrapper.le(GeContractMaintenance::getTargetStartDate, param.getTargetEndDate());
                }
            }

            wrapper.orderByDesc(GeContractMaintenance::getTargetStartDate);

            // 2. 查询合同数据
            List<GeContractMaintenance> contracts = baseMapper.selectList(wrapper);
            if (ObjectUtil.isEmpty(contracts)) {
                return Collections.emptyList();
            }

            // 3. 机组名称模糊查询
            if (ObjectUtil.isNotEmpty(param.getUnitName())) {
                List<Long> matchedUnitIds = getUnitIdsByNameLike(param.getUnitName(), tenantId);
                if (matchedUnitIds.isEmpty()) {
                    // 如果没有找到匹配的机组，直接返回空结果
                    return Collections.emptyList();
                }
                // 过滤出匹配机组名称的合同
                contracts = contracts.stream()
                        .filter(contract -> matchedUnitIds.contains(contract.getUnitId()))
                        .collect(Collectors.toList());

                if (ObjectUtil.isEmpty(contracts)) {
                    return Collections.emptyList();
                }
            }

            // 4. 构建返回结果
            List<GeContractMaintenanceVO> result = buildContractVOList(contracts);

            log.debug("查询合同列表成功，租户ID: {}, 返回数据量: {}", tenantId, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询合同列表失败，参数: {}, 错误信息: {}", param, e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    @Override
    public List<Long> getContractUnitIdsByDate(Long tenantId, Date date) {
        try {
            LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(GeContractMaintenance::getTenantId, tenantId)
                    .le(GeContractMaintenance::getTargetStartDate, date)
                    .ge(GeContractMaintenance::getTargetEndDate, date);

            List<GeContractMaintenance> contracts = list(wrapper);

            return contracts.stream()
                    .map(GeContractMaintenance::getUnitId)
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取绿电直连合同机组ID失败，租户ID: {}, 日期: {}, 错误信息: {}", tenantId, date, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<GeContractMaintenanceVO> getContractByUnitId(Long unitId) {
        LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeContractMaintenance::getUnitId, unitId);
        List<GeContractMaintenance> contracts = baseMapper.selectList(wrapper);
        if (ObjectUtil.isNotEmpty(contracts)) {
            return buildContractVOList(contracts);
        }
        return Collections.emptyList();
    }

    @Override
    public List<GeContractMaintenanceVO> getContractByUnitIds(List<Long> unitIds) {
        LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeContractMaintenance::getUnitId, unitIds);
        List<GeContractMaintenance> contracts = baseMapper.selectList(wrapper);
        if (ObjectUtil.isNotEmpty(contracts)) {
            return buildContractVOList(contracts);
        }
        return Collections.emptyList();
    }

    @Override
    public FxPage<GeContractMaintenanceVO> getPowerGenerationContractPage(GeContractMaintenanceDTO param) {
        try {
            // 1. 构建查询条件
            LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            wrapper.eq(GeContractMaintenance::getTenantId, tenantId);

            // 添加查询条件过滤
            if (ObjectUtil.isNotNull(param)) {
                if (ObjectUtil.isNotNull(param.getEnterpriseId())) {
                    wrapper.eq(GeContractMaintenance::getEnterpriseId, param.getEnterpriseId());
                }
                if (ObjectUtil.isNotNull(param.getUnitId())) {
                    wrapper.eq(GeContractMaintenance::getUnitId, param.getUnitId());
                }

                // 机组名称模糊查询
                if (ObjectUtil.isNotEmpty(param.getUnitName())) {
                    List<Long> matchedUnitIds = getUnitIdsByNameLike(param.getUnitName(), tenantId);
                    if (matchedUnitIds.isEmpty()) {
                        // 如果没有找到匹配的机组，直接返回空结果
                        return FxPage.page(Collections.emptyList(), 0, param.getPage(), param.getPageSize());
                    }
                    wrapper.in(GeContractMaintenance::getUnitId, matchedUnitIds);
                }

                // 机组类型查询
                if (ObjectUtil.isNotNull(param.getUnitType())) {
                    List<Long> matchedUnitIds = getUnitIdsByType(param.getUnitType(), tenantId);
                    if (matchedUnitIds.isEmpty()) {
                        // 如果没有找到匹配的机组，直接返回空结果
                        return FxPage.page(Collections.emptyList(), 0, param.getPage(), param.getPageSize());
                    }
                    wrapper.in(GeContractMaintenance::getUnitId, matchedUnitIds);
                }

                if (ObjectUtil.isNotNull(param.getTargetStartDate())) {
                    wrapper.ge(GeContractMaintenance::getTargetEndDate, param.getTargetStartDate());
                }
                if (ObjectUtil.isNotNull(param.getTargetEndDate())) {
                    wrapper.le(GeContractMaintenance::getTargetStartDate, param.getTargetEndDate());
                }
            }

            wrapper.orderByDesc(GeContractMaintenance::getTargetStartDate);

            // 2. 分页查询合同数据
            IPage<GeContractMaintenance> page = baseMapper.selectPage(
                    new Page<>(param.getPage(), param.getPageSize()), wrapper);

            if (ObjectUtil.isEmpty(page.getRecords())) {
                return FxPage.page(Collections.emptyList(), 0, param.getPage(), param.getPageSize());
            }

            // 3. 批量查询关联数据并组装结果
            List<GeContractMaintenanceVO> resultList = buildContractVOList(page.getRecords());

            log.debug("分页查询合同列表成功，租户ID: {}, 页码: {}, 每页大小: {}, 总记录数: {}, 机组名称查询: {}",
                    tenantId, param.getPage(), param.getPageSize(), page.getTotal(), param.getUnitName());

            return FxPage.page(resultList, page.getTotal(), param.getPage(), param.getPageSize());

        } catch (Exception e) {
            log.error("分页查询合同列表失败，参数: {}, 错误信息: {}", param, e.getMessage(), e);
            return FxPage.page(Collections.emptyList(), 0, param.getPage(), param.getPageSize());
        }
    }

    /**
     * 根据机组名称模糊查询获取机组 ID 列表
     *
     * @param unitName 机组名称
     * @param tenantId 租户 ID
     * @return 机组 ID 列表
     */
    private List<Long> getUnitIdsByNameLike(String unitName, Long tenantId) {
        try {
            LambdaQueryWrapper<GeUnitBasic> unitWrapper =
                    new LambdaQueryWrapper<>();
            unitWrapper.eq(GeUnitBasic::getTenantId, tenantId);
            unitWrapper.like(GeUnitBasic::getUnitName, unitName);

            List<GeUnitBasic> units =
                    geUnitBasicService.getBaseMapper().selectList(unitWrapper);

            return units.stream()
                    .map(GeUnitBasic::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据机组名称查询机组ID失败，机组名称: {}, 租户ID: {}, 错误信息: {}", unitName, tenantId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据机组类型查询机组 ID 列表
     *
     * @param unitType 机组类型
     * @param tenantId 租户 ID
     * @return 机组ID列表
     */
    private List<Long> getUnitIdsByType(Integer unitType, Long tenantId) {
        try {
            LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(GeUnitBasic::getTenantId, tenantId);
            wrapper.eq(GeUnitBasic::getType, unitType);
            List<GeUnitBasic> units = geUnitBasicService.getBaseMapper().selectList(wrapper);
            return units.stream()
                    .map(GeUnitBasic::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据机组类型查询机组ID失败，机组类型: {}, 租户ID: {}, 错误信息: {}", unitType, tenantId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建合同VO列表，包含企业名称和机组名称
     *
     * @param contracts 合同实体列表
     * @return 合同VO列表
     */
    private List<GeContractMaintenanceVO> buildContractVOList(List<GeContractMaintenance> contracts) {
        if (ObjectUtil.isEmpty(contracts)) {
            return Collections.emptyList();
        }

        // 收集需要查询的ID
        Set<Long> enterpriseIds = contracts.stream()
                .map(GeContractMaintenance::getEnterpriseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Long> unitIds = contracts.stream()
                .map(GeContractMaintenance::getUnitId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 并行查询企业和机组信息
        Map<Long, String> enterpriseNameMap = new HashMap<>();
        Map<Long, String> unitNameMap = new HashMap<>();
        Map<Long, Integer> unitTypeMap = new HashMap<>();

        if (!enterpriseIds.isEmpty()) {
            List<GePowerGenerationEnterpriseVO> enterprises =
                    gePowerGenerationEnterpriseService.getPowerGenerationEnterpriseListByIds(new ArrayList<>(enterpriseIds));
            enterpriseNameMap = enterprises.stream()
                    .collect(Collectors.toMap(GePowerGenerationEnterpriseVO::getId,
                            GePowerGenerationEnterpriseVO::getName,
                            (existing, replacement) -> existing));
        }

        if (!unitIds.isEmpty()) {
            List<GeUnitBasicVO> units = geUnitBasicService.getUnitBasicListByIds(new ArrayList<>(unitIds));
            unitNameMap = units.stream()
                    .collect(Collectors.toMap(GeUnitBasicVO::getId,
                            GeUnitBasicVO::getUnitName,
                            (existing, replacement) -> existing));
            unitTypeMap = units.stream()
                    .collect(Collectors.toMap(GeUnitBasicVO::getId,
                            GeUnitBasicVO::getType,
                            (existing, replacement) -> existing));
        }

        // 组装返回结果
        List<GeContractMaintenanceVO> resultList = new ArrayList<>();
        for (GeContractMaintenance contract : contracts) {
            GeContractMaintenanceVO vo = new GeContractMaintenanceVO();
            BeanUtil.copyProperties(contract, vo);
            vo.setEnterpriseName(enterpriseNameMap.get(contract.getEnterpriseId()));
            vo.setUnitName(unitNameMap.get(contract.getUnitId()));
            vo.setUnitType(unitTypeMap.get(contract.getUnitId()));
            resultList.add(vo);
        }

        return resultList;
    }

    @Override
    public List<GeContractMaintenanceExportVO> exportContract(GeContractMaintenanceDTO param) {
        try {
            // 1. 获取所有合同数据（不分页）
            List<GeContractMaintenanceVO> allContracts = contractList(param);

            if (ObjectUtil.isEmpty(allContracts)) {
                return Collections.emptyList();
            }

            // 2. 转换为导出VO
            return allContracts.stream()
                    .map(this::buildExportVO)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("导出合同数据失败，参数: {}, 错误信息: {}", param, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建导出VO
     */
    private GeContractMaintenanceExportVO buildExportVO(GeContractMaintenanceVO contract) {
        GeContractMaintenanceExportVO exportVO = new GeContractMaintenanceExportVO();

        // 1. 新能源公司
        exportVO.setEnterpriseName(contract.getEnterpriseName());

        // 2. 机组名称
        exportVO.setUnitName(contract.getUnitName());

        // 3. 类型（机组类型转换为字符串）
        exportVO.setUnitTypeStr(convertUnitTypeToString(contract.getUnitType()));

        // 4. 合同时间（格式：2025-01 ~ 2034-12）
        exportVO.setContractTime(formatContractTime(contract.getTargetStartDate(), contract.getTargetEndDate()));

        // 5. 合同价格
        exportVO.setContractPrice(contract.getContractPrice());

        return exportVO;
    }

    /**
     * 格式化合同时间
     */
    private String formatContractTime(Date targetStartDate, Date targetEndDate) {
        if (targetStartDate == null || targetEndDate == null) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(targetStartDate) + " ~ " + sdf.format(targetEndDate);
    }

    /**
     * 转换机组类型为字符串
     */
    private String convertUnitTypeToString(Integer unitType) {
        if (unitType == null) {
            return "";
        }

        // 使用机组类型枚举获取对应的中文描述
        String label = GeUnitTypeEnum.getLabelByValue(unitType);
        if (label != null) {
            return label;
        }
        
        // 如果枚举中没有对应的类型，返回默认格式
        return "其他(" + unitType + ")";
    }
}
