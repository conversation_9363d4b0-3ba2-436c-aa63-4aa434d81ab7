package com.fx.green.electricity.shanxi.service.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 负荷用户电表
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("vpp_user_meter")
public class VppUserMeter extends TenantBaseEntity {

    /**
     * 设备id
     */
    private Long id;

    /**
     * 负荷用户id
     */
    private Long userId;

    /**
     * 户号id
     */
    private Long accountId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 用户户号
     */
    private String consNo;

    /**
     * 电表名称
     */
    private String meterName;

    /**
     * 电表表号
     */
    private String meterNo;

}