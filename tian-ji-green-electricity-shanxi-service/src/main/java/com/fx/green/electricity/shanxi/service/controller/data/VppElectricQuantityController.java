package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用电数据
 * 1、 申报数据
 * 2、 实际用电量
 *
 * <AUTHOR>
 **/
@RestController
@Api(tags = "数据维护 - 电量数据")
@RequestMapping("/vppElectricQuantity")
public class VppElectricQuantityController {

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @ApiOperation("查询实际用电量导入记录")
    @PostMapping("/queryImportRecord")
    public DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param) {
        return DataResult.success(vppElectricQuantityService.queryImportRecord(param));
    }

    @CommonNoRepeat
    @ApiOperation("申报电量数据导入")
    @PostMapping("/importRecord")
    public DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file, @RequestParam("time") String time) {
        return DataResult.success(vppElectricQuantityService.importRecord(file, time));
    }

    @CommonNoRepeat
    @ApiOperation("日前申报数据下载")
    @PostMapping("/downloadRecord")
    public DataResult<List<SeElectricDeclareVO>> downloadRecord(@RequestBody CommonDTO.DateDTO param) {
        return vppElectricQuantityService.downloadRecord(param);
    }

    @ApiOperation("删除某一天的申报数据")
    @PostMapping("/deleteRecordData")
    public DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO) {
        return vppElectricQuantityService.deleteRecordData(deleteRecordDTO);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("实际用电量 - 导入")
    @PostMapping("/importActualElectricityConsumption")
    public DataResult<ImportExcelVO> importActualElectricityConsumption(@RequestPart("file") MultipartFile file) {
        return DataResult.success(vppElectricQuantityService.importActualElectricityConsumption(file));
    }

    @ApiOperation("实际用电量 - 详情")
    @PostMapping("/getActualElectricityConsumptionDetail")
    public DataResult<List<ElectricityDetailVO>> getActualElectricityConsumptionDetail(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return DataResult.success(vppElectricQuantityService.getActualElectricityConsumptionDetail(param));
    }

    @ApiOperation("实际用电量 - 删除")
    @PostMapping("/deleteActualElectricityConsumption")
    public DataResult<Void> deleteActualElectricityConsumption(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        vppElectricQuantityService.deleteActualElectricityConsumption(param);
        return DataResult.success();
    }
}
