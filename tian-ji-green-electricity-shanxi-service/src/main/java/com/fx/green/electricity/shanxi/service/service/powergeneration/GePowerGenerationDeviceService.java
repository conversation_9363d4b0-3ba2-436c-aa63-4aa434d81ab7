package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceAssociatecConvergenceStationVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDevicePageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationDevice;

import java.util.List;

/**
 * 发电设备信息 Service 接口
 */
public interface GePowerGenerationDeviceService extends IService<GePowerGenerationDevice> {

    /**
     * 新增发电设备信息
     *
     * @param param 发电设备信息
     */
    void createPowerGenerationDevice(GePowerGenerationDeviceSaveDTO param);

    /**
     * 删除发电设备信息
     *
     * @param param 发电设备信息
     */
    void deletePowerGenerationDevice(IdDTO param);

    /**
     * 更新发电设备信息
     *
     * @param param 发电设备信息
     */
    void updatePowerGenerationDevice(GePowerGenerationDeviceSaveDTO param);

    /**
     * 获得发电设备信息详情
     *
     * @param param 发电设备信息
     * @return 发电设备信息
     */
    GePowerGenerationDeviceVO getPowerGenerationDevice(IdDTO param);

    /**
     * 关联机组
     *
     * @param param 发电设备关联机组DTO
     */
    void associateUnit(GePowerGenerationDeviceAssociateUnitDTO param);

    /**
     * 查询关联机组列表
     *
     * @param param 发电设备id
     * @return 关联机组列表
     */
    List<Long> getUnitList(IdDTO param);

    /**
     * 批量新增发电电表信息
     *
     * @param param 发电电表批量保存DTO
     */
    void createPowerGenerationMeter(GePowerGenerationMeterSaveDTO param);

    /**
     * 查询关联电表列表
     *
     * @param param 发电设备id
     * @return 关联电表列表
     */
    List<GePowerGenerationMeterVO> getMeterList(IdDTO param);

    /**
     * 分页查询发电设备信息
     *
     * @param param 分页查询参数
     * @return 分页结果
     */
    FxPage<GePowerGenerationDevicePageVO> getPowerGenerationDevicePage(GePowerGenerationDevicePageDTO param);

    /**
     * 查询发电设备信息列表
     *
     * @param param 查询参数
     * @return 发电设备信息列表
     */
    List<GePowerGenerationDeviceVO> getPowerGenerationDeviceList(GePowerGenerationDeviceListDTO param);

    /**
     * 汇总站关联发电设备
     *
     * @param param 汇总站关联发电设备DTO
     */
    void convergenceStationAssociate(GePowerGenerationDeviceAssociatecConvergenceStationDTO param);

    /**
     * 查询汇总站关联的发电设备信息
     *
     * @param param 汇总站ID
     * @return 汇总站关联发电设备信息
     */
    GePowerGenerationDeviceAssociatecConvergenceStationVO getConvergenceStationAssociated(IdDTO param);
}
