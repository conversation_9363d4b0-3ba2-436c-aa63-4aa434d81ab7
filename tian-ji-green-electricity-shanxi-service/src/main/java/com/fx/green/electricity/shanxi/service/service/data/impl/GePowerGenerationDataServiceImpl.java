package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GeFileImportEnum;
import com.fx.green.electricity.shanxi.api.enums.MaintenanceStatusEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.*;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitNodePrice;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitPowerPredict;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitRealElectricity;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.service.data.*;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitBasicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.*;

/**
 * 发电数据维护 Service 实现类
 */
@Slf4j
@Service
public class GePowerGenerationDataServiceImpl implements GePowerGenerationDataService {

    @Resource
    private GeUnitRealElectricityService geUnitRealElectricityService;

    @Resource
    private GeUnitPowerPredictService geUnitPowerPredictService;

    @Resource
    private GeUnitBasicService geUnitBasicService;

    @Resource
    private GeFileRecordService geFileRecordService;

    @Resource
    private GeUnitNodePriceService geUnitNodePriceService;

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    /**
     * 通过不同类型导入机组数据
     *
     * @param param 机组数据保存参数
     * @return 导入结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ImportExcelVO importUnitData(GeUnitSaveDTO param) {
        try {
            log.info("开始导入机组数据，文件类型: {}, 数据条数: {}", param.getFileType(), param.getFileDataList().size());

            // 验证输入参数
            validateImportParam(param);

            // 获取机组基础信息映射
            Map<String, GeUnitBasic> unitMap = getUnitBasicMap(param.getFileDataList());

            // 根据文件类型处理不同的数据导入
            GeFileImportEnum fileTypeEnum = GeFileImportEnum.getFileTypeEnum(param.getFileType());
            if (fileTypeEnum == null) {
                throw new FxServiceException("不支持的文件类型: " + param.getFileType());
            }

            int processedCount;
            switch (fileTypeEnum) {
                case UNIT_REAL_ELECTRICITY:
                    processedCount = importRealElectricityData(param, unitMap);
                    break;
                case UNIT_NODE_PRICE:
                    processedCount = importNodePriceData(param, unitMap);
                    break;
                case UNIT_POWER_PREDICT:
                    processedCount = importPowerPredictData(param, unitMap);
                    break;
                default:
                    throw new FxServiceException("未实现的文件类型处理: " + fileTypeEnum.getFileType());
            }

            log.info("机组数据导入完成，文件类型: {}, 处理条数: {}", param.getFileType(), processedCount);
            return createSuccessImportResult(processedCount, fileTypeEnum.getFileName());

        } catch (Exception e) {
            log.error("导入机组数据失败，文件类型: {}, 错误信息: {}", param.getFileType(), e.getMessage(), e);
            return createErrorImportResult(e.getMessage());
        }
    }

    /**
     * 验证导入参数
     */
    private void validateImportParam(GeUnitSaveDTO param) {
        if (param == null) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }
        if (param.getFileType() == null) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }
        if (param.getFileDataList() == null || param.getFileDataList().isEmpty()) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }
    }

    /**
     * 获取机组基础信息映射
     */
    private Map<String, GeUnitBasic> getUnitBasicMap(List<GeUnitImportDTO> fileDataList) {
        List<String> unitNameList = fileDataList.stream()
                .map(GeUnitImportDTO::getUnitName)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeUnitBasic::getUnitName, unitNameList);
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(wrapper);

        if (unitNameList.size() != geUnitBasics.size()) {
            Set<String> foundUnits = geUnitBasics.stream()
                    .map(GeUnitBasic::getUnitName)
                    .collect(Collectors.toSet());
            List<String> missingUnits = unitNameList.stream()
                    .filter(name -> !foundUnits.contains(name))
                    .collect(Collectors.toList());
            throw new FxServiceException("以下机组在系统中不存在: " + String.join(", ", missingUnits));
        }

        return geUnitBasics.stream().collect(Collectors.toMap(GeUnitBasic::getUnitName, unit -> unit));
    }

    /**
     * 导入机组实际发电数据
     */
    private int importRealElectricityData(GeUnitSaveDTO param, Map<String, GeUnitBasic> unitMap) {
        log.info("开始导入机组实际发电数据");

        List<Long> unitIds = unitMap.values().stream()
                .map(GeUnitBasic::getId)
                .collect(Collectors.toList());
        List<Date> dates = param.getFileDataList().stream()
                .map(GeUnitImportDTO::getInfoDate)
                .distinct()
                .collect(Collectors.toList());

        // 1. 删除已存在的数据
        deleteExistingRealElectricityData(unitIds, dates);

        // 2. 转换并保存新数据
        List<GeUnitRealElectricity> entities = param.getFileDataList().stream()
                .map(dto -> convertToRealElectricity(dto, unitMap))
                .collect(Collectors.toList());

        geUnitRealElectricityService.saveBatch(entities);

        // 3. 记录文件导入记录
        recordFileImport(entities, GeFileImportEnum.UNIT_REAL_ELECTRICITY.getFileType());

        // 4. 更新数据维护状态
        updateMaintenanceStatus(entities, GeFileImportEnum.UNIT_REAL_ELECTRICITY);

        log.info("机组实际发电数据导入完成，条数: {}", entities.size());
        return entities.size();
    }

    /**
     * 导入机组节点价格数据
     */
    private int importNodePriceData(GeUnitSaveDTO param, Map<String, GeUnitBasic> unitMap) {
        log.info("开始导入机组节点价格数据");

        List<Long> unitIds = unitMap.values().stream()
                .map(GeUnitBasic::getId)
                .collect(Collectors.toList());
        List<Date> dates = param.getFileDataList().stream()
                .map(GeUnitImportDTO::getInfoDate)
                .distinct()
                .collect(Collectors.toList());

        // 1. 删除已存在的数据
        deleteExistingNodePriceData(unitIds, dates);

        // 2. 转换并保存新数据
        List<GeUnitNodePrice> entities = param.getFileDataList().stream()
                .map(dto -> convertToNodePrice(dto, unitMap))
                .distinct()
                .collect(Collectors.toList());

        geUnitNodePriceService.saveBatch(entities);

        // 3. 记录文件导入记录
        recordFileImport(entities, GeFileImportEnum.UNIT_NODE_PRICE.getFileType());

        // 4. 更新数据维护状态
        updateMaintenanceStatus(entities, GeFileImportEnum.UNIT_NODE_PRICE);

        log.info("机组节点价格数据导入完成，条数: {}", entities.size());
        return entities.size();
    }

    /**
     * 导入机组功率预测数据
     */
    private int importPowerPredictData(GeUnitSaveDTO param, Map<String, GeUnitBasic> unitMap) {
        log.info("开始导入机组功率预测数据");

        List<Long> unitIds = unitMap.values().stream()
                .map(GeUnitBasic::getId)
                .collect(Collectors.toList());
        List<Date> dates = param.getFileDataList().stream()
                .map(GeUnitImportDTO::getInfoDate)
                .distinct()
                .collect(Collectors.toList());

        // 1. 删除已存在的数据
        deleteExistingPowerPredictData(unitIds, dates);

        // 2. 转换并保存新数据
        List<GeUnitPowerPredict> entities = param.getFileDataList().stream()
                .map(dto -> convertToPowerPredict(dto, unitMap))
                .collect(Collectors.toList());

        geUnitPowerPredictService.saveBatch(entities);

        // 3. 记录文件导入记录
        recordFileImport(entities, GeFileImportEnum.UNIT_POWER_PREDICT.getFileType());

        // 4. 更新数据维护状态
        updateMaintenanceStatus(entities, GeFileImportEnum.UNIT_POWER_PREDICT);

        log.info("机组功率预测数据导入完成，条数: {}", entities.size());
        return entities.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportExcelVO importPowerGenerationData(MultipartFile file, Integer type) {
        try {
            log.info("开始导入发电数据文件，文件名: {}, 类型: {}", file.getOriginalFilename(), type);

            // 1. 参数验证
            validateImportFile(file, type);

            // 2. 解析Excel文件
            List<GeUnitImportDTO> entities = parseExcelFile(file);

            if (entities.isEmpty()) {
                throw new FxServiceException(IMPORT_FILE_HAVE_NULL);
            }

            // 3. 构建保存参数并导入数据
            GeUnitSaveDTO geUnitSaveDTO = new GeUnitSaveDTO();
            geUnitSaveDTO.setFileType(type);
            geUnitSaveDTO.setFileDataList(entities);

            ImportExcelVO result = importUnitData(geUnitSaveDTO);

            log.info("发电数据文件导入成功，文件名: {}, 处理条数: {}", file.getOriginalFilename(), entities.size());
            return result;

        } catch (FxServiceException e) {
            log.error("导入发电数据文件业务异常，文件名: {}, 错误信息: {}", file.getOriginalFilename(), e.getMessage());
            return createErrorImportResult("导入失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导入发电数据文件系统异常，文件名: {}, 错误信息: {}", file.getOriginalFilename(), e.getMessage(), e);
            return createErrorImportResult("导入文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证导入文件
     */
    private void validateImportFile(MultipartFile file, Integer type) {
        if (file == null || file.isEmpty()) {
            throw new FxServiceException(IMPORT_EMPTY_ERROR);
        }

        if (type == null) {
            throw new FxServiceException(IMPORT_TEMPLATE_ERROR);
        }

        boolean isExcelFile = FileTypeUtil.checkExcelFile(file);
        if (!isExcelFile) {
            throw new FxServiceException(IMPORT_FILE_TYPE_ERROR);
        }
    }

    /**
     * 解析Excel文件
     */
    private List<GeUnitImportDTO> parseExcelFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            List<LinkedHashMap<Integer, String>> excelList = EasyExcel.read(inputStream)
                    .sheet()
                    .headRowNumber(0)
                    .doReadSync();

            if (excelList == null || excelList.isEmpty()) {
                throw new FxServiceException(IMPORT_EMPTY_ERROR);
            }

            if (excelList.size() < 2) {
                throw new FxServiceException("文件至少需要包含表头和一行数据");
            }

            // 解析表头
            LinkedHashMap<Integer, String> headerRow = excelList.get(0);
            List<String> timeHeaders = parseTimeHeaders(headerRow);

            if (timeHeaders.isEmpty()) {
                throw new FxServiceException("未找到有效的时间列头，请检查文件格式");
            }

            // 解析数据行
            List<GeUnitImportDTO> entities = new ArrayList<>();
            for (int i = 1; i < excelList.size(); i++) {
                try {
                    List<GeUnitImportDTO> rowData = parseDataRow(excelList.get(i), timeHeaders, i + 1);
                    entities.addAll(rowData);
                } catch (Exception e) {
                    log.warn("解析第{}行数据时出错: {}", i + 1, e.getMessage());
                    // 继续处理其他行，不中断整个导入过程
                }
            }

            return entities;
        }
    }

    /**
     * 解析数据行（重载方法，增加行号参数用于错误提示）
     */
    private List<GeUnitImportDTO> parseDataRow(LinkedHashMap<Integer, String> dataRow, List<String> timeHeaders, int rowNumber) {
        try {
            // 获取日期（第2列，索引为1）
            String dateStr = dataRow.get(1);
            if (dateStr == null || dateStr.trim().isEmpty()) {
                throw new IllegalArgumentException("日期不能为空");
            }
            Date dateTime = DateUtil.parseDate(dateStr.trim());

            // 获取机组名称（第3列，索引为2）
            String unitNameRaw = dataRow.get(2);
            if (unitNameRaw == null || unitNameRaw.trim().isEmpty()) {
                throw new IllegalArgumentException("机组名称不能为空");
            }
            final String unitName = unitNameRaw.trim();

            // 解析时间点数据
            List<TimeBaseDTO> timeBaseDTOS = parseTimePointData(dataRow, timeHeaders);

            return timeBaseDTOS.stream().map(timeBase -> {
                GeUnitImportDTO dto = new GeUnitImportDTO();
                dto.setValue(timeBase.getValue());
                dto.setTimeFrame(timeBase.getTimeFrame());
                dto.setUnitName(unitName);
                dto.setInfoDate(dateTime);
                return dto;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            throw new IllegalArgumentException("第" + rowNumber + "行数据解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理表头获取到96点数据
     *
     * @param headerRow 表头
     */
    private List<String> parseTimeHeaders(LinkedHashMap<Integer, String> headerRow) {
        List<String> timeHeaders = new ArrayList<>();

        // 跳过前两列（日期和机组名称）
        for (int i = 3; i < headerRow.size(); i++) {
            String header = headerRow.get(i);
            if (header != null && !header.trim().isEmpty()) {
                timeHeaders.add(header.trim());
            }
        }

        return timeHeaders;
    }


    // ======================= 辅助方法 =======================

    /**
     * 解析时间点数据（重命名原方法以避免冲突）
     */
    private List<TimeBaseDTO> parseTimePointData(LinkedHashMap<Integer, String> dataRow, List<String> timeHeaders) {
        List<TimeBaseDTO> entities = new ArrayList<>();

        // 遍历时间列数据（从第三列开始）
        for (int i = 3; i < dataRow.size(); i++) {
            String time = timeHeaders.get(i - 3); // 对应的时间点
            String valueStr = dataRow.get(i);

            if (valueStr != null && !valueStr.trim().isEmpty()) {
                try {
                    BigDecimal value = new BigDecimal(valueStr.trim());

                    TimeBaseDTO entity = new TimeBaseDTO();
                    entity.setTimeFrame(time);
                    entity.setValue(value);

                    entities.add(entity);
                } catch (NumberFormatException e) {
                    log.warn("数值格式错误: {}", valueStr);
                }
            }
        }
        return entities;
    }

    /**
     * 删除已存在的实际发电数据
     */
    private void deleteExistingRealElectricityData(List<Long> unitIds, List<Date> dates) {
        LambdaQueryWrapper<GeUnitRealElectricity> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(GeUnitRealElectricity::getUnitId, unitIds)
                .in(GeUnitRealElectricity::getInfoDate, dates);
        geUnitRealElectricityService.remove(deleteWrapper);
    }

    /**
     * 删除已存在的节点价格数据
     */
    private void deleteExistingNodePriceData(List<Long> unitIds, List<Date> dates) {
        LambdaQueryWrapper<GeUnitNodePrice> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(GeUnitNodePrice::getUnitId, unitIds)
                .in(GeUnitNodePrice::getInfoDate, dates);
        geUnitNodePriceService.remove(deleteWrapper);
    }

    /**
     * 删除已存在的功率预测数据
     */
    private void deleteExistingPowerPredictData(List<Long> unitIds, List<Date> dates) {
        LambdaQueryWrapper<GeUnitPowerPredict> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(GeUnitPowerPredict::getUnitId, unitIds)
                .in(GeUnitPowerPredict::getInfoDate, dates);
        geUnitPowerPredictService.remove(deleteWrapper);
    }

    /**
     * 转换为实际发电数据实体
     */
    private GeUnitRealElectricity convertToRealElectricity(GeUnitImportDTO dto, Map<String, GeUnitBasic> unitMap) {
        GeUnitBasic unitBasic = unitMap.get(dto.getUnitName());
        GeUnitRealElectricity entity = new GeUnitRealElectricity();
        entity.setUnitId(unitBasic.getId());
        entity.setTenantId(unitBasic.getTenantId());
        entity.setInfoDate(dto.getInfoDate());
        entity.setTimePoint(dto.getTimeFrame());
        entity.setValue(dto.getValue());
        return entity;
    }

    /**
     * 转换为节点价格数据实体
     */
    private GeUnitNodePrice convertToNodePrice(GeUnitImportDTO dto, Map<String, GeUnitBasic> unitMap) {
        GeUnitBasic unitBasic = unitMap.get(dto.getUnitName());
        GeUnitNodePrice entity = new GeUnitNodePrice();
        entity.setUnitId(unitBasic.getId());
        entity.setTenantId(unitBasic.getTenantId());
        entity.setInfoDate(dto.getInfoDate());
        entity.setTimePoint(dto.getTimeFrame());
        entity.setValue(dto.getValue());
        return entity;
    }

    /**
     * 转换为功率预测数据实体
     */
    private GeUnitPowerPredict convertToPowerPredict(GeUnitImportDTO dto, Map<String, GeUnitBasic> unitMap) {
        GeUnitBasic unitBasic = unitMap.get(dto.getUnitName());
        GeUnitPowerPredict entity = new GeUnitPowerPredict();
        entity.setUnitId(unitBasic.getId());
        entity.setTenantId(unitBasic.getTenantId());
        entity.setInfoDate(dto.getInfoDate());
        entity.setTimePoint(dto.getTimeFrame());
        entity.setValue(dto.getValue());
        return entity;
    }

    /**
     * 记录文件导入记录（通用方法）
     */
    private <T> void recordFileImport(List<T> entities, Integer fileType) {
        List<GeFileRecordDTO> fileRecords = entities.stream()
                .map(entity -> createFileRecord(entity, fileType))
                .distinct()
                .collect(Collectors.toList());

        if (!fileRecords.isEmpty()) {
            geFileRecordService.insertFileRecord(fileRecords);
        }
    }

    /**
     * 创建文件记录DTO
     */
    private <T> GeFileRecordDTO createFileRecord(T entity, Integer fileType) {
        GeFileRecordDTO record = new GeFileRecordDTO();

        if (entity instanceof GeUnitRealElectricity) {
            GeUnitRealElectricity realElectricity = (GeUnitRealElectricity) entity;
            record.setBelongId(realElectricity.getUnitId());
            record.setDataDate(realElectricity.getInfoDate());
        } else if (entity instanceof GeUnitNodePrice) {
            GeUnitNodePrice nodePrice = (GeUnitNodePrice) entity;
            record.setBelongId(nodePrice.getUnitId());
            record.setDataDate(nodePrice.getInfoDate());
        } else if (entity instanceof GeUnitPowerPredict) {
            GeUnitPowerPredict powerPredict = (GeUnitPowerPredict) entity;
            record.setBelongId(powerPredict.getUnitId());
            record.setDataDate(powerPredict.getInfoDate());
        }

        record.setFileType(fileType);
        return record;
    }

    /**
     * 更新数据维护状态
     */
    private <T> void updateMaintenanceStatus(List<T> entities, GeFileImportEnum fileImportEnum) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        try {
            // 按日期分组，为每个日期更新维护状态
            Map<Date, List<T>> dateGroupedEntities = groupEntitiesByDate(entities);

            for (Map.Entry<Date, List<T>> entry : dateGroupedEntities.entrySet()) {
                Date date = entry.getKey();
                List<T> dateEntities = entry.getValue();

                // 获取租户ID（从第一个实体中获取）
                Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
                if (tenantId == null) {
                    log.warn("无法获取租户ID，跳过状态更新，日期: {}", date);
                    continue;
                }

                // 1. 获取合同标的时间在包含当前日期的绿电直连合同的机组
                List<Long> contractUnitIds = geContractMaintenanceService.getContractUnitIdsByDate(tenantId, date);
                if (contractUnitIds.isEmpty()) {
                    log.info("当前日期没有找到绿电直连合同的机组，跳过状态更新，日期: {}", date);
                    continue;
                }

                // 2. 获取当前导入数据涉及的机组ID
                Set<Long> importedUnitIds = getUnitIdsFromEntities(dateEntities);

                // 3. 根据机组IDs获取机组信息，过滤名称
                LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(GeUnitBasic::getId, contractUnitIds);
                List<GeUnitBasic> contractUnits = geUnitBasicService.getBaseMapper().selectList(wrapper);

                // 4. 根据名称查看是否是所有的机组，是所有机组改为已维护，否则改为部分维护
                Integer maintenanceStatus = determineMaintenanceStatus(contractUnits, importedUnitIds);

                // 构建更新参数
                DataMaintenanceUpdateDTO updateDTO = buildMaintenanceUpdateDTO(tenantId, date, fileImportEnum, maintenanceStatus);

                // 调用状态更新服务
                dataMaintenanceService.updateStatus(updateDTO);

                log.info("数据维护状态更新成功，租户ID: {}, 日期: {}, 类型: {}, 条数: {}",
                        tenantId, date, fileImportEnum.getFileType(), dateEntities.size());
            }

        } catch (Exception e) {
            log.error("更新数据维护状态失败，文件类型: {}, 错误信息: {}", fileImportEnum.getFileType(), e.getMessage(), e);
            // 不抛出异常，避免影响主要的导入流程
        }
    }

    /**
     * 按日期分组实体
     */
    private <T> Map<Date, List<T>> groupEntitiesByDate(List<T> entities) {
        return entities.stream().collect(Collectors.groupingBy(entity -> {
            if (entity instanceof GeUnitRealElectricity) {
                return ((GeUnitRealElectricity) entity).getInfoDate();
            } else if (entity instanceof GeUnitNodePrice) {
                return ((GeUnitNodePrice) entity).getInfoDate();
            } else if (entity instanceof GeUnitPowerPredict) {
                return ((GeUnitPowerPredict) entity).getInfoDate();
            }
            throw new IllegalArgumentException("不支持的实体类型: " + entity.getClass().getName());
        }));
    }

    /**
     * 从实体中获取租户ID
     */
    private <T> Long getTenantIdFromEntity(T entity) {
        if (entity instanceof GeUnitRealElectricity) {
            return ((GeUnitRealElectricity) entity).getTenantId();
        } else if (entity instanceof GeUnitNodePrice) {
            return ((GeUnitNodePrice) entity).getTenantId();
        } else if (entity instanceof GeUnitPowerPredict) {
            return ((GeUnitPowerPredict) entity).getTenantId();
        }
        return null;
    }

    /**
     * 构建数据维护更新DTO
     */
    private DataMaintenanceUpdateDTO buildMaintenanceUpdateDTO(Long tenantId, Date date, GeFileImportEnum fileImportEnum, Integer maintenanceStatus) {
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(tenantId);
        updateDTO.setDateDay(date);

        switch (fileImportEnum) {
            case UNIT_REAL_ELECTRICITY:
                updateDTO.setUnitRealElectricityStatus(maintenanceStatus);
                break;
            case UNIT_NODE_PRICE:
                updateDTO.setUnitNodePriceStatus(maintenanceStatus);
                break;
            case UNIT_POWER_PREDICT:
                updateDTO.setUnitPowerPredictStatus(maintenanceStatus);
                break;
            default:
                log.warn("未处理的文件导入类型: {}", fileImportEnum.getFileType());
                break;
        }

        return updateDTO;
    }

    // ======================= ImportExcelVO 创建方法 =======================

    /**
     * 创建成功导入结果
     */
    private ImportExcelVO createSuccessImportResult(int successCount, String dataTypeName) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(successCount);
        result.setFailedNum(0);

        ImportExcelDetailVO detail = new ImportExcelDetailVO();
        detail.setFilename(dataTypeName + "数据");
        detail.setMessage("导入成功，共处理 " + successCount + " 条记录");
        detail.setStatus(true);
        detail.setImportDate(new Date());
        detail.setInfoList(new ArrayList<>());
        detail.setSuccessNum(successCount);
        detail.setFailedNum(0);

        result.getDetailList().add(detail);
        return result;
    }

    /**
     * 创建错误导入结果
     */
    private ImportExcelVO createErrorImportResult(String errorMessage) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);

        ImportExcelDetailVO detail = new ImportExcelDetailVO();
        detail.setFilename("发电数据");
        detail.setMessage(errorMessage);
        detail.setStatus(false);
        detail.setImportDate(new Date());
        detail.setInfoList(new ArrayList<>());
        detail.setSuccessNum(0);
        detail.setFailedNum(1);

        result.getDetailList().add(detail);
        return result;
    }

    // ======================= 维护状态更新辅助方法 =======================

    /**
     * 从实体列表中提取机组ID
     */
    private <T> Set<Long> getUnitIdsFromEntities(List<T> entities) {
        return entities.stream().map(entity -> {
            if (entity instanceof GeUnitRealElectricity) {
                return ((GeUnitRealElectricity) entity).getUnitId();
            } else if (entity instanceof GeUnitNodePrice) {
                return ((GeUnitNodePrice) entity).getUnitId();
            } else if (entity instanceof GeUnitPowerPredict) {
                return ((GeUnitPowerPredict) entity).getUnitId();
            }
            throw new IllegalArgumentException("不支持的实体类型: " + entity.getClass().getName());
        }).collect(Collectors.toSet());
    }

    /**
     * 根据合同机组和导入机组确定维护状态
     */
    private Integer determineMaintenanceStatus(List<GeUnitBasic> contractUnits, Set<Long> importedUnitIds) {
        if (contractUnits.isEmpty()) {
            log.warn("合同机组列表为空，返回未维护状态");
            return MaintenanceStatusEnum.NOT_MAINTAINED.getStatus();
        }

        // 获取合同中所有机组的ID
        Set<Long> contractUnitIds = contractUnits.stream()
                .map(GeUnitBasic::getId)
                .collect(Collectors.toSet());

        // 计算交集，即已导入数据的合同机组
        Set<Long> importedContractUnits = new HashSet<>(contractUnitIds);
        importedContractUnits.retainAll(importedUnitIds);

        if (importedContractUnits.isEmpty()) {
            // 没有任何合同机组导入了数据
            return MaintenanceStatusEnum.NOT_MAINTAINED.getStatus();
        } else if (importedContractUnits.size() == contractUnitIds.size()) {
            // 所有合同机组都导入了数据
            return MaintenanceStatusEnum.FULLY_MAINTAINED.getStatus();
        } else {
            // 部分合同机组导入了数据
            return MaintenanceStatusEnum.PARTIALLY_MAINTAINED.getStatus();
        }
    }
}
