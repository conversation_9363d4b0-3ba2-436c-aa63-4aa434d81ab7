package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeContractMaintenance;

import java.util.Date;
import java.util.List;

/**
 * 绿电直连合同数据维护 Service 接口
 */
public interface GeContractMaintenanceService extends IService<GeContractMaintenance> {

    /**
     * 新增合同数据
     *
     * @param param 合同数据
     */
    void insertContract(GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param 合同数据
     */
    void deleteContract(IdDTO param);

    /**
     * 获取发电合同列表
     *
     * @param param 合同数据
     * @return 合同数据列表
     */
    List<GeContractMaintenanceVO> contractList(GeContractMaintenanceDTO param);

    /**
     * 获取合同标的时间在包含当前日期的绿电直连合同的机组
     *
     * @param tenantId 租户ID
     * @param date     日期
     * @return 合同标的时间在包含当前日期的绿电直连合同的机组ID列表
     */
    List<Long> getContractUnitIdsByDate(Long tenantId, Date date);

    /**
     * 根据机组id获取合同信息
     *
     * @param unitId 机组id
     * @return 合同信息
     */
    List<GeContractMaintenanceVO> getContractByUnitId(Long unitId);

    /**
     * 根据机组id列表获取合同信息
     *
     * @param unitIds 机组id列表
     * @return 合同信息
     */
    List<GeContractMaintenanceVO> getContractByUnitIds(List<Long> unitIds);

    /**
     * 获取发电合同列表分页
     *
     * @param param 合同数据
     * @return 合同数据列表
     */
    FxPage<GeContractMaintenanceVO> getPowerGenerationContractPage(GeContractMaintenanceDTO param);

    /**
     * 导出合同数据
     *
     * @param param 合同数据
     */
    List<GeContractMaintenanceExportVO> exportContract(GeContractMaintenanceDTO param);

}
