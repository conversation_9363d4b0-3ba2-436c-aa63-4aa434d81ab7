package com.fx.green.electricity.shanxi.service.service.retail.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.FxPage;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.converter.VppRetailContractsImportConverter;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.VppRetailContractsImportExcel;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsMapper;
import com.fx.green.electricity.shanxi.service.service.file.VppFileService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import com.fx.green.electricity.shanxi.service.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.IMPORT_EMPTY_ERROR;
import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.IMPORT_FILE_TYPE_ERROR;

/**
 * 零售合同管理 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppRetailContractsServiceImpl extends ServiceImpl<VppRetailContractsMapper, VppRetailContracts> implements VppRetailContractsService {

    @Resource
    private VppRetailContractsManageService vppRetailContractsManageService;

    @Resource
    private VppFileService vppFileService;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    public FxPage<VppRetailContractsVO> getRetailContractsPage(QueryVppRetailContractsDTO param) {
        try {
            // 1. 参数验证
            validateQueryParameters(param);

            // 2. 分页查询数据
            LambdaQueryWrapper<VppRetailContracts> queryWrapper = buildQueryWrapper(param);
            IPage<VppRetailContracts> pageResult = baseMapper.selectPage(new Page<>(param.getPage(), param.getPageSize()), queryWrapper);

            // 3. 转换为VO并构建FxPage
            return BeanCopyUtils.convertToFxPage(pageResult, VppRetailContractsVO.class);

        } catch (Exception e) {
            log.error("查询零售合同列表失败", e);
            return FxPage.page(new ArrayList<>(), 0L, param.getPage(), param.getPageSize());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importRetailContract(MultipartFile file, String runMonth) {
        try {
            // 1. 基础校验
            validateImportParameters(file, runMonth);

            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

            // 2. 确保今年所有月份的零售合同记录存在
            ensureCurrentYearRecordsExist(tenantId, runMonth);

            // 3. 获取基础数据
            List<VppRetailContractsVO> retailContracts = getRetailContractsByMonth(tenantId, runMonth);
            Map<String, List<VppLoadUserVO.TreeUserVO>> userNameMap = getUserNameMap(tenantId, runMonth);

            // 4. 解析Excel文件
            List<VppRetailContractsManageVO> contractDataList = parseExcelFile(file, runMonth, retailContracts, userNameMap, tenantId);

            // 5. 数据验证
            validateContractData(contractDataList, userNameMap);

            // 6. 上传文件并保存数据
            saveContractData(file, contractDataList, retailContracts.get(0).getId(), tenantId);

        } catch (FxServiceException e) {
            log.error("零售合同导入业务异常: {}", e.getMessage());
            throw new FxServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("零售合同导入系统异常", e);
            throw new FxServiceException("导入失败，系统异常，请稍后重试");
        }
    }

    @Override
    public List<VppRetailContractsVO> getRetailContractsList(QueryVppRetailContractsDTO param) {
        // 1. 参数验证
        validateQueryParameters(param);

        // 2. 查询数据
        LambdaQueryWrapper<VppRetailContracts> queryWrapper = buildQueryWrapper(param);
        List<VppRetailContracts> list = baseMapper.selectList(queryWrapper);
        return BeanCopyUtils.copyList(list, VppRetailContractsVO.class);
    }


    /**
     * 校验导入参数
     */
    private void validateImportParameters(MultipartFile file, String runMonth) {
        if (file == null || file.isEmpty()) {
            throw new FxServiceException(IMPORT_EMPTY_ERROR);
        }

        if (!FileTypeUtil.checkExcelFile(file)) {
            throw new FxServiceException(IMPORT_FILE_TYPE_ERROR);
        }

        if (ObjectUtil.isEmpty(runMonth)) {
            throw new FxServiceException("运行月份不能为空");
        }
    }

    /**
     * 获取指定月份的零售合同数据
     *
     * @param tenantId 租户ID
     * @param runMonth 运行月份
     * @return 零售合同数据
     */
    private List<VppRetailContractsVO> getRetailContractsByMonth(Long tenantId, String runMonth) {
        QueryVppRetailContractsDTO param = new QueryVppRetailContractsDTO();
        param.setTenantId(tenantId);
        param.setStartDate(DateUtil.parseDate(runMonth + "-01"));
        param.setEndDate(DateUtil.endOfMonth(DateUtil.parseDate(runMonth + "-01")));


        List<VppRetailContractsVO> records = getRetailContractsList(param);

        if (ObjectUtil.isEmpty(records)) {
            throw new FxServiceException("虚拟电厂不存在");
        }

        return records;
    }

    /**
     * 获取用户名称映射
     *
     * @param tenantId 租户ID
     * @param runMonth 运行月份
     * @return 用户名称映射
     */
    private Map<String, List<VppLoadUserVO.TreeUserVO>> getUserNameMap(Long tenantId, String runMonth) {
        AdjustDeclareDTO.QueryUserTreeDTO userTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
        userTreeDTO.setQueryDate(DateUtil.parseDate(runMonth + "-01"));
        userTreeDTO.setTenantId(tenantId);

        // 查询用户树形结构列表
        VppLoadUserVO.TreeVO data = vppLoadUserService.queryTreeList(userTreeDTO);
        if (ObjectUtil.isNull(data) || ObjectUtil.isEmpty(data.getTreeUserVOList())) {
            return new HashMap<>();
        }

        // 将用户名称映射到用户列表
        return data.getTreeUserVOList().stream()
                .collect(Collectors.groupingBy(VppLoadUserVO.TreeUserVO::getName));
    }

    /**
     * 解析Excel文件
     *
     * @param file            文件
     * @param runMonth        运行月份
     * @param retailContracts 零售合同数据
     * @param userNameMap     用户名称映射
     * @param tenantId        租户ID
     * @return 零售合同管理VO列表
     */
    private List<VppRetailContractsManageVO> parseExcelFile(MultipartFile file, String runMonth,
                                                            List<VppRetailContractsVO> retailContracts,
                                                            Map<String, List<VppLoadUserVO.TreeUserVO>> userNameMap,
                                                            Long tenantId) {
        try (InputStream inputStream = file.getInputStream()) {
            // 使用EasyExcel读取导入实体
            List<VppRetailContractsImportExcel> importDataList = EasyExcelFactory.read(inputStream)
                    .head(VppRetailContractsImportExcel.class)
                    .sheet()
                    .headRowNumber(3) // 从第2行开始读取数据（第1-3行是表头）
                    .doReadSync();

            if (ObjectUtil.isEmpty(importDataList)) {
                throw new FxServiceException("Excel文件中没有有效数据");
            }

            log.info("成功读取Excel数据，共{}行", importDataList.size());

            // 转换为业务实体
            return convertToManageVOList(importDataList, runMonth, retailContracts.get(0).getId(), userNameMap, tenantId);

        } catch (FxServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new FxServiceException("解析Excel文件失败，请检查文件格式: " + e.getMessage());
        }
    }

    /**
     * 将导入实体列表转换为业务实体列表
     *
     * @param importDataList    导入实体列表
     * @param runMonth          运行月份
     * @param retailContractsId 零售合同ID
     * @param userNameMap       用户名称映射
     * @param tenantId          租户ID
     * @return 零售合同管理VO列表
     */
    private List<VppRetailContractsManageVO> convertToManageVOList(
            List<VppRetailContractsImportExcel> importDataList,
            String runMonth,
            Long retailContractsId,
            Map<String, List<VppLoadUserVO.TreeUserVO>> userNameMap,
            Long tenantId) {

        List<VppRetailContractsManageVO> dataList = new ArrayList<>();

        for (int i = 0; i < importDataList.size(); i++) {
            VppRetailContractsImportExcel importExcel = importDataList.get(i);
            int rowNumber = i + 4; // Excel行号从4开始（第1-3行是表头）

            try {
                // 数据验证
                VppRetailContractsImportConverter.validateImportData(importExcel);

                // 转换为业务实体
                VppRetailContractsManageVO manageVO = VppRetailContractsImportConverter.convertToManageVO(
                        importExcel, runMonth, retailContractsId, userNameMap, tenantId);

                if (manageVO != null) {
                    dataList.add(manageVO);
                    log.debug("成功转换第{}行数据，用户: {}", rowNumber, importExcel.getUserName());
                } else {
                    log.debug("跳过第{}行数据（已解约或非分时合同），用户: {}", rowNumber, importExcel.getUserName());
                }

            } catch (Exception e) {
                log.warn("处理第{}行数据失败: {}，用户: {}", rowNumber, e.getMessage(),
                        importExcel != null ? importExcel.getUserName() : "未知");
                // 继续处理其他行，不中断整个导入过程
            }
        }

        log.info("成功转换{}条有效数据", dataList.size());
        return dataList;
    }

    /**
     * 验证合同数据
     *
     * @param contractDataList 合同数据列表
     * @param userNameMap      用户名称映射
     */
    private void validateContractData(List<VppRetailContractsManageVO> contractDataList,
                                      Map<String, List<VppLoadUserVO.TreeUserVO>> userNameMap) {
        if (ObjectUtil.isEmpty(contractDataList)) {
            throw new FxServiceException("没有有效的合同数据可导入");
        }

        // 检查用户名称是否匹配
        Map<String, List<VppRetailContractsManageVO>> contractUserMap = contractDataList.stream()
                .collect(Collectors.groupingBy(VppRetailContractsManageVO::getName));

        if (userNameMap.size() != contractUserMap.size()) {
            throw new FxServiceException("导入失败，零售合同用户名称不符，请修改后重试！");
        }
    }

    /**
     * 保存合同数据
     *
     * @param file              文件
     * @param contractDataList  合同数据列表
     * @param retailContractsId 零售合同ID
     * @param tenantId          租户ID
     */
    private void saveContractData(MultipartFile file,
                                  List<VppRetailContractsManageVO> contractDataList,
                                  Long retailContractsId,
                                  Long tenantId) {
        try {
            // 上传文件
            VppFileUploadVO fileUploadResult = vppFileService.uploadFile(file);

            // 构建导入记录DTO
            QueryImportRecordDTO importRecordDTO = new QueryImportRecordDTO();
            importRecordDTO.setList(contractDataList);
            importRecordDTO.setName(fileUploadResult.getName());
            importRecordDTO.setUrl(fileUploadResult.getUrl());
            importRecordDTO.setRetailContractsId(retailContractsId);
            importRecordDTO.setTenantId(tenantId);

            // 维护文件列表
            UpdateWrapper<VppRetailContracts> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(VppRetailContracts::getAnnex, importRecordDTO.getName());
            updateWrapper.lambda().set(VppRetailContracts::getUrl, importRecordDTO.getUrl());
            updateWrapper.lambda().set(VppRetailContracts::getStatus, 1);
            updateWrapper.lambda().set(VppRetailContracts::getImportDate, new Date());
            updateWrapper.lambda().eq(VppRetailContracts::getId, importRecordDTO.getRetailContractsId());
            this.update(updateWrapper);
            // 文件导入入库
            List<VppRetailContractsManageVO> resultList = importRecordDTO.getList();
            vppRetailContractsManageService.addList(resultList, importRecordDTO);

        } catch (Exception e) {
            log.error("保存合同数据失败", e);
            throw new FxServiceException("保存合同数据失败，请稍后重试");
        }
    }

    /**
     * 验证查询参数
     *
     * @param param 查询参数
     */
    private void validateQueryParameters(QueryVppRetailContractsDTO param) {
        if (param == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        if (param.getTenantId() == null) {
            throw new IllegalArgumentException("租户ID不能为空");
        }

        if (param.getStartDate() == null || param.getEndDate() == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        if (param.getStartDate().after(param.getEndDate())) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }

    /**
     * 确保今年所有月份的零售合同记录存在
     *
     * @param tenantId 租户ID
     * @param runMonth 运行月份 (格式: yyyy-MM)
     * @return 已存在的月份记录
     */
    @Transactional(rollbackFor = Exception.class)
    private void ensureCurrentYearRecordsExist(Long tenantId, String runMonth) {
        // 获取年份
        String year = runMonth.substring(0, 4);

        // 生成今年所有月份列表
        List<String> yearMonthList = TimeUtils.generateYearMonthList(year);

        // 批量查询已存在的月份记录
        Set<String> existingMonths = getExistingYearMonths(tenantId, yearMonthList);

        // 创建缺失的月份记录
        List<VppRetailContracts> missingRecords = new ArrayList<>();
        for (String month : yearMonthList) {
            if (!existingMonths.contains(month)) {
                VppRetailContracts contract = createMonthlyRecord(tenantId, month);
                missingRecords.add(contract);
            }
        }

        // 批量保存缺失的记录
        if (!missingRecords.isEmpty()) {
            saveBatch(missingRecords);
            log.info("为租户{}创建了{}年{}条月份记录", tenantId, year, missingRecords.size());
        } else {
            log.info("租户{}的{}年月份记录已全部存在", tenantId, year);
        }
    }


    /**
     * 获取已存在的年度月份记录
     *
     * @param tenantId  租户ID
     * @param monthList 月份列表
     * @return 已存在的月份记录集合
     */
    private Set<String> getExistingYearMonths(Long tenantId, List<String> monthList) {
        if (monthList.isEmpty()) {
            return new HashSet<>();
        }

        LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContracts::getTenantId, tenantId)
                .in(VppRetailContracts::getRunMonth, monthList)
                .eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER)
                .select(VppRetailContracts::getRunMonth);

        List<VppRetailContracts> existingRecords = baseMapper.selectList(queryWrapper);
        return existingRecords.stream()
                .map(VppRetailContracts::getRunMonth)
                .collect(Collectors.toSet());
    }


    /**
     * 创建月份记录
     *
     * @param tenantId 租户ID
     * @param month    月份 (格式: yyyy-MM-dd)
     * @return 月份记录对象
     */
    private VppRetailContracts createMonthlyRecord(Long tenantId, String month) {
        VppRetailContracts contract = new VppRetailContracts();
        contract.setStatus(0);
        contract.setRunMonth(month);
        contract.setDataItem("零售合同");
        contract.setIsDelete(0);
        contract.setTenantId(tenantId);
        return contract;
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return 查询条件对象
     */
    private LambdaQueryWrapper<VppRetailContracts> buildQueryWrapper(QueryVppRetailContractsDTO param) {
        LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();

        // 必要条件
        queryWrapper.eq(VppRetailContracts::getTenantId, param.getTenantId())
                .eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER);

        // 时间范围条件
        if (param.getStartDate() != null) {
            queryWrapper.ge(VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getStartDate()));
        }

        if (param.getEndDate() != null) {
            queryWrapper.le(VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getEndDate()));
        }

        // 状态条件
        if (param.getStatus() != null) {
            queryWrapper.eq(VppRetailContracts::getStatus, param.getStatus());
        }

        // 排序
        queryWrapper.orderByAsc(VppRetailContracts::getId);

        return queryWrapper;
    }

}
