package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitBasic;

import java.util.List;

/**
 * 机组信息维护 Service 接口
 */
public interface GeUnitBasicService extends IService<GeUnitBasic> {

    /**
     * 新增机组信息
     *
     * @param param 机组信息
     */
    void insertUnitBase(GeUnitBasicDTO param);

    /**
     * 删除机组信息
     *
     * @param param 机组信息
     */
    void deleteUnitBase(IdDTO param);

    /**
     * 根据租户 id 查询对应机组
     *
     * @param param 机组信息
     * @return 机组信息列表
     */
    List<GeUnitBasicVO> unitBaseList(IdDTO param);

    /**
     * 根据机组 id 列表查询机组信息
     *
     * @param unitIds 机组 id 列表
     * @return 机组信息列表
     */
    List<GeUnitBasicVO> getUnitBasicListByIds(List<Long> unitIds);
}
