package com.fx.green.electricity.shanxi.service.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitBasicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 机组信息维护
 */
@Api(tags = "机组信息维护")
@RestController
@RequestMapping("/geUnitBasic")
public class GeUnitBasicController {

    @Resource
    private GeUnitBasicService geUnitBasicService;

    @ApiOperation("新增机组信息")
    @PostMapping("/insertUnitBase")
    public DataResult<Void> insertUnitBase(@RequestBody GeUnitBasicDTO param) {
        geUnitBasicService.insertUnitBase(param);
        return DataResult.success();
    }

    @ApiOperation("删除机组信息")
    @PostMapping("/deleteUnitBase")
    public DataResult<Void> deleteUnitBase(@RequestBody IdDTO param) {
        geUnitBasicService.deleteUnitBase(param);
        return DataResult.success();
    }

    @ApiOperation("根据发电企业 id 查询对应机组")
    @PostMapping("/unitBaseList")
    public DataResult<List<GeUnitBasicVO>> unitBaseList(@RequestBody IdDTO param) {
        List<GeUnitBasicVO> geUnitBasicVOS = geUnitBasicService.unitBaseList(param);
        return DataResult.success(geUnitBasicVOS);
    }
}
