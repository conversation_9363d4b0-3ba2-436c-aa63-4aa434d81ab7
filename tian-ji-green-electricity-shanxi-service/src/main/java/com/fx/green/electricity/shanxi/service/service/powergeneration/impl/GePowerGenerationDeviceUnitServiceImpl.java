package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationDeviceUnit;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GePowerGenerationDeviceUnitMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationDeviceUnitService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发电设备和机组关联表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class GePowerGenerationDeviceUnitServiceImpl extends ServiceImpl<GePowerGenerationDeviceUnitMapper, GePowerGenerationDeviceUnit> implements GePowerGenerationDeviceUnitService {

    @Override
    public List<Long> getUnitIdsByDeviceId(Long deviceId) {
        if (Objects.isNull(deviceId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GePowerGenerationDeviceUnit::getDeviceId, deviceId);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());

        List<GePowerGenerationDeviceUnit> deviceUnitList = list(queryWrapper);
        return deviceUnitList.stream()
                .map(GePowerGenerationDeviceUnit::getUnitId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getDeviceIdsByUnitId(Long unitId) {
        if (Objects.isNull(unitId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GePowerGenerationDeviceUnit::getUnitId, unitId);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());

        List<GePowerGenerationDeviceUnit> deviceUnitList = list(queryWrapper);
        return deviceUnitList.stream()
                .map(GePowerGenerationDeviceUnit::getDeviceId)
                .collect(Collectors.toList());
    }

    @Override
    public List<GePowerGenerationDeviceUnit> getByEnterpriseId(Long enterpriseId) {
        if (Objects.isNull(enterpriseId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GePowerGenerationDeviceUnit::getEnterpriseId, enterpriseId);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());
        queryWrapper.orderByDesc(GePowerGenerationDeviceUnit::getCreateTime);

        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<GePowerGenerationDeviceUnit> deviceUnitList) {
        if (CollectionUtils.isEmpty(deviceUnitList)) {
            return;
        }

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        deviceUnitList.forEach(deviceUnit -> deviceUnit.setTenantId(tenantId));

        saveBatch(deviceUnitList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByDeviceId(Long deviceId) {
        if (Objects.isNull(deviceId)) {
            return;
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GePowerGenerationDeviceUnit::getDeviceId, deviceId);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());

        remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByUnitId(Long unitId) {
        if (Objects.isNull(unitId)) {
            return;
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GePowerGenerationDeviceUnit::getUnitId, unitId);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());

        remove(queryWrapper);
    }

    @Override
    public Map<Long, Integer> getUnitCountsByDeviceIds(List<Long> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<GePowerGenerationDeviceUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GePowerGenerationDeviceUnit::getDeviceId, deviceIds);
        queryWrapper.eq(GePowerGenerationDeviceUnit::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());

        List<GePowerGenerationDeviceUnit> deviceUnitList = list(queryWrapper);

        // 按设备ID分组统计数量
        Map<Long, Integer> countMap = deviceUnitList.stream()
                .collect(Collectors.groupingBy(
                        GePowerGenerationDeviceUnit::getDeviceId,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));

        // 确保所有传入的deviceId都有返回值，没有关联机组的设备返回0
        return deviceIds.stream()
                .collect(Collectors.toMap(
                        deviceId -> deviceId,
                        deviceId -> countMap.getOrDefault(deviceId, 0)
                ));
    }

}
