package com.fx.green.electricity.shanxi.service.controller.data;


import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 数据维护 Controller
 */
@Api(tags = "数据维护 - 数据维护")
@RestController
@RequestMapping("/dataMaintenance")
public class DataMaintenanceController {

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @ApiOperation("数据维护列表展示")
    @PostMapping("/queryList")
    public DataResult<List<DataMaintenanceVO>> queryList(@RequestBody @Valid DataMaintenanceDTO param) {
        return DataResult.success(dataMaintenanceService.queryList(param));
    }

    @ApiOperation("查询即将失效的用户")
    @PostMapping("/getExpireUserList")
    public DataResult<List<ExpireUserVO>> getExpireUserList(@RequestBody @Valid DataMaintenanceDTO param) {
        return DataResult.success(dataMaintenanceService.getExpireUserList(param));
    }

    @ApiOperation("获取生效的用户的数量")
    @PostMapping("/geUserCount")
    public DataResult<Integer> geUserCount(@RequestBody @Valid DataMaintenanceDTO param) {
        return DataResult.success(dataMaintenanceService.geUserCount(param));
    }

    @ApiOperation("修改状态")
    @PostMapping("/updateStatus")
    public DataResult<Void> updateStatus(@RequestBody @Valid DataMaintenanceUpdateDTO param) {
        dataMaintenanceService.updateStatus(param);
        return DataResult.success();
    }

}
