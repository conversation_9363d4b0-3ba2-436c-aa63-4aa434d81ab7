package com.fx.green.electricity.shanxi.service.controller.user;

import cn.hutool.core.bean.BeanUtil;
import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.UserAnalysisVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 负荷用户管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户管理 - 负荷用户管理")
@RequestMapping("/vppLoadUser")
public class VppLoadUserController {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @CommonNoRepeat
    @ApiOperation("保存或修改基础信息")
    @PostMapping("/saveOrUpdate")
    public DataResult<Long> saveOrUpdate(@RequestBody VppLoadUserDTO param) {
        return DataResult.success(vppLoadUserService.saveOrUpdate(param));
    }

    @ApiOperation("分页列表")
    @PostMapping("/pageList")
    public DataResult<FxPage<VppLoadUserVO>> pageList(@RequestBody VppUserDTO.SearchDTO param) {
        return DataResult.success(vppLoadUserService.pageList(param));
    }

    @ApiOperation("根据id删除负荷用户")
    @PostMapping("/delete")
    public DataResult<Void> delete(@RequestBody IdDTO param) {
        vppLoadUserService.delete(param);
        return DataResult.success();
    }

    @ApiOperation("查询负荷用户详情")
    @PostMapping("/findById")
    public DataResult<VppUserInfoVO> findById(@RequestBody IdDTO param) {
        return DataResult.success(vppLoadUserService.findById(param));
    }

    @ApiOperation("负荷用户树级列表-单日")
    @PostMapping("/queryTreeList")
    public DataResult<VppLoadUserVO.TreeVO> queryTreeList(@RequestBody AdjustDeclareDTO.QueryUserTreeDTO param) {
        return DataResult.success(vppLoadUserService.queryTreeList(param));
    }

//    @ApiOperation("负荷用户树级列表-多日查询")
//    @PostMapping("/queryTreeListByDate")
//    public DataResult<VppLoadUserVO.TreeVO> queryTreeListByDate(@RequestBody QueryDateDTO param) {
//        return DataResult.success(vppLoadUserService.queryTreeListByDate(param));
//    }

    @ApiOperation("登录查询用户信息")
    @PostMapping("/findUserName")
    public DataResult<VppLoadUserVO> findUserName(@RequestBody VppUserDTO param) {
        return DataResult.success(vppLoadUserService.findUserName(param));
    }

    @CommonNoRepeat
    @ApiOperation("修改openId")
    @PostMapping("/updateOpenId")
    public DataResult<Void> updateOpenId(@RequestBody VppUserDTO.OpenIdDTO param) {
        vppLoadUserService.updateOpenId(param);
        return DataResult.success();
    }

    @ApiOperation("根据用户名字查询信息")
    @PostMapping("/getAllList")
    public DataResult<List<VppLoadUserVO>> getAllList(@RequestBody GetAllListDTO getAllListDTO) {
        List<VppUser> allList = vppLoadUserService.getAllList(getAllListDTO);
        List<VppLoadUserVO> list = BeanUtil.copyToList(allList, VppLoadUserVO.class);
        return DataResult.success(list);
    }

    @ApiOperation("根据用户名字查询信息")
    @PostMapping("/getAllUserList")
    public DataResult<List<VppLoadUserVO>> getAllUserList(@RequestBody GetAllListDTO getAllListDTO) {
        List<VppUser> allList = vppLoadUserService.getAllUserList(getAllListDTO);
        List<VppLoadUserVO> list = BeanUtil.copyToList(allList, VppLoadUserVO.class);
        return DataResult.success(list);
    }

    @ApiOperation("重置密码")
    @PostMapping("/resetPassword")
    public DataResult<Void> resetPassword(@RequestBody VppUserDTO param) {
        vppLoadUserService.resetPassword(param);
        return DataResult.success();
    }

    @ApiOperation("查询用户或者户号的预测和实际电量")
    @PostMapping("/queryUserElectricity")
    public DataResult<UserAnalysisVO> queryUserElectricity(@RequestBody @Valid UserAnalysisDTO param) {
        return DataResult.success(vppLoadUserService.queryUserElectricity(param));
    }

}
