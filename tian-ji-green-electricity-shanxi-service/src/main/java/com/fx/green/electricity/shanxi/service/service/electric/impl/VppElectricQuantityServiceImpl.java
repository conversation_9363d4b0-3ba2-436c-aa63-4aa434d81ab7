package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.enums.BooleanEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.enums.DecimalPlaceEnum;
import com.fx.green.electricity.shanxi.api.enums.MaintenanceStatusEnum;
import com.fx.green.electricity.shanxi.api.enums.VppElectricQuantityTypeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.*;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricActualDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.WholesaleAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppBiddingClearVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;
import com.fx.green.electricity.shanxi.service.listener.VppElectricActualImportListener;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricQuantityMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.declare.SeElectricDeclareService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualMiddleService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.service.service.file.VppFileService;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.*;


/**
 * 实际用电量记录 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppElectricQuantityServiceImpl extends ServiceImpl<VppElectricQuantityMapper, VppElectricQuantity> implements VppElectricQuantityService {

    @Resource
    private VppElectricActualService vppElectricActualService;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    @Resource
    private SeElectricDeclareService seElectricDeclareService;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppElectricActualMiddleService vppElectricActualMiddleService;

    @Resource
    private VppBindCycleService vppBindCycleService;

    @Resource
    private VppFileService vppFileService;

    @Override
    public ImportExcelVO importActualElectricityConsumption(MultipartFile file) {
        // 1. 文件校验
        if (!validateFile(file)) {
            return createErrorResult(file.getOriginalFilename(), "文件校验失败");
        }

        // 2. 上传文件
        VppFileUploadVO vppFileUploadVO = vppFileService.uploadFile(file);
        if (vppFileUploadVO == null) {
            return createErrorResult(file.getOriginalFilename(), "文件上传失败");
        }

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        String url = vppFileUploadVO.getUrl();

        // 3. 使用 EasyExcel 监听器读取数据
        VppElectricActualImportListener listener = new VppElectricActualImportListener();
        try (InputStream inputStream = file.getInputStream()) {
            EasyExcelFactory.read(inputStream, VppElectricActualImportExcel.class, listener)
                    .sheet()
                    .headRowNumber(1) // 表头在第1行
                    .doRead();
        } catch (Exception e) {
            log.error("读取Excel文件异常: {}", e.getMessage(), e);
            return createErrorResult(file.getOriginalFilename(), "读取Excel数据失败: " + e.getMessage());
        }

        // 4. 检查是否有解析错误
        if (listener.hasErrors()) {
            log.warn("Excel数据解析存在错误: {}", listener.getErrorSummary());
            return createErrorResult(file.getOriginalFilename(), "数据校验失败: " + listener.getErrorSummary());
        }

        // 5. 获取解析后的数据
        List<VppElectricActualDTO> allData = listener.getAllData();
        if (allData.isEmpty()) {
            return createErrorResult(file.getOriginalFilename(), "没有解析到有效的电量数据");
        }

        // 6. 数据聚合处理
        List<VppElectricQuantityDTO> aggregatedData = aggregateData(allData, file.getOriginalFilename(), url, tenantId);
        log.info("[importActualElectricityConsumption][数据聚合完成，共{}个日期的数据]", aggregatedData.size());

        // 7. 执行导入
        ImportExcelDetailVO dataResult = importElectric(aggregatedData, 1);
        log.info("[importActualElectricityConsumption][导入完成，结果: 成功{}条，失败{}条]", dataResult.getSuccessNum(), dataResult.getFailedNum());

        // 8. 返回结果
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(dataResult.getSuccessNum());
        result.setFailedNum(dataResult.getFailedNum());
        return result;
    }

    /**
     * 批量导入实际数据
     * @param electricQuantityDTOS 电量数据DTO列表
     * @param source 数据来源 1: 导入 2: 灵狐推送
     * @return 导入结果
     */
    private ImportExcelDetailVO importElectric(List<VppElectricQuantityDTO> electricQuantityDTOS, Integer source) {
        ImportExcelDetailVO result = new ImportExcelDetailVO();
        result.setInfoList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(0);

        if (electricQuantityDTOS.isEmpty()) {
            result.setStatus(true);
            return result;
        }

        try {
            // 1. 预处理：批量查询已存在的数据，减少数据库查询次数
            Map<String, VppElectricQuantityVO> existingDataMap = batchQueryExistingData(electricQuantityDTOS);

            // 2. 批量处理所有日期的数据
            List<DataMaintenanceUpdateDTO> updateDTOs = new ArrayList<>();

            for (VppElectricQuantityDTO electricQuantityDTO : electricQuantityDTOS) {
                // 单日处理逻辑
                processSingleDay(electricQuantityDTO, source, updateDTOs, existingDataMap);
            }

            // 3. 批量导入实际数据
            ImportExcelDetailVO actualResult = vppElectricActualService.importVppElectricActualListBatch(electricQuantityDTOS, updateDTOs);

            result.setSuccessNum(actualResult.getSuccessNum());
            result.setFailedNum(actualResult.getFailedNum());
            result.setStatus(true);

            log.info("[importElectric][批量导入完成，成功{}条，失败{}条]", result.getSuccessNum(), result.getFailedNum());

        } catch (Exception e) {
            log.error("批量导入异常", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            result.setMessage("批量导入异常: " + e.getMessage());
            result.setStatus(false);
            result.setFailedNum(electricQuantityDTOS.size());
        }
        return result;
    }

    /**
     * 批量查询已存在的数据
     * @param electricQuantityDTOS 电量数据DTO列表
     * @return 已存在数据映射
     */
    private Map<String, VppElectricQuantityVO> batchQueryExistingData(List<VppElectricQuantityDTO> electricQuantityDTOS) {
        Map<String, VppElectricQuantityVO> existingDataMap = new HashMap<>();

        // 1. 按租户分组批量查询
        Map<Long, List<VppElectricQuantityDTO>> tenantGrouped = electricQuantityDTOS.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getTenantId));

        for (Map.Entry<Long, List<VppElectricQuantityDTO>> entry : tenantGrouped.entrySet()) {
            Long tenantId = entry.getKey();
            List<Date> dates = entry.getValue().stream()
                    .map(VppElectricQuantityDTO::getRunningDate)
                    .distinct()
                    .collect(Collectors.toList());

            // 2. 为每个日期单独查询
            for (Date date : dates) {
                LambdaQueryWrapper<VppElectricQuantity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VppElectricQuantity::getTenantId, tenantId);
                queryWrapper.eq(VppElectricQuantity::getRunningDate, date);
                queryWrapper.eq(VppElectricQuantity::getIsDelete, BooleanEnum.FALSE.getCode());
                queryWrapper.eq(VppElectricQuantity::getType, VppElectricQuantityTypeEnum.ACTUAL.getCode());

                VppElectricQuantity existing = baseMapper.selectOne(queryWrapper);
                if (existing != null) {
                    String key = tenantId + "_" + DateUtil.formatDate(existing.getRunningDate());
                    existingDataMap.put(key, BeanUtil.copyProperties(existing, VppElectricQuantityVO.class));
                }
            }
        }

        log.info("[batchQueryExistingData][批量查询已存在数据完成，找到{}条记录]", existingDataMap.size());
        return existingDataMap;
    }

    /**
     * 优化的单日处理逻辑
     * @param electricQuantityDTO 电量数据DTO
     * @param source 数据来源 1: 导入 2: 灵狐推送
     * @param updateDTOs 数据维护更新DTO列表
     * @param existingDataMap 已存在数据映射
     */
    private void processSingleDay(VppElectricQuantityDTO electricQuantityDTO, Integer source,
                                  List<DataMaintenanceUpdateDTO> updateDTOs,
                                  Map<String, VppElectricQuantityVO> existingDataMap) {

        String key = electricQuantityDTO.getTenantId() + "_" + DateUtil.formatDate(electricQuantityDTO.getRunningDate());
        VppElectricQuantityVO existing = existingDataMap.get(key);

        VppElectricQuantity vppElectricQuantity = new VppElectricQuantity();
        BeanUtil.copyProperties(electricQuantityDTO, vppElectricQuantity);

        // 1. 判断是否存在
        if (existing != null) {
            vppElectricQuantity.setId(existing.getId());
            baseMapper.updateById(vppElectricQuantity);
        } else {
            baseMapper.insert(vppElectricQuantity);
        }


        // 3. 处理实际电量数据
        electricQuantityDTO.getElectricActualList().forEach(ea -> {
            ea.setElectricId(vppElectricQuantity.getId());
            ea.setTimeFrame(ea.getTimeFrame().length() > 5 ? ea.getTimeFrame().substring(0, 5) : ea.getTimeFrame());
            ea.setTenantId(electricQuantityDTO.getTenantId());
        });

        // 4. 准备批量更新数据维护状态
        DataMaintenanceUpdateDTO updateDTO = buildDataMaintenanceUpdate(electricQuantityDTO, source);
        updateDTOs.add(updateDTO);

        // 5. 删除这一天的实际用电量
        DataMaintenanceQueryDTO dto = new DataMaintenanceQueryDTO();
        dto.setTenantId(electricQuantityDTO.getTenantId());
        dto.setDateDay(electricQuantityDTO.getRunningDate());
        deleteActualElectricityConsumption(dto);
    }

    @Override
    public List<ElectricityDetailVO> getActualElectricityConsumptionDetail(DataMaintenanceQueryDTO param) {
        // 查询虚拟电厂下负荷用户列表
        List<VppUser> vppUserLists = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
        // 绑定周期判断
        DateTime startDate = DateUtil.beginOfMonth(param.getDateDay());
        DateTime endDate = DateUtil.endOfMonth(param.getDateDay());
        List<VppUser> vppUserList = getUserListByTwoDate(startDate, endDate, vppUserLists);

        List<VppBiddingClearVO.ResolveLoad> resolveLoadList = vppElectricActualService.getListByTenantId(param.getTenantId(), param.getDateDay());
        Map<Long, List<VppBiddingClearVO.ResolveLoad>> resolveLoadMap = resolveLoadList.stream().collect(Collectors.groupingBy(VppBiddingClearVO.ResolveLoad::getUserId));
        List<ElectricityDetailVO> resultList = new ArrayList<>();
        vppUserList.forEach(vppUser -> {
            ElectricityDetailVO electricityDetailVO = new ElectricityDetailVO();
            electricityDetailVO.setName(vppUser.getName());
            List<VppBiddingClearVO.ResolveLoad> list = resolveLoadMap.get(vppUser.getId());
            if (list != null && !list.isEmpty()) {
                electricityDetailVO.setType(1);
                List<BigDecimal> valList = list.stream().map(VppBiddingClearVO.ResolveLoad::getVal).collect(Collectors.toList());
                BigDecimal reduce = valList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                valList.add(0, reduce);
                valList.replaceAll(e -> e.setScale(DecimalPlaceEnum.LOAD.length, RoundingMode.HALF_UP));
                List<String> elList = valList.stream().map(BigDecimal::toString).collect(Collectors.toList());
                elList.add(0, DateUtil.format(param.getDateDay(), "yyyy-MM-dd"));
                electricityDetailVO.setDataList(elList);
            } else {
                electricityDetailVO.setType(2);
            }
            electricityDetailVO.setDateDay(param.getDateDay());
            List<String> timeFrameList = new ArrayList<>(Arrays.asList(VppConstant.NINETY_SIX_TIMES));
            timeFrameList.add(0, "日总量");
            timeFrameList.add(0, "日期");
            electricityDetailVO.setTimeFrame(timeFrameList);
            resultList.add(electricityDetailVO);
        });
        return resultList;
    }

    public List<VppUser> getUserListByTwoDate(Date startDate, Date endDate, List<VppUser> listByTenantId) {
        Map<Long, VppUser> vppUserMap = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, u -> u));
        List<VppUserBindCycle> list = vppBindCycleService.findCycleList(new ArrayList<>(vppUserMap.keySet()));
        Map<Long, VppUser> resultMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(list)) {
            for (VppUserBindCycle cycle : list) {
                Date bindCycleStart = cycle.getBindCycleStart();
                Date bindCycleEnd = cycle.getBindCycleEnd();
                // 判断日期是否在范围内
                boolean start = DateUtil.isIn(startDate, bindCycleStart, bindCycleEnd);
                boolean end = DateUtil.isIn(endDate, bindCycleStart, bindCycleEnd);
                if (start || end) {
                    if (!resultMap.containsKey(cycle.getUserId())) {
                        VppUser vppUser = vppUserMap.get(cycle.getUserId());
                        resultMap.put(cycle.getUserId(), vppUser);
                    }
                }
            }
        }
        return new ArrayList<>(resultMap.values());
    }

    @Override
    public void deleteActualElectricityConsumption(DataMaintenanceQueryDTO param) {
        // 1. 删除 实际用电量合并
        vppElectricActualConvergeService.removeByParam(param.getDateDay(), param.getTenantId());

        // 2. 删除 实际用电量中位数
        vppElectricActualMiddleService.removeByParam(param.getDateDay(), param.getTenantId());

        // 3. 删除 实际用电量数据包含 24 点数据
        vppElectricActualService.removeByParam(param.getDateDay(), param.getTenantId());

        // 4. 修改 数据维护表数据状态
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(param.getTenantId());
        updateDTO.setDateDay(param.getDateDay());
        updateDTO.setRealElectricityStatus(MaintenanceStatusEnum.NOT_MAINTAINED.getStatus());
        updateDTO.setRealElectricitySources(0);
        dataMaintenanceService.updateStatus(updateDTO);
    }

    /**
     * 构建数据维护更新DTO
     * @param electricQuantityDTO 电量数据DTO
     * @param source 数据来源 1: 导入 2: 灵狐推送
     * @return 数据维护更新DTO
     */
    private DataMaintenanceUpdateDTO buildDataMaintenanceUpdate(VppElectricQuantityDTO electricQuantityDTO, Integer source) {
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setDateDay(electricQuantityDTO.getRunningDate());
        updateDTO.setTenantId(electricQuantityDTO.getTenantId());

        // 1. 获取系统中该日期该租户下的所有用户
        List<VppUser> userList = vppLoadUserService.getListByTenantId(electricQuantityDTO.getTenantId(), "");

        // 2. 绑定周期判断
        List<VppUser> listByTenantId = vppLoadUserService.getUserList(electricQuantityDTO.getRunningDate(), userList);
        List<String> systemUserNames = listByTenantId.stream()
                .map(VppUser::getName)
                .distinct()
                .collect(Collectors.toList());

        // 3. 获取导入数据中的用户列表
        List<String> importUserNames = electricQuantityDTO.getElectricActualList().stream()
                .map(VppElectricActualDTO::getName)
                .distinct()
                .collect(Collectors.toList());

        // 4. 判断状态值
        if (importUserNames.isEmpty()) {
            updateDTO.setRealElectricityStatus(MaintenanceStatusEnum.NOT_MAINTAINED.getStatus());  // 无数据
        } else if (new HashSet<>(importUserNames).containsAll(systemUserNames)) {
            updateDTO.setRealElectricityStatus(MaintenanceStatusEnum.FULLY_MAINTAINED.getStatus());  // 数据完整
        } else {
            updateDTO.setRealElectricityStatus(MaintenanceStatusEnum.PARTIALLY_MAINTAINED.getStatus());  // 数据部分缺失
        }

        updateDTO.setRealElectricitySources(source);
        return updateDTO;
    }

    @Override
    public VppElectricQuantityVO findByTimeAndType(VppElectricQuantityDTO electricQuantityDTO) {
        QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(electricQuantityDTO.getTenantId()), "veq.tenant_id", electricQuantityDTO.getTenantId());
        queryWrapper.eq("veq.running_date", electricQuantityDTO.getRunningDate());
        queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
        queryWrapper.eq("veq.type", electricQuantityDTO.getType());
        return baseMapper.findByWrapper(queryWrapper);
    }

    @Override
    public void delete(IdDTO idDTO) {
        baseMapper.deleteById(idDTO.getId());
    }

    @Override
    public FxPage<ImportExcelDetailPageListVO> queryImportRecord(VppElectricQuantityDTO.QueryDTO electricQuantityDTO) {
        if (ObjectUtil.isNull(electricQuantityDTO.getStartTime()) || ObjectUtil.isNull(electricQuantityDTO.getEndTime())) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }

        //获取用户信息
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(electricQuantityDTO.getTenantId(), "");
        List<VppUser> listByTenantIds = vppLoadUserService.getUserListByTwoDate(electricQuantityDTO.getStartTime(), electricQuantityDTO.getEndTime(), listByTenantId);
        List<Long> userIdList = listByTenantIds.stream().map(VppUser::getId).collect(Collectors.toList());
        Map<Long, String> userMaps = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getName));
        //获取实际用电量信息
        List<VppElectricActualConverge> vppElectricActualConverges = vppElectricActualConvergeService.queryElectricByUserIdLists(userIdList, electricQuantityDTO.getStartTime(), electricQuantityDTO.getEndTime(), electricQuantityDTO.getTenantId());
        Map<String, List<VppElectricActualConverge>> elecMap = vppElectricActualConverges.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));


        List<ImportExcelDetailPageListVO> list = new ArrayList<>();
        try {
            if (ObjectUtil.isNull(electricQuantityDTO.getStatus())) {
                //获取全部数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(electricQuantityDTO.getStartTime(), electricQuantityDTO.getEndTime());
                for (Date value : everyDay) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = value;

                    List<VppElectricActualConverge> list1 = elecMap.get(DateUtil.formatDate(date));
                    StringBuilder notes = new StringBuilder();
                    if (ObjectUtil.isNotNull(list1)) {
                        List<Long> idList = list1.stream().map(VppElectricActualConverge::getUserId).distinct().collect(Collectors.toList());
                        List<Long> missingIds = userIdList.stream().filter(o -> !idList.contains(o)).collect(Collectors.toList());
                        for (Long userIds : missingIds) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    } else {
                        for (Long userIds : userIdList) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    }


                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNotNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(vppElectricQuantity.getRunningDate(), "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename(vppElectricQuantity.getName());
                        importExcelDetailPageListVO.setStatus(1);
                        importExcelDetailPageListVO.setType(2);
                        importExcelDetailPageListVO.setUrl(vppElectricQuantity.getUrl());
                        importExcelDetailPageListVO.setImportDate(vppElectricQuantity.getUploadTime());
                        if (ObjectUtil.isNotEmpty(notes)) {
                            String string = notes.substring(1, notes.length()).toString();
                            importExcelDetailPageListVO.setNotes("没有实际用电量的用户:" + string);
                        }
                        list.add(importExcelDetailPageListVO);
                    } else {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename("");
                        importExcelDetailPageListVO.setStatus(0);
                        importExcelDetailPageListVO.setType(2);
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    //日前申报
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNotNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(vppRecentlyDeclared.getRunningDate(), "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename(vppRecentlyDeclared.getName());
                        excelDetailPageListVO.setStatus(1);
                        excelDetailPageListVO.setUrl(vppRecentlyDeclared.getUrl());
                        excelDetailPageListVO.setType(1);
                        excelDetailPageListVO.setImportDate(vppRecentlyDeclared.getUploadTime());
                        list.add(excelDetailPageListVO);
                    } else {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename("");
                        excelDetailPageListVO.setStatus(0);
                        excelDetailPageListVO.setType(1);
                        list.add(excelDetailPageListVO);
                    }


                }
            } else if (electricQuantityDTO.getStatus() == 0) {
                //获取未上传数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(electricQuantityDTO.getStartTime(), electricQuantityDTO.getEndTime());
                for (int i = 0; i < everyDay.size(); i++) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = everyDay.get(i);
                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename("");
                        importExcelDetailPageListVO.setStatus(0);
                        importExcelDetailPageListVO.setType(2);
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename("");
                        excelDetailPageListVO.setStatus(0);
                        excelDetailPageListVO.setType(1);
                        list.add(excelDetailPageListVO);
                    }

                }
            } else if (electricQuantityDTO.getStatus() == 1) {
                //获取上传数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(electricQuantityDTO.getStartTime(), electricQuantityDTO.getEndTime());
                for (int i = 0; i < everyDay.size(); i++) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = everyDay.get(i);
                    //绑定周期判断
                    List<VppElectricActualConverge> list1 = elecMap.get(DateUtil.formatDate(date));
                    StringBuilder notes = new StringBuilder();
                    if (ObjectUtil.isNotNull(list1)) {
                        List<Long> idList = list1.stream().map(VppElectricActualConverge::getUserId).distinct().collect(Collectors.toList());
                        List<Long> missingIds = userIdList.stream().filter(o -> !idList.contains(o)).collect(Collectors.toList());
                        for (Long userIds : missingIds) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    } else {
                        for (Long userIds : userIdList) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    }


                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNotNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(vppElectricQuantity.getRunningDate(), "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename(vppElectricQuantity.getName());
                        importExcelDetailPageListVO.setUrl(vppElectricQuantity.getUrl());
                        importExcelDetailPageListVO.setStatus(1);
                        importExcelDetailPageListVO.setType(2);
                        if (ObjectUtil.isNotEmpty(notes)) {
                            String string = notes.substring(1, notes.length());
                            importExcelDetailPageListVO.setNotes("没有实际用电量的用户:" + string);
                        }
                        importExcelDetailPageListVO.setImportDate(vppElectricQuantity.getUploadTime());
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", electricQuantityDTO.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNotNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(vppRecentlyDeclared.getRunningDate(), "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename(vppRecentlyDeclared.getName());
                        importExcelDetailPageListVO.setUrl(vppRecentlyDeclared.getUrl());
                        excelDetailPageListVO.setStatus(1);
                        excelDetailPageListVO.setType(1);
                        excelDetailPageListVO.setImportDate(vppRecentlyDeclared.getUploadTime());
                        list.add(excelDetailPageListVO);
                    }
                }
            }
        } catch (ParseException e) {
            log.error("解析日期错误");
        }
        FxPage fxPage = this.ListPage(electricQuantityDTO, list);
        return fxPage;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportExcelVO importRecord(MultipartFile file, String time) {
        log.info("[importRecord][开始导入日前申报数据，文件: {}, 日期: {}]", file.getOriginalFilename(), time);

        try {
            // 1. 参数校验
            validateImportRecordParams(file, time);

            // 2. 解析Excel数据
            List<VppElectricDeclareDTO> importList = parseElectricDeclareExcel(file, time);
            log.info("[importRecord][Excel解析完成，共解析{}条数据]", importList.size());

            // 3. 上传文件获取URL
            String fileUrl = uploadFileAndGetUrl(file);

            // 4. 构建电量数据DTO
            VppElectricQuantityDTO quantityDTO = buildElectricQuantityDTO(file, time, fileUrl, importList);

            // 5. 执行数据导入
            Long electricId = executeImportWithTransaction(quantityDTO);

            log.info("[importRecord][导入成功，电量记录ID: {}]", electricId);
            return createSuccessImportResult(file.getOriginalFilename());

        } catch (FxServiceException e) {
            log.error("[importRecord][业务异常 - 文件: {}, 错误: {}]", file.getOriginalFilename(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[importRecord][系统异常 - 文件: {}]", file.getOriginalFilename(), e);
            return createErrorImportResult(file.getOriginalFilename(), "系统异常: " + e.getMessage());
        }
    }

    /**
     * 校验导入参数
     */
    private void validateImportRecordParams(MultipartFile file, String time) {
        if (file == null || file.isEmpty()) {
            throw new FxServiceException(IMPORT_EMPTY_ERROR);
        }

        if (!FileTypeUtil.checkExcelFile(file)) {
            throw new FxServiceException(IMPORT_FILE_TYPE_ERROR);
        }

        if (ObjectUtil.isEmpty(time)) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }

        try {
            DateUtil.parse(time);
        } catch (Exception e) {
            throw new FxServiceException("时间格式错误，请使用正确的日期格式");
        }
    }

    /**
     * 解析日前申报Excel数据
     */
    private List<VppElectricDeclareDTO> parseElectricDeclareExcel(MultipartFile file, String time) {
        try (InputStream inputStream = file.getInputStream()) {
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            Date dateDay = DateUtil.parse(time);

            // 使用新的Excel导入实体类
            List<VppElectricDeclareImportExcel> excelDataList = EasyExcelFactory.read(inputStream)
                    .head(VppElectricDeclareImportExcel.class)
                    .sheet()
                    .headRowNumber(1)
                    .doReadSync();

            if (excelDataList.isEmpty()) {
                throw new FxServiceException("Excel文件中没有找到有效数据");
            }

            log.info("[parseElectricDeclareExcel][Excel解析完成，共{}条原始数据]", excelDataList.size());

            // 转换为VppElectricDeclareDTO列表
            List<VppElectricDeclareDTO> importList = new ArrayList<>();

            for (VppElectricDeclareImportExcel excelData : excelDataList) {
                // 数据校验
                validateExcelRowData(excelData);

                // 转换数据
                VppElectricDeclareDTO dto = convertToVppElectricDeclareDTO(excelData, dateDay, tenantId);
                importList.add(dto);
            }

            log.info("[parseElectricDeclareExcel][数据转换完成，共{}条有效数据]", importList.size());
            return importList;

        } catch (FxServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("[parseElectricDeclareExcel][解析Excel失败]", e);
            throw new FxServiceException("Excel文件解析失败: " + e.getMessage());
        }
    }

    /**
     * 校验Excel行数据
     */
    private void validateExcelRowData(VppElectricDeclareImportExcel excelData) {
        if (excelData.getSerialNumber() == null) {
            throw new FxServiceException("序号不能为空");
        }

        if (ObjectUtil.isEmpty(excelData.getTimeFrame())) {
            throw new FxServiceException("时段不能为空");
        }

        if (excelData.getElectricity() == null) {
            throw new FxServiceException("电量不能为空");
        }

        if (excelData.getElectricity().compareTo(BigDecimal.ZERO) < 0) {
            throw new FxServiceException("电量不能为负数");
        }
    }

    /**
     * 转换Excel数据为VppElectricDeclareDTO
     */
    private VppElectricDeclareDTO convertToVppElectricDeclareDTO(VppElectricDeclareImportExcel excelData,
                                                                 Date dateDay, Long tenantId) {
        VppElectricDeclareDTO dto = new VppElectricDeclareDTO();

        // 处理时段格式
        String timeFrame = processTimeFrame(excelData.getTimeFrame());
        dto.setTimeFrame(timeFrame);

        // 电量单位转换：MW -> MWh (15分钟 = 0.25小时，所以MW * 0.25 = MWh)
        BigDecimal electricityMWh = excelData.getElectricity().multiply(new BigDecimal("0.25"));
        dto.setElectricity(electricityMWh);

        dto.setDateDay(dateDay);
        dto.setTenantId(tenantId);

        return dto;
    }

    /**
     * 处理时段格式
     */
    private String processTimeFrame(String timeFrame) {
        if (ObjectUtil.isEmpty(timeFrame)) {
            throw new FxServiceException("时间段不能为空");
        }

        // 如果是时间段格式 "00:00-00:15"，提取结束时间
        if (timeFrame.contains("-")) {
            String[] timeParts = timeFrame.split("-");
            if (timeParts.length != 2) {
                throw new FxServiceException("时间段格式错误: " + timeFrame);
            }
            return timeParts[1].trim();
        }

        // 如果已经是单一时间格式 "00:15"，直接返回
        return timeFrame.trim();
    }

    /**
     * 上传文件并获取URL
     */
    private String uploadFileAndGetUrl(MultipartFile file) {
        VppFileUploadVO uploadResult = vppFileService.uploadFile(file);
        if (uploadResult == null || ObjectUtil.isEmpty(uploadResult.getUrl())) {
            throw new FxServiceException("文件上传失败");
        }
        return uploadResult.getUrl();
    }

    /**
     * 构建电量数据DTO
     */
    private VppElectricQuantityDTO buildElectricQuantityDTO(MultipartFile file, String time,
                                                            String fileUrl, List<VppElectricDeclareDTO> importList) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

        VppElectricQuantityDTO quantityDTO = new VppElectricQuantityDTO();
        quantityDTO.setName(file.getOriginalFilename());
        quantityDTO.setUrl(fileUrl);
        quantityDTO.setType(VppElectricQuantityTypeEnum.DECLARE.getCode()); // 1-日前申报
        quantityDTO.setTenantId(tenantId);
        quantityDTO.setRunningDate(DateUtil.parse(time));
        quantityDTO.setUploadTime(new Date());
        quantityDTO.setStatus(1);
        quantityDTO.setElectricDeclareList(importList);

        return quantityDTO;
    }

    /**
     * 执行导入操作（带事务处理）
     */
    public Long executeImportWithTransaction(VppElectricQuantityDTO quantityDTO) {
        try {
            // 1. 删除旧数据
            removeOldElectricData(quantityDTO);

            // 2. 插入新的电量记录
            VppElectricQuantity electricQuantity = insertElectricQuantity(quantityDTO);

            // 3. 插入申报数据
            insertElectricDeclareData(quantityDTO.getElectricDeclareList(), electricQuantity.getId());

            // 4. 更新数据维护状态
            updateDataMaintenanceStatus(quantityDTO);

            return electricQuantity.getId();

        } catch (Exception e) {
            log.error("[executeImportWithTransaction][事务执行失败]", e);
            throw new FxServiceException("数据导入失败: " + e.getMessage());
        }
    }

    /**
     * 删除旧的电量数据
     */
    private void removeOldElectricData(VppElectricQuantityDTO quantityDTO) {
        VppElectricQuantityVO existing = this.findByTimeAndType(quantityDTO);
        if (existing != null) {
            log.info("[removeOldElectricData][删除旧数据，ID: {}]", existing.getId());
            this.delete(new IdDTO(existing.getId()));
            seElectricDeclareService.deleteByElectricId(existing.getId());
        }
    }

    /**
     * 插入电量记录
     */
    private VppElectricQuantity insertElectricQuantity(VppElectricQuantityDTO quantityDTO) {
        VppElectricQuantity electricQuantity = new VppElectricQuantity();
        BeanUtil.copyProperties(quantityDTO, electricQuantity);
        baseMapper.insert(electricQuantity);
        return electricQuantity;
    }

    /**
     * 插入申报数据
     */
    private void insertElectricDeclareData(List<VppElectricDeclareDTO> declareList, Long electricId) {
        if (ObjectUtil.isEmpty(declareList)) {
            return;
        }

        for (VppElectricDeclareDTO declareDTO : declareList) {
            declareDTO.setElectricId(electricId);
        }
        seElectricDeclareService.addElectricDeclareList(declareList);
    }

    /**
     * 更新数据维护状态
     */
    private void updateDataMaintenanceStatus(VppElectricQuantityDTO quantityDTO) {
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(quantityDTO.getTenantId());
        updateDTO.setDateDay(quantityDTO.getRunningDate());
        updateDTO.setDeclareStatus(1);
        updateDTO.setDeclareSources(2); // 2-导入来源
        dataMaintenanceService.updateStatus(updateDTO);
    }

    /**
     * 创建成功导入结果
     */
    private ImportExcelVO createSuccessImportResult(String filename) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(1);
        result.setFailedNum(0);
        return result;
    }

    /**
     * 创建错误导入结果
     */
    private ImportExcelVO createErrorImportResult(String filename, String errorMessage) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);

        ImportExcelDetailVO detail = new ImportExcelDetailVO();
        detail.setFilename(filename);
        detail.setMessage(errorMessage);
        detail.setStatus(false);
        detail.setImportDate(new Date());
        detail.setInfoList(new ArrayList<>());

        result.getDetailList().add(detail);
        return result;
    }

    @Override
    public List<VppElectricDeclareVO> getAllReportElectricity(WholesaleAnalysisDTO wholesaleAnalysisDTO) {
        return baseMapper.getAllReportElectricity(wholesaleAnalysisDTO);
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        return seElectricDeclareService.deleteRecordData(deleteRecordDTO);
    }

    @Override
    public DataResult<List<SeElectricDeclareVO>> downloadRecord(CommonDTO.DateDTO commonDTO) {
        LambdaQueryWrapper<VppElectricQuantity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppElectricQuantity::getTenantId, commonDTO.getTenantId());
        queryWrapper.eq(VppElectricQuantity::getRunningDate, commonDTO.getQueryDate());
        queryWrapper.eq(VppElectricQuantity::getIsDelete, 0);
        queryWrapper.eq(VppElectricQuantity::getType, 1);
        List<VppElectricQuantity> vppElectricQuantities = baseMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(vppElectricQuantities)) {
            Long id = vppElectricQuantities.get(0).getId();
            //获取日前申报数据详情
            List<SeElectricDeclareVO> list = seElectricDeclareService.getDataByElectricId(id);
            return DataResult.success(list);
        } else {
            return new DataResult<>();
        }
    }


    /**
     * 自定义分页分页
     */
    public FxPage ListPage(VppElectricQuantityDTO.QueryDTO electricQuantityDTO, List data) {
        Integer pageNum = electricQuantityDTO.getPage();
        Integer pageSize = electricQuantityDTO.getPageSize();
        // 记录总数
        Integer count = data.size();
        // 页数
        int pageCount;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        // 开始索引
        int fromIndex;
        // 结束索引
        int toIndex;
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }
        List pageList = new ArrayList<>();
        if (data.size() != 0) {
            pageList = data.subList(fromIndex, toIndex);
        }
        return FxPage.page(pageList, count, pageNum, pageSize);
    }

    /**
     * 传入两个时间范围，返回这两个时间范围内的所有日期，并保存在一个集合中
     *
     * @param beginTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static List<Date> findEveryDay(Date beginTime, Date endTime) throws ParseException {
        //创建一个放所有日期的集合
        List<Date> dates = new ArrayList();

        //将格式化后的第一天添加进集合
        dates.add(beginTime);
        //使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //传入起始时间将此日历设置为起始日历
        calBegin.setTime(beginTime);
        //判断结束日期是否在起始日历的日期之后
        while (endTime.after(calBegin.getTime())) {
            //根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //得到的每一天就添加进集合
            dates.add(calBegin.getTime());
            //如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }

    /**
     * 文件校验
     */
    private boolean validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("文件校验失败: 文件为空");
            return false;
        }

        if (!FileTypeUtil.checkExcelFile(file)) {
            log.warn("文件校验失败: 请上传xls或xlsx类型文件, 当前文件: {}", file.getOriginalFilename());
            return false;
        }

        return true;
    }

    /**
     * 数据聚合方法
     * @param dataList 原始数据列表
     * @param filename 文件名
     * @param url 文件URL
     * @param tenantId 租户ID
     * @return 电量数据DTO列表
     */
    private List<VppElectricQuantityDTO> aggregateData(List<VppElectricActualDTO> dataList,
                                                       String filename,
                                                       String url,
                                                       Long tenantId) {
        log.info("[aggregateData][开始优化聚合数据，原始数据量: {}]", dataList.size());

        // 1. 按日期分组
        Map<String, List<VppElectricActualDTO>> dateGroupedData = dataList.stream()
                .collect(Collectors.groupingBy(dto -> DateUtil.formatDate(dto.getDateDay())));

        // 2. 为每个日期创建 VppElectricQuantityDTO
        return dateGroupedData.entrySet().stream()
                .map(entry -> {
                    String dateStr = entry.getKey();
                    List<VppElectricActualDTO> dayData = entry.getValue();

                    // 创建 VppElectricQuantityDTO
                    VppElectricQuantityDTO quantityDTO = new VppElectricQuantityDTO();
                    quantityDTO.setName(filename);
                    quantityDTO.setType(VppElectricQuantityTypeEnum.ACTUAL.getCode());
                    quantityDTO.setUrl(url);
                    quantityDTO.setTenantId(tenantId);
                    quantityDTO.setUploadTime(new Date());
                    quantityDTO.setStatus(1);
                    quantityDTO.setRunningDate(DateUtil.parse(dateStr));
                    quantityDTO.setElectricActualList(dayData);
                    return quantityDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建错误结果
     */
    private ImportExcelVO createErrorResult(String filename, String message) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);

        ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
        importDetails.setFilename(filename);
        importDetails.setInfoList(new ArrayList<>());
        importDetails.setImportDate(new Date());
        importDetails.setStatus(false);
        importDetails.setMessage(message);
        result.getDetailList().add(importDetails);

        log.warn("[createErrorResult][导入失败 - 文件: {}, 原因: {}]", filename, message);
        return result;
    }

}
