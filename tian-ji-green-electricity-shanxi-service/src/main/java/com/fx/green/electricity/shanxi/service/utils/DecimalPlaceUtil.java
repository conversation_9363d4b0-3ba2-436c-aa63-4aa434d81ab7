package com.fx.green.electricity.shanxi.service.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fx.green.electricity.shanxi.api.enums.DecimalPlaceEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 小数位工具类，配合DecimalPlaceEnum枚举使用
 * 后期可以统一修改
 *
 * <AUTHOR>
 */
public class DecimalPlaceUtil {

    private DecimalPlaceUtil() {
    }

    /**
     * 字符串保留小数位并返回字符串(四舍五入)
     *
     * @param number           字符串小数
     * @param decimalPlaceEnum 小数位枚举
     * @return 格式化后的字符串
     */
    public static String formatForString(String number, DecimalPlaceEnum decimalPlaceEnum) {
        if (CharSequenceUtil.isBlank(number)) {
            return "";
        }
        BigDecimal bigDecimal = new BigDecimal(number).setScale(decimalPlaceEnum.length, RoundingMode.HALF_UP);
        return bigDecimal.toPlainString();
    }

    /**
     * bigDecimal 转 String 类型
     */
    public static String decimalToStr(BigDecimal number, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(number)) {
            return "";
        }
        BigDecimal bigDecimal = number.setScale(decimalPlaceEnum.length, RoundingMode.HALF_UP);
        return bigDecimal.toPlainString();
    }

    /**
     * BigDecimal 保留对应小数位
     */
    public static BigDecimal formatForDecimal(BigDecimal number, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(number)) {
            return null;
        }
        return number.setScale(decimalPlaceEnum.length, RoundingMode.HALF_UP);
    }

    /**
     * BigDecimal 保留对应小数位
     */
    public static BigDecimal strToDecimal(String number, DecimalPlaceEnum decimalPlaceEnum) {
        if (CharSequenceUtil.isBlank(number) || "null".equals(number)) {
            return null;
        }
        return new BigDecimal(number).setScale(decimalPlaceEnum.length, RoundingMode.HALF_UP);
    }

    /**
     * 除法，被除数为0返回0
     *
     * @param divisor          除数
     * @param dividend         被除数
     * @param decimalPlaceEnum 保留位数
     * @return 商
     * <AUTHOR>
     **/
    public static BigDecimal divide(BigDecimal divisor, BigDecimal dividend, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(divisor) || ObjectUtil.isNull(dividend)) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.compareTo(dividend) == 0) {
            return BigDecimal.ZERO;
        }

        return divisor.divide(dividend, decimalPlaceEnum.length, RoundingMode.DOWN);
    }

    /**
     * 加法
     *
     * @param addNum           BigDecimal
     * @param otherNum         BigDecimal
     * @param decimalPlaceEnum 小数位枚举
     * @return 和
     */
    public static BigDecimal add(BigDecimal addNum, BigDecimal otherNum, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(addNum) && ObjectUtil.isNull(otherNum)) {
            return null;
        }
        if (ObjectUtil.isNull(addNum) && ObjectUtil.isNotNull(otherNum)) {
            return formatForDecimal(otherNum, decimalPlaceEnum);
        }
        if (ObjectUtil.isNotNull(addNum) && ObjectUtil.isNull(otherNum)) {
            return formatForDecimal(addNum, decimalPlaceEnum);
        }
        return formatForDecimal(addNum.add(otherNum), decimalPlaceEnum);
    }

    /**
     * 加法计算（result = x + y）
     *
     * @param x 被加数（可为null）
     * @param y 加数 （可为null）
     * @return 和 （可为null）
     */
    public static BigDecimal add(BigDecimal x, BigDecimal y) {
        if (x == null && y == null) {
            return BigDecimal.ZERO;
        } else if (x == null) {
            return y;
        } else if (y == null) {
            return x;
        } else {
            return x.add(y);
        }
    }

    /**
     * 减法
     */
    public static BigDecimal sub(BigDecimal num, BigDecimal otherNum, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(num) && ObjectUtil.isNull(otherNum)) {
            return null;
        }
        if (ObjectUtil.isNull(num) && ObjectUtil.isNotNull(otherNum)) {
            return formatForDecimal(BigDecimal.ZERO.subtract(otherNum), decimalPlaceEnum);
        }
        if (ObjectUtil.isNotNull(num) && ObjectUtil.isNull(otherNum)) {
            return formatForDecimal(num.subtract(BigDecimal.ZERO), decimalPlaceEnum);
        }
        return formatForDecimal(num.subtract(otherNum), decimalPlaceEnum);
    }

    /**
     * 乘法
     */
    public static BigDecimal multiply(BigDecimal num, BigDecimal otherNum, DecimalPlaceEnum decimalPlaceEnum) {
        if (ObjectUtil.isNull(num) && ObjectUtil.isNull(otherNum)) {
            return null;
        }
        if (ObjectUtil.isNull(num) || ObjectUtil.isNull(otherNum)) {
            return null;
        }
        return formatForDecimal(num.multiply(otherNum), decimalPlaceEnum);
    }
}
