package com.fx.green.electricity.shanxi.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.VppElectricActualImportExcel;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricActualDTO;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实际用电量导入监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
public class VppElectricActualImportListener extends AnalysisEventListener<VppElectricActualImportExcel> {

    /**
     * 每隔1000条处理数据，防止内存溢出
     */
    private static final int BATCH_COUNT = 10000;

    /**
     * 缓存的数据
     */
    private final List<VppElectricActualDTO> cachedDataList = new ArrayList<>(BATCH_COUNT);

    /**
     * 错误信息列表
     */
    private final List<String> errorMessages = new ArrayList<>();

    /**
     * 时间段映射缓存
     */
    private static final Map<String, String> TIME_FRAME_MAPPING = new ConcurrentHashMap<>();

    /**
     * 市场成员名称最大长度
     */
    private static final int MAX_NAME_LENGTH = 100;

    /**
     * 电量数据的最大值（MWh）
     */
    private static final BigDecimal MAX_ELECTRICITY = new BigDecimal("99999.999");

    /**
     * 电量数据的最小值（MWh）
     */
    private static final BigDecimal MIN_ELECTRICITY = new BigDecimal("-99999.999");

    static {
        // 初始化时间段映射
        initTimeFrameMapping();
    }

    /**
     * 初始化时间段映射
     */
    private static void initTimeFrameMapping() {
        String[] timeFrames = {
                "00:15", "00:30", "00:45", "01:00", "01:15", "01:30", "01:45", "02:00",
                "02:15", "02:30", "02:45", "03:00", "03:15", "03:30", "03:45", "04:00",
                "04:15", "04:30", "04:45", "05:00", "05:15", "05:30", "05:45", "06:00",
                "06:15", "06:30", "06:45", "07:00", "07:15", "07:30", "07:45", "08:00",
                "08:15", "08:30", "08:45", "09:00", "09:15", "09:30", "09:45", "10:00",
                "10:15", "10:30", "10:45", "11:00", "11:15", "11:30", "11:45", "12:00",
                "12:15", "12:30", "12:45", "13:00", "13:15", "13:30", "13:45", "14:00",
                "14:15", "14:30", "14:45", "15:00", "15:15", "15:30", "15:45", "16:00",
                "16:15", "16:30", "16:45", "17:00", "17:15", "17:30", "17:45", "18:00",
                "18:15", "18:30", "18:45", "19:00", "19:15", "19:30", "19:45", "20:00",
                "20:15", "20:30", "20:45", "21:00", "21:15", "21:30", "21:45", "22:00",
                "22:15", "22:30", "22:45", "23:00", "23:15", "23:30", "23:45", "24:00"
        };

        for (String timeFrame : timeFrames) {
            String fieldName = "electricity" + timeFrame.replace(":", "");
            TIME_FRAME_MAPPING.put(fieldName, timeFrame);
        }
    }

    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(VppElectricActualImportExcel data, AnalysisContext context) {
        log.debug("[VppElectricActualImportListener][解析到一条数据：{}]", data);

        // 数据校验
        if (!validateData(data, context.readRowHolder().getRowIndex())) {
            return;
        }

        // 转换数据
        List<VppElectricActualDTO> convertedData = convertToDTO(data, context.readRowHolder().getRowIndex());
        cachedDataList.addAll(convertedData);

        // 达到BATCH_COUNT了，清理缓存防止内存溢出
        if (cachedDataList.size() >= BATCH_COUNT) {
            log.debug("[VppElectricActualImportListener][已缓存{}条数据]", cachedDataList.size());
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        if (!cachedDataList.isEmpty()) {
            log.info("[VppElectricActualImportListener][解析完成，最后批次数据{}条]", cachedDataList.size());
        }

        if (!errorMessages.isEmpty()) {
            log.warn("[VppElectricActualImportListener][导入过程中发现{}个错误]", errorMessages.size());
        }

        log.info("[VppElectricActualImportListener][所有数据解析完成！总计处理{}条记录]", cachedDataList.size());
    }

    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行。
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) {
        log.error("[VppElectricActualImportListener][解析失败，但是继续解析下一行：第{}行，错误信息：{}]",
                context.readRowHolder().getRowIndex(), exception.getMessage());

        errorMessages.add(String.format("第%d行解析失败：%s",
                context.readRowHolder().getRowIndex(), exception.getMessage()));
    }

    /**
     * 数据校验
     */
    private boolean validateData(VppElectricActualImportExcel data, Integer rowIndex) {
        ValidationResult result = new ValidationResult();
        List<String> errors = new ArrayList<>();

        // 1. 校验基础字段
        validateBasicFields(data, errors);

        // 2. 校验电量数据
        validateElectricityData(data, errors);

        // 3. 校验业务规则
        validateBusinessRules(data, errors);

        if (!errors.isEmpty()) {
            result.setValid(false);
            result.setErrorMessage(String.format("第%d行数据校验失败：%s", rowIndex, String.join("; ", errors)));
            result.setErrors(errors);
        } else {
            result.setValid(true);
        }

        if (!result.isValid()) {
            errorMessages.add(result.getErrorMessage());
            log.warn(result.getErrorMessage());
            return false;
        }

        return true;
    }

    /**
     * 转换为DTO对象
     */
    private List<VppElectricActualDTO> convertToDTO(VppElectricActualImportExcel excel, Integer rowIndex) {
        List<VppElectricActualDTO> result = new ArrayList<>();

        try {
            Field[] fields = VppElectricActualImportExcel.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().startsWith("electricity")) {
                    field.setAccessible(true);
                    BigDecimal electricity = (BigDecimal) field.get(excel);

                    if (electricity != null) {
                        String timeFrame = TIME_FRAME_MAPPING.get(field.getName());
                        if (timeFrame != null) {
                            VppElectricActualDTO dto = new VppElectricActualDTO();
                            dto.setName(excel.getName());
                            dto.setRegistered(excel.getRegistered());
                            dto.setMeteringCode(excel.getMeteringCode());
                            dto.setDateDay(excel.getDateDay());
                            dto.setTimeFrame(timeFrame);
                            dto.setElectricity(electricity);

                            result.add(dto);
                        }
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = String.format("[VppElectricActualImportListener][第%d行数据转换失败：%s]", rowIndex, e.getMessage());
            errorMessages.add(errorMsg);
            log.error(errorMsg, e);
        }

        return result;
    }

    /**
     * 获取所有解析的数据
     */
    public List<VppElectricActualDTO> getAllData() {
        return cachedDataList;
    }

    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errorMessages.isEmpty();
    }

    /**
     * 获取错误信息摘要
     */
    public String getErrorSummary() {
        if (errorMessages.isEmpty()) {
            return "无错误";
        }

        if (errorMessages.size() <= 5) {
            return String.join("; ", errorMessages);
        } else {
            List<String> firstFive = errorMessages.subList(0, 5);
            return String.join("; ", firstFive) + String.format("...等共%d个错误", errorMessages.size());
        }
    }

    /**
     * 校验基础字段
     */
    private static void validateBasicFields(VppElectricActualImportExcel data, List<String> errors) {
        // 校验市场成员名称
        if (StringUtils.isBlank(data.getName())) {
            errors.add("市场成员名称不能为空");
        } else if (data.getName().length() > MAX_NAME_LENGTH) {
            errors.add("市场成员名称长度不能超过" + MAX_NAME_LENGTH + "个字符");
        }

        // 校验户号
        if (StringUtils.isBlank(data.getRegistered())) {
            errors.add("户号不能为空");
        }

        // 校验日期
        if (data.getDateDay() == null) {
            errors.add("日期不能为空");
        }
    }

    /**
     * 校验电量数据
     */
    private static void validateElectricityData(VppElectricActualImportExcel data, List<String> errors) {
        try {
            // 使用反射检查所有电量字段
            java.lang.reflect.Field[] fields = VppElectricActualImportExcel.class.getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                if (field.getName().startsWith("electricity")) {
                    field.setAccessible(true);
                    BigDecimal electricity = (BigDecimal) field.get(data);

                    if (electricity != null) {
                        // 校验电量数值范围
                        if (electricity.compareTo(MIN_ELECTRICITY) < 0 || electricity.compareTo(MAX_ELECTRICITY) > 0) {
                            errors.add(String.format("电量数据超出有效范围[%s, %s]", MIN_ELECTRICITY, MAX_ELECTRICITY));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[VppElectricActualImportListener][校验电量数据时发生异常]", e);
            errors.add("电量数据格式错误");
        }

    }

    /**
     * 校验业务规则
     */
    private static void validateBusinessRules(VppElectricActualImportExcel data, List<String> errors) {
        // 可以添加更多业务相关的校验规则

        // 例如：校验计量点格式
        if (StringUtils.isNotBlank(data.getMeteringCode())) {
            if (data.getMeteringCode().length() > 50) {
                errors.add("计量点编码长度不能超过50个字符");
            }
        }

        // 例如：校验单位
        if (StringUtils.isNotBlank(data.getUnit()) && !"MWh".equalsIgnoreCase(data.getUnit().trim())) {
            log.warn("[VppElectricActualImportListener][电量单位不是MWh，当前单位：{}]", data.getUnit());
        }
    }

    /**
     * 校验结果类
     */
    @Data
    public static class ValidationResult {
        private boolean valid = true;
        private String errorMessage;
        private List<String> errors = new ArrayList<>();
    }
}
