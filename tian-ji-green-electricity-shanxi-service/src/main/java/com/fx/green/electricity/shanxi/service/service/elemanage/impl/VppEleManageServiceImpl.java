package com.fx.green.electricity.shanxi.service.service.elemanage.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fx.common.constant.DataResult;
import com.fx.data.gather.api.api.DataGatherVppApi;
import com.fx.data.gather.api.dto.vpp.VppUserCodeDataQueryDTO;
import com.fx.data.gather.api.dto.vpp.VppUserDataQueryDTO;
import com.fx.data.gather.api.vo.vpp.PubVppDataVO;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.enums.VppTimeSharingEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.VppPredictedElectricityConfirmDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserAccountVO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricityConfirm;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserAccount;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMapper;
import com.fx.green.electricity.shanxi.service.service.elemanage.VppEleManageService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityConfirmService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.service.user.VppUserAccountService;
import com.fx.green.electricity.shanxi.service.utils.PointConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 **/
@Slf4j
@Service
public class VppEleManageServiceImpl implements VppEleManageService {

    @Resource
    private DataGatherVppApi dataGatherVppApi;
    @Resource
    private VppLoadUserService vppLoadUserService;
    @Resource
    private VppPredictedElectricityConfirmService vppPredictedElectricityConfirmService;
    @Resource
    private VppElectricActualMapper vppElectricActualMapper;
    @Resource
    private VppUserAccountService vppUserAccountService;


    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(QueryUserTreeListDTO param) {
        VppLoadUserVO.TreeVO treeVO = new VppLoadUserVO.TreeVO();
        if (param.getType().equals(1)) {
            AdjustDeclareDTO.QueryUserTreeDTO queryUserTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
            queryUserTreeDTO.setTenantId(param.getTenantId());
            queryUserTreeDTO.setQueryDate(param.getStartDate());
            treeVO = vppLoadUserService.queryThreeFloorsTreeList(queryUserTreeDTO);
        } else if (param.getType().equals(2)) {
            AdjustDeclareDTO.QueryUserTreeDTO queryUserTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
            queryUserTreeDTO.setTenantId(param.getTenantId());
            queryUserTreeDTO.setQueryDate(param.getStartDate());
            treeVO = vppLoadUserService.queryThreeFloorsTreeAllList(queryUserTreeDTO);
        }
        if (ObjectUtil.isNotNull(param.getDimension())) {
            //查询用户是否修改了预测电量
            VppPredictedElectricityConfirmDTO vppPredictedElectricityConfirmDTO = new VppPredictedElectricityConfirmDTO();
            vppPredictedElectricityConfirmDTO.setDateDay(param.getStartDate());
            vppPredictedElectricityConfirmDTO.setTenantId(param.getTenantId());
            vppPredictedElectricityConfirmDTO.setDimension(param.getDimension());
            List<VppPredictedElectricityConfirm> list = vppPredictedElectricityConfirmService.getDataList(vppPredictedElectricityConfirmDTO);
            if (ObjectUtil.isNotEmpty(list)) {
                Map<Long, List<VppPredictedElectricityConfirm>> userMap = list.stream().collect(Collectors.groupingBy(VppPredictedElectricityConfirm::getUserId));
                for (VppLoadUserVO.TreeUserVO treeUserVO : treeVO.getTreeUserVOList()) {
                    Long id = treeUserVO.getId();
                    if (ObjectUtil.isNotNull(userMap.get(id))) {
                        treeUserVO.setIsUpdate(1);
                    } else {
                        treeUserVO.setIsUpdate(0);
                    }
                }
            }
        }
        return DataResult.success(treeVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<EleManageVO> queryEleManage(QueryEleManageDTO param) {
        EleManageVO eleManageVO = new EleManageVO();
        getDayData(param, eleManageVO);
        return DataResult.success(eleManageVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void getDayData(QueryEleManageDTO param, EleManageVO eleManageVO) {
        List<EleManageVO.PredictedElectricityInfo> dataList = new ArrayList<>();
        //获取实际用电量
        List<String> registeredList = new ArrayList<>();
        //查询用户户号
        if (ObjectUtil.isNotNull(param.getRegistered())) {
            registeredList.add(param.getRegistered());
            //组合用户户号数据
            comboUserData(dataList, param, registeredList);
        } else if(param.getUserId()!=null){
            List<VppUserAccountVO> userAccountList = vppUserAccountService.getUserAccount(param.getUserId());
            if (ObjectUtil.isNotEmpty(userAccountList)) {
                registeredList = userAccountList.stream().map(VppUserAccountVO::getConsNo).collect(Collectors.toList());
            }
            //组合用户户号数据
            comboUserData(dataList, param, registeredList);
        } else {
            //组合虚拟电厂数据
            comboVppData(dataList, param);
        }
        //放入时刻信息
        if(VppTimeSharingEnum.POINT_96.getCode() == param.getTimeSharing()){
            eleManageVO.setTimeFrame(new ArrayList<>(Arrays.asList(VppConstant.NINETY_SIX_TIMES)));
        }
        //如果查询维度是2-24点则按照24点聚合数据，否则不聚合
        if (param.getTimeSharing() == VppTimeSharingEnum.POINT_24.getCode()) {
            eleManageVO.setTimeFrame(new ArrayList<>(Arrays.asList(VppConstant.TWENTY_FOUR_TIMES)));
            for (EleManageVO.PredictedElectricityInfo predictedElectricityInfo : dataList) {
                List<BigDecimal> actualElectricityList = predictedElectricityInfo.getActualElectricityList();
                List<BigDecimal> dataGatherElectricityList = predictedElectricityInfo.getDataGatherElectricityList();
                List<BigDecimal> list3 = PointConvertUtil.make96To24(actualElectricityList, "SUM");
                List<BigDecimal> list4 = PointConvertUtil.make96To24(dataGatherElectricityList, "SUM");
                predictedElectricityInfo.setActualElectricityList(list3);
                predictedElectricityInfo.setDataGatherElectricityList(list4);
            }
            dataList = dataList.stream().sorted(Comparator.comparing(EleManageVO.PredictedElectricityInfo::getDateDay)).collect(Collectors.toList());
        } else {
            dataList = dataList.stream().sorted(Comparator.comparing(EleManageVO.PredictedElectricityInfo::getDateDay)).collect(Collectors.toList());
        }
        //查询维度为96点或者24点时计算总电量
        if(VppTimeSharingEnum.isGroupType15(param.getTimeSharing())){
            for (EleManageVO.PredictedElectricityInfo predictedElectricityInfo : dataList) {
                List<BigDecimal> actualElectricityList = predictedElectricityInfo.getActualElectricityList();
                if (ObjectUtil.isNotNull(actualElectricityList) && ObjectUtil.isNotEmpty(actualElectricityList)) {
                    Optional<BigDecimal> reduce = actualElectricityList.stream().filter(Objects::nonNull).reduce(BigDecimal::add);
                    if (reduce.isPresent() && (ObjectUtil.isNotEmpty(reduce.get()))) {
                        actualElectricityList.add(0, reduce.get().setScale(3, RoundingMode.HALF_UP));
                    }
                    predictedElectricityInfo.setActualElectricityList(actualElectricityList);
                }
                List<BigDecimal> dataGatherElectricityList = predictedElectricityInfo.getDataGatherElectricityList();
                if (ObjectUtil.isNotNull(dataGatherElectricityList) && ObjectUtil.isNotEmpty(dataGatherElectricityList)) {
                    Optional<BigDecimal> reduce = dataGatherElectricityList.stream().filter(Objects::nonNull).reduce(BigDecimal::add);
                    if (reduce.isPresent() && (ObjectUtil.isNotEmpty(reduce.get()))) {
                        dataGatherElectricityList.add(0, reduce.get().setScale(3, RoundingMode.HALF_UP));
                    }
                    predictedElectricityInfo.setDataGatherElectricityList(dataGatherElectricityList);
                }
            }
        }
        eleManageVO.setDataList(dataList);
    }

    /**
     * 按照租户获取关口表数据
     *
     * @param param 查询参数
     * @param registeredList 户号列表
     * @return 关口表数据
     */
    private Map<String,Map<String,List<PubVppDataVO>>> getRegisteredDataGatherMap(QueryEleManageDTO param, List<String> registeredList) {
        Map<String,Map<String,List<PubVppDataVO>>> returnMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(registeredList)) {
            for(String registered:registeredList){
                VppUserCodeDataQueryDTO vppDateListDataQueryDTO = new VppUserCodeDataQueryDTO();
                vppDateListDataQueryDTO.setUserCode(registered);
                //根据分时维度获取groupType
                vppDateListDataQueryDTO.setGroupType(VppTimeSharingEnum.getGroupTypeByCode(param.getTimeSharing()));
                vppDateListDataQueryDTO.setTenantId(param.getTenantId());
                vppDateListDataQueryDTO.setStartTime(DateUtil.beginOfDay(param.getStartDate()).toJdkDate());
                vppDateListDataQueryDTO.setEndTime(DateUtil.beginOfDay(param.getEndDate()).toJdkDate());
                //按照户号查询数据
                DataResult<List<PubVppDataVO>> dataResult = dataGatherVppApi.queryUserCodeElectricity(vppDateListDataQueryDTO);
                //将null替换为0
                if(dataResult.isSuccess() && dataResult.getData()!=null){
                    List<PubVppDataVO> dataList = dataResult.getData();
                    dataList.forEach(pubVppDataVO -> {
                        pubVppDataVO.setElectricity(pubVppDataVO.getElectricity()!=null ?
                                pubVppDataVO.getElectricity().setScale(3, RoundingMode.HALF_UP):BigDecimal.ZERO);
                    });
                    //返回的数据按照日期分组，再放入map
                    returnMap.put(registered,dataList.stream().collect(Collectors.groupingBy(PubVppDataVO::getDateStr)));
                }
            }
        }
        return returnMap;
    }

    /**
     * 按照租户获取虚拟电厂的关口表数据
     *
     * @param param 查询参数
     * @return 关口表数据
     */
    private List<PubVppDataVO> getTenantDataGatherList(QueryEleManageDTO param) {
        List<PubVppDataVO> dataGatherList = new ArrayList<>();
        DataResult<List<PubVppDataVO>> listDataResult;
        VppUserDataQueryDTO queryDTO = new VppUserDataQueryDTO();
        queryDTO.setTenantId(param.getTenantId());
        queryDTO.setGroupType(VppTimeSharingEnum.getGroupTypeByCode(param.getTimeSharing()));
        queryDTO.setStartTime(DateUtil.beginOfDay(param.getStartDate()).toJdkDate());
        queryDTO.setEndTime(DateUtil.beginOfDay(param.getEndDate()).toJdkDate());
        listDataResult = dataGatherVppApi.queryElectricity(queryDTO);
        if(listDataResult.isSuccess() && listDataResult.getData()!=null){
            List<PubVppDataVO> dataList = listDataResult.getData();
            dataList.forEach(pubVppDataVO -> {
                pubVppDataVO.setElectricity(pubVppDataVO.getElectricity()!=null ?
                        pubVppDataVO.getElectricity().setScale(3, RoundingMode.HALF_UP):BigDecimal.ZERO);
            });
            dataGatherList.addAll(dataList);
        }
        return dataGatherList;
    }
    /**
     * 组合用户数据放入dataList
     * @param dataList 数据列表
     * @param param 查询参数
     * @param registeredList 用户ID列表
     */
    private void comboUserData(List<EleManageVO.PredictedElectricityInfo> dataList,
                             QueryEleManageDTO param,
                             List<String> registeredList) {
        //户号没传入则不组装
        if(ObjectUtil.isEmpty(registeredList)){
            return;
        }
        //查询实际用电量
        List<VppElectricActualVO> actualEleList = vppElectricActualMapper.get96DataGroupByRegistered(param.getStartDate(),
                param.getEndDate(),param.getTenantId(), registeredList,param.getTimeSharing());
        //查询关口表电量
        Map<String,Map<String,List<PubVppDataVO>>> registeredDataGatherMap = getRegisteredDataGatherMap(param,registeredList);
        //根据户号查询负荷用户信息
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppUserAccount::getConsNo, registeredList.get(0));
        VppUserAccount vppUserAccount = vppUserAccountService.getOne(queryWrapper);
        VppUser vppUser = vppLoadUserService.getUser(vppUserAccount.getUserId());
        //将实际用电量按照日期+用户id+户号分组
        Map<String,List<VppElectricActualVO>> actualGroupMap = actualEleList.stream().collect(Collectors.groupingBy(
                actual->actual.getDateDay()+','+actual.getUserCode()+','+actual.getRegistered()));
        actualGroupMap.forEach((key,list)->{
            //关口表电量
            List<BigDecimal> dataGatherList = new ArrayList<>();
            //实际用电量
            List<BigDecimal> actualEleUserList = new ArrayList<>();
            String[] split = key.split(",");
            String dateDay = split[0];
            String name = vppUser.getName();
            String registered = split[2];
            EleManageVO.PredictedElectricityInfo predictedElectricityInfoUser = new EleManageVO.PredictedElectricityInfo();
            predictedElectricityInfoUser.setDateDay(DateUtil.parseDate(dateDay));
            predictedElectricityInfoUser.setRegistered(registered);
            predictedElectricityInfoUser.setName(name);
            predictedElectricityInfoUser.setType(2L);
            //按照时刻排序放入实际用电量
            if(ObjectUtil.isNotEmpty(list) && VppTimeSharingEnum.isGroupType15(param.getTimeSharing())){
                actualEleUserList = list.stream().sorted(Comparator.comparing(VppElectricActualVO::getTimeFrame)).map(VppElectricActualVO::getElectricity).collect(Collectors.toList());
            }else {
                actualEleUserList = list.stream().map(VppElectricActualVO::getElectricity).collect(Collectors.toList());
            }
            predictedElectricityInfoUser.setActualElectricityList(actualEleUserList);
            //按户号获取关口表数据
            Map<String,List<PubVppDataVO>> dateGatherMap = registeredDataGatherMap.get(registered);
            //如果是查询96点的数据则进行排序
            //否则直接放入list
            if(ObjectUtil.isNotEmpty(dateGatherMap)){
                List<PubVppDataVO> dateGatherUserList = dateGatherMap.get(dateDay);
                if(ObjectUtil.isNotEmpty(dateGatherUserList)){
                    if(VppTimeSharingEnum.isGroupType15(param.getTimeSharing())){
                        dataGatherList = dateGatherUserList.stream().sorted(Comparator.comparing(PubVppDataVO::getTimeFrame)).map(PubVppDataVO::getElectricity).collect(Collectors.toList());
                    }else {
                        dataGatherList = dateGatherUserList.stream().map(PubVppDataVO::getElectricity).collect(Collectors.toList());
                    }
                }
            }
            predictedElectricityInfoUser.setDataGatherElectricityList(dataGatherList);
            //返回时重新处理日期格式
            setYearMonthDateDay(param, key, predictedElectricityInfoUser);
            dataList.add(predictedElectricityInfoUser);
        });
    }

    /**
     * 组合虚拟电厂数据放入dataList
     * @param dataList 数据列表
     * @param param 查询参数
     */
    private void comboVppData(List<EleManageVO.PredictedElectricityInfo> dataList,
                               QueryEleManageDTO param) {
        //查询实际用电量
        List<VppElectricActualVO> actualEleList = vppElectricActualMapper.get96DataGroupByTenantId(param.getStartDate(),
                param.getEndDate(),param.getTenantId(),param.getTimeSharing());
        //查询关口表电量
        List<PubVppDataVO> tenantDataGatherList = getTenantDataGatherList(param);

        //将实际用电量按照日期分组
        Map<String,List<VppElectricActualVO>> actualGroupMap = actualEleList.stream().collect(Collectors.groupingBy(VppElectricActualVO::getDateDay));
        //将关口表电量按照日期分组
        Map<String,List<PubVppDataVO>> tenantDataGatherMap = tenantDataGatherList.stream().collect(Collectors.groupingBy(PubVppDataVO::getDateStr));
        actualGroupMap.forEach((key,list)->{
            //关口表电量
            List<BigDecimal> dataGatherList = new ArrayList<>();
            EleManageVO.PredictedElectricityInfo predictedElectricityInfoUser = new EleManageVO.PredictedElectricityInfo();
            predictedElectricityInfoUser.setDateDay(DateUtil.parseDate(key));
            predictedElectricityInfoUser.setType(1L);
            predictedElectricityInfoUser.setName("虚拟电厂");
            //按照时刻排序放入实际用电量
            List<BigDecimal> actualEleUserList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(list) && VppTimeSharingEnum.isGroupType15(param.getTimeSharing())) {
                actualEleUserList = list.stream().sorted(Comparator.comparing(VppElectricActualVO::getTimeFrame)).map(VppElectricActualVO::getElectricity).collect(Collectors.toList());
            }else {
                actualEleUserList = list.stream().map(VppElectricActualVO::getElectricity).collect(Collectors.toList());
            }
            predictedElectricityInfoUser.setActualElectricityList(actualEleUserList);
            //获取关口表数据
            List<PubVppDataVO> dateGatherUserList = tenantDataGatherMap.get(key);
            //如果是查询96点的数据则进行排序
            //否则直接放入list
            if(ObjectUtil.isNotEmpty(dateGatherUserList) && VppTimeSharingEnum.isGroupType15(param.getTimeSharing())){
                dataGatherList = dateGatherUserList.stream().sorted(Comparator.comparing(PubVppDataVO::getTimeFrame)).map(PubVppDataVO::getElectricity).collect(Collectors.toList());
            }else {
                dataGatherList = dateGatherUserList.stream().map(PubVppDataVO::getElectricity).collect(Collectors.toList());
            }
            predictedElectricityInfoUser.setDataGatherElectricityList(dataGatherList);
            //返回时重新处理日期格式
            setYearMonthDateDay(param, key, predictedElectricityInfoUser);
            dataList.add(predictedElectricityInfoUser);
        });
    }

    /**
     * 设置按年月查询返回的日期格式为，年yyyy月yyyy-MM
     * @param param 查询参数
     * @param key 日期字符串，格式为yyyy-MM-dd
     * @param predictedElectricityInfoUser 返回数据对象
     */
    private static void setYearMonthDateDay(QueryEleManageDTO param, String key, EleManageVO.PredictedElectricityInfo predictedElectricityInfoUser) {
        if(param.getTimeSharing() == VppTimeSharingEnum.MONTH.getCode()){
            predictedElectricityInfoUser.setDateDay(DateUtil.parse(key, DatePattern.NORM_MONTH_PATTERN));
        }else if(param.getTimeSharing() == VppTimeSharingEnum.YEAR.getCode()){
            predictedElectricityInfoUser.setDateDay(DateUtil.parse(key, DatePattern.NORM_YEAR_PATTERN));
        }
    }

    public static void main(String[] args) {
        int compare = DateUtil.compare(DateUtil.parseDate("2025-03-14"), DateUtil.parseDate(DateUtil.formatDate(new Date())));
        System.out.println(compare);

        if (new BigDecimal(1).compareTo(BigDecimal.ZERO) > 0) {
            System.out.println(1);
        } else {
            System.out.println(2);
        }
    }
}
