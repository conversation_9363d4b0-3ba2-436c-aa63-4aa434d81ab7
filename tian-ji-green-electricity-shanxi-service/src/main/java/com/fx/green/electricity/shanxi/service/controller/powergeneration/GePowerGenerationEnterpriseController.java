package com.fx.green.electricity.shanxi.service.controller.powergeneration;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationEnterpriseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发电企业信息 Controller
 */
@Api(tags = "发电企业信息")
@RestController
@RequestMapping("/gePowerGenerationEnterprise")
public class GePowerGenerationEnterpriseController {

    @Resource
    private GePowerGenerationEnterpriseService gePowerGenerationEnterpriseService;

    @CommonNoRepeat
    @ApiOperation("新增发电企业信息")
    @PostMapping("/create")
    public DataResult<Void> createPowerGenerationEnterprise(@RequestBody GePowerGenerationEnterpriseDTO param) {
        gePowerGenerationEnterpriseService.createPowerGenerationEnterprise(param);
        return DataResult.success();
    }

    @ApiOperation("删除发电企业信息")
    @PostMapping("/delete")
    public DataResult<Void> deletePowerGenerationEnterprise(@RequestBody IdDTO param) {
        gePowerGenerationEnterpriseService.deletePowerGenerationEnterprise(param);
        return DataResult.success();
    }

    @ApiOperation("获得发电企业信息分页")
    @PostMapping("/page")
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterprisePage(@RequestBody GePowerGenerationEnterpriseDTO param) {
        return DataResult.success(gePowerGenerationEnterpriseService.getPowerGenerationEnterprisePage(param));
    }

    @ApiOperation("获取所有发电企业和机组对应信息")
    @PostMapping("/list-all")
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterpriseList() {
        return DataResult.success(gePowerGenerationEnterpriseService.getPowerGenerationEnterpriseList());
    }

    @ApiOperation("获取企业机组树")
    @PostMapping("/unit-tree")
    public DataResult<List<GePowerGenerationEnterpriseUnitTreeVO>> getEnterpriseUnitTree(@RequestBody GePowerGenerationEnterpriseUnitTreeDTO param) {
        List<GePowerGenerationEnterpriseUnitTreeVO> result = gePowerGenerationEnterpriseService.getEnterpriseUnitTree(param);
        return DataResult.success(result);
    }

}
