package com.fx.green.electricity.shanxi.service.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户电量合同表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("vpp_user_electricity_contract")
public class VppUserElectricityContract extends TenantBaseEntity {

    /**
     * /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 合同开始时间
     */
    private Date contractStart;

    /**
     * 合同结束时间
     */
    private Date contractEnd;

    /**
     * 合同价格(关联机组中的价格的算术平均数)
     */
    private BigDecimal contractPrice;

}
