package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.util.MyDateUtil;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GeFileImportEnum;
import com.fx.green.electricity.shanxi.api.enums.MaintenanceStatusEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeFileRecord;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitNodePrice;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitPowerPredict;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitRealElectricity;
import com.fx.green.electricity.shanxi.service.mapper.data.GeFileRecordMapper;
import com.fx.green.electricity.shanxi.service.service.data.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件导入记录 Service 实现类
 */
@Service
public class GeFileRecordServiceImpl extends ServiceImpl<GeFileRecordMapper, GeFileRecord> implements GeFileRecordService {

    @Resource
    private GeUnitRealElectricityService geUnitRealElectricityService;

    @Resource
    private GeUnitPowerPredictService geUnitPowerPredictService;

    @Resource
    private GeUnitNodePriceService geUnitNodePriceService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertFileRecord(List<GeFileRecordDTO> param) {
        LambdaQueryWrapper<GeFileRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GeFileRecord::getFileType, param.get(0).getFileType());
        queryWrapper.in(GeFileRecord::getDataDate, param.stream().map(GeFileRecordDTO::getDataDate).collect(Collectors.toList()));
        queryWrapper.in(GeFileRecord::getBelongId, param.stream().map(GeFileRecordDTO::getBelongId).collect(Collectors.toList()));
        this.remove(queryWrapper);
        List<GeFileRecord> list = param.stream().map(t -> {
            GeFileRecord geFileRecord = new GeFileRecord();
            BeanUtil.copyProperties(t, geFileRecord);
            return geFileRecord;
        }).collect(Collectors.toList());
        this.saveBatch(list);
    }

    @Override
    public List<GeDayRecordVO> getFileRecordByDay(GeFileRecordDTO param) {
        // 1. 获取日期范围
        List<String> dateList = MyDateUtil.getDateRangeFormatByDate(
                DateUtil.beginOfMonth(param.getDataDate()),
                DateUtil.endOfMonth(param.getDataDate()), null);

        // 2. 从数据维护服务获取维护状态信息
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        DataMaintenanceDTO maintenanceDTO = new DataMaintenanceDTO();
        maintenanceDTO.setTenantId(tenantId);
        maintenanceDTO.setQueryMonth(param.getDataDate());
        List<DataMaintenanceVO> maintenanceList = dataMaintenanceService.queryList(maintenanceDTO);

        // 3. 将维护数据按日期索引
        Map<String, DataMaintenanceVO> maintenanceMap = maintenanceList.stream()
                .collect(Collectors.toMap(
                        vo -> DateUtil.formatDate(vo.getDateDay()),
                        vo -> vo,
                        (existing, replacement) -> existing // 处理重复键
                ));

        // 4. 构建结果列表
        List<GeDayRecordVO> resultList = new ArrayList<>(dateList.size());
        for (String date : dateList) {
            resultList.add(buildDayRecordFromMaintenance(date, maintenanceMap));
        }

        return resultList;
    }

    /**
     * 从数据维护信息构建单日记录
     *
     * @param date           日期字符串
     * @param maintenanceMap 维护状态映射
     * @return 单日记录
     */
    private GeDayRecordVO buildDayRecordFromMaintenance(String date, Map<String, DataMaintenanceVO> maintenanceMap) {
        GeDayRecordVO geDayRecordVO = new GeDayRecordVO();
        geDayRecordVO.setInfoDate(date);

        DataMaintenanceVO maintenanceVO = maintenanceMap.get(date);

        List<GeDayRecordVO.FileRecordVO> fileRecordList = new ArrayList<>();

        // 机组实际发电数据
        GeDayRecordVO.FileRecordVO realElectricityRecord = new GeDayRecordVO.FileRecordVO();
        realElectricityRecord.setFileType(GeFileImportEnum.UNIT_REAL_ELECTRICITY.getFileType());
        realElectricityRecord.setMaintenanceStatus(getMaintenanceStatus(maintenanceVO, "unitRealElectricity"));
        fileRecordList.add(realElectricityRecord);

        // 机组节点价格数据
        GeDayRecordVO.FileRecordVO nodePriceRecord = new GeDayRecordVO.FileRecordVO();
        nodePriceRecord.setFileType(GeFileImportEnum.UNIT_NODE_PRICE.getFileType());
        nodePriceRecord.setMaintenanceStatus(getMaintenanceStatus(maintenanceVO, "unitNodePrice"));
        fileRecordList.add(nodePriceRecord);

        // 机组功率预测数据
        GeDayRecordVO.FileRecordVO powerPredictRecord = new GeDayRecordVO.FileRecordVO();
        powerPredictRecord.setFileType(GeFileImportEnum.UNIT_POWER_PREDICT.getFileType());
        powerPredictRecord.setMaintenanceStatus(getMaintenanceStatus(maintenanceVO, "unitPowerPredict"));
        fileRecordList.add(powerPredictRecord);

        // 如果需要支持其他文件类型，可以继续添加
        // 实际用电量数据
        GeDayRecordVO.FileRecordVO tenantRealElectricityRecord = new GeDayRecordVO.FileRecordVO();
        tenantRealElectricityRecord.setFileType(GeFileImportEnum.TENANT_REAL_ELECTRICITY.getFileType());
        tenantRealElectricityRecord.setMaintenanceStatus(getMaintenanceStatus(maintenanceVO, "realElectricity"));
        fileRecordList.add(tenantRealElectricityRecord);

        // 日前申报数据
        GeDayRecordVO.FileRecordVO frontDayReportRecord = new GeDayRecordVO.FileRecordVO();
        frontDayReportRecord.setFileType(GeFileImportEnum.FRONT_DAY_REPORT.getFileType());
        frontDayReportRecord.setMaintenanceStatus(getMaintenanceStatus(maintenanceVO, "declare"));
        fileRecordList.add(frontDayReportRecord);

        geDayRecordVO.setFileRecordList(fileRecordList);
        return geDayRecordVO;
    }

    /**
     * 获取维护状态
     *
     * @param maintenanceVO 维护状态VO
     * @param statusType    状态类型
     * @return 维护状态
     */
    private Integer getMaintenanceStatus(DataMaintenanceVO maintenanceVO, String statusType) {
        if (maintenanceVO == null) {
            return MaintenanceStatusEnum.NOT_MAINTAINED.getStatus();
        }

        Integer status = null;
        switch (statusType) {
            case "unitRealElectricity":
                status = maintenanceVO.getUnitRealElectricityStatus();
                break;
            case "unitNodePrice":
                status = maintenanceVO.getUnitNodePriceStatus();
                break;
            case "unitPowerPredict":
                status = maintenanceVO.getUnitPowerPredictStatus();
                break;
            case "realElectricity":
                status = maintenanceVO.getRealElectricityStatus();
                break;
            case "declare":
                status = maintenanceVO.getDeclareStatus();
                break;
            default:
                break;
        }

        return status != null ? status : MaintenanceStatusEnum.NOT_MAINTAINED.getStatus();
    }


    @Override
    public List<GeFileRecordVO> fileStatus(GeFileRecordDTO param) {
        if (Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_REAL_ELECTRICITY.getFileType())
                || Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_NODE_PRICE.getFileType())
                || Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_POWER_PREDICT.getFileType())) {
            QueryWrapper<GeFileRecordDTO> wrapper = new QueryWrapper<>();
            wrapper.eq("gf.file_type", param.getFileType());
            wrapper.eq("gf.data_date", param.getDataDate());
            List<GeFileRecordVO> fileRecordByUnit = baseMapper.getFileRecordByUnit(wrapper);
            if (ObjectUtil.isEmpty(fileRecordByUnit)) {
                return Collections.emptyList();
            }
            fileRecordByUnit.forEach(t -> t.setStatus(ObjectUtil.isNotEmpty(t.getDataDate())));
            return fileRecordByUnit;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.TENANT_REAL_ELECTRICITY.getFileType())) {
            QueryWrapper<GeFileRecordDTO> wrapper = new QueryWrapper<>();
            wrapper.eq("gf.file_type", param.getFileType());
            wrapper.eq("gf.data_date", param.getDataDate());
            List<GeFileRecordVO> fileRecordByUnit = baseMapper.getFileRecordByTenant(wrapper);
            if (ObjectUtil.isEmpty(fileRecordByUnit)) {
                return Collections.emptyList();
            }
            fileRecordByUnit.forEach(t -> t.setStatus(ObjectUtil.isNotEmpty(t.getDataDate())));
            return fileRecordByUnit;
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getInfoDataValue(GeFileRecordDTO param) {
        if (Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_REAL_ELECTRICITY.getFileType())) {
            // 展示机组实际发电量
            LambdaQueryWrapper<GeUnitRealElectricity> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitRealElectricity::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitRealElectricity::getInfoDate, param.getDataDate());
            List<GeUnitRealElectricity> realList = geUnitRealElectricityService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(realList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("日期", DateUtil.formatDate(realList.get(0).getInfoDate()));
            realList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_NODE_PRICE.getFileType())) {
            // 展示机组节点价格
            LambdaQueryWrapper<GeUnitNodePrice> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitNodePrice::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitNodePrice::getInfoDate, param.getDataDate());
            List<GeUnitNodePrice> nodeList = geUnitNodePriceService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(nodeList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new LinkedHashMap<>();
            resultMap.put("日期", DateUtil.formatDate(nodeList.get(0).getInfoDate()));
            nodeList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.UNIT_POWER_PREDICT.getFileType())) {
            // 展示机组功率预测
            LambdaQueryWrapper<GeUnitPowerPredict> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitPowerPredict::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitPowerPredict::getInfoDate, param.getDataDate());
            List<GeUnitPowerPredict> powerList = geUnitPowerPredictService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(powerList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new LinkedHashMap<>();
            resultMap.put("日期", DateUtil.formatDate(powerList.get(0).getInfoDate()));
            powerList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.TENANT_REAL_ELECTRICITY.getFileType())) {
            //TODO 展示实际用电量，暂时使用旧的
            return null;
        }
        return null;
    }


}
