package com.fx.green.electricity.shanxi.service.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户电量合同机组关联表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("vpp_user_electricity_contract_unit")
public class VppUserElectricityContractUnit extends TenantBaseEntity {

    /**
     *
     * 编号
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 机组编号
     */
    private Long unitId;

}
