package com.fx.green.electricity.shanxi.service.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绿电直连合同维护 Controller
 */
@RestController
@Api(tags = "数据维护 - 绿电直连合同维护")
@RequestMapping("/geContractMaintenance")
public class GeContractMaintenanceController {

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @ApiOperation("新增合同数据")
    @PostMapping("/insertContract")
    public DataResult<Void> insertContract(@RequestBody GeContractMaintenanceDTO param) {
        geContractMaintenanceService.insertContract(param);
        return DataResult.success();
    }

    @ApiOperation("删除合同数据")
    @PostMapping("/deleteContract")
    public DataResult<Void> deleteContract(@RequestBody IdDTO param) {
        geContractMaintenanceService.deleteContract(param);
        return DataResult.success();
    }

    @ApiOperation("合同数据列表")
    @PostMapping("/contractList")
    public DataResult<List<GeContractMaintenanceVO>> contractList(@RequestBody GeContractMaintenanceDTO param) {
        List<GeContractMaintenanceVO> geContractMaintenanceVOS = geContractMaintenanceService.contractList(param);
        return DataResult.success(geContractMaintenanceVOS);
    }

    @ApiOperation("获取发电合同列表分页")
    @PostMapping("/page")
    public DataResult<FxPage<GeContractMaintenanceVO>> getPowerGenerationContractPage(@RequestBody GeContractMaintenanceDTO param) {
        FxPage<GeContractMaintenanceVO> page = geContractMaintenanceService.getPowerGenerationContractPage(param);
        return DataResult.success(page);
    }

    @ApiOperation("导出合同数据")
    @PostMapping("/export")
    public DataResult<List<GeContractMaintenanceExportVO>> exportContract(@RequestBody GeContractMaintenanceDTO param) {
        List<GeContractMaintenanceExportVO> exportList = geContractMaintenanceService.exportContract(param);
        return DataResult.success(exportList);
    }
}
