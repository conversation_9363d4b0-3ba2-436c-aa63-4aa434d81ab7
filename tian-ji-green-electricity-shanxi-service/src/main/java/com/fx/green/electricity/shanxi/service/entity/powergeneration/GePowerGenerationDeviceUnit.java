package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 发电设备和机组关联表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("发电设备和机组关联表")
public class GePowerGenerationDeviceUnit extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -525886433686176836L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("发电设备id")
    private Long deviceId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("租户id")
    private Long tenantId;

}
