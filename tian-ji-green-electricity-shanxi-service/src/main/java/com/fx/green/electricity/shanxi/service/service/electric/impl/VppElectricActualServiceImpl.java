package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.TimeConstant;
import com.fx.common.enums.BooleanEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.util.MyDateUtil;
import com.fx.green.electricity.shanxi.api.constant.PartitionTableConstant;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.other.VppPartitionTableService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 实际用电量数据 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppElectricActualServiceImpl extends ServiceImpl<VppElectricActualMapper, VppElectricActual> implements VppElectricActualService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppPartitionTableService vppPartitionTableService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    private static final Integer INSERT_SIZE = 1000;


    @Override
    public List<VppBiddingClearVO.ResolveLoad> getList(ResultAnalysisDTO param, Date dateDay, String startTime, String endTime) {
        return baseMapper.getList(param, dateDay, startTime, endTime);
    }

    @Override
    public List<VppElectricActualVO> getUserRealityPower(ResultAnalysisDTO param, String startTime, String endTime) {
        String month = DateUtil.format(param.getStartDate(), "yyyy-MM");
        return baseMapper.getUserRealityPower(param, startTime, endTime, month);
    }

    private QueryWrapper<VppElectricActualDTO> querySearch(VppBatteryInformationDTO param) {
        QueryWrapper<VppElectricActualDTO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sea.tenant_id", param.getTenantId());
        queryWrapper.eq("sea.is_delete", BooleanEnum.FALSE.getCode());
        if (param.getUserCodeList() != null && !param.getUserCodeList().isEmpty()) {
            queryWrapper.in("sea.user_code", param.getUserCodeList());
        }
        if (param.getSearchDate() != null && param.getSearchDate().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", param.getSearchDate().get(0));
            queryWrapper.le("sea.date_day", param.getSearchDate().get(1));
        }
        if (param.getSearchTime() != null && param.getSearchTime().size() == VppConstant.TWO_NUMBER) {
            switch (param.getType()) {
                case 96:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
                case 24:
                    queryWrapper.ge("stc.twenty_four", param.getSearchTime().get(0));
                    queryWrapper.le("stc.twenty_four", param.getSearchTime().get(1));
                    break;
                default:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
            }
        }
        if (param.getSelectDate() != null && !param.getSelectDate().isEmpty()) {
            queryWrapper.in("sea.date_day", param.getSelectDate());
        }
        if (param.getSelectTime() != null && param.getSelectTime().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", MyDateUtil.getMinMonthDate(param.getSelectTime().get(0)));
            queryWrapper.le("sea.date_day", MyDateUtil.getMaxMonthDate(param.getSelectTime().get(1)));
        }

        return queryWrapper;
    }


    @Override
    public List<VppElectricActualSimpleVO> electricListByDateTimeUser(VppBatteryInformationDTO param) {
        //获取绑定用户信息
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        List<VppUser> userList = new ArrayList<>();
        if (CollUtil.isNotEmpty(listByTenantId)) {
            //绑定周期判断
            userList = vppLoadUserService.getUserListByTwoDate(param.getSearchDate().get(0), param.getSearchDate().get(1), listByTenantId);
        }


        QueryWrapper<VppElectricActualDTO> queryWrapper = this.querySearch(param);
        if (ObjectUtil.isNotEmpty(userList)) {
            List<Long> userIdList = userList.stream().map(VppUser::getId).collect(Collectors.toList());
            queryWrapper.in("user_id", userIdList);
        }
        Date minMonthDate = null;
        Date maxMonthDate = null;
        if (param.getSearchDate() != null && param.getSearchDate().size() == 2) {
            minMonthDate = MyDateUtil.getMinMonthDate(param.getSearchDate().get(0));
            maxMonthDate = MyDateUtil.getMaxMonthDate(param.getSearchDate().get(1));
        }
        if (TimeConstant.NINETY_SIX_NUMBER.equals(param.getType())) {
            // 96个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "sea.time_frame");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "sea.time_frame");
            return baseMapper.selectElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        } else {
            // 24个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "stc.twenty_four");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "stc.twenty_four");
            return baseMapper.select24ElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        }
    }

    @Override
    public List<VppElectricActualVO> electricActualList(ResultAnalysisDTO param, String startTime, String endTime) {
        return baseMapper.electricActualList(param, startTime, endTime);
    }

    @Override
    public List<PieChartOfUserVO.RatioVO> getAllInfo(ControllableLoadDTO param, Long userId, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActualConverge> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActualConverge::getUserId, userIdList);
        return baseMapper.getAllInfo(param, userId, electricConvergeLqw);
    }

    @Override
    public BigDecimal getAllPower(ControllableLoadDTO param, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActual> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActual::getUserId, userIdList);
        return baseMapper.getAllPower(param, electricConvergeLqw);
    }

    @Override
    public List<VppElectricActualForUserVO> getElectricActual(Long userId, Date monthStart, Date monthEnd) {
        return baseMapper.getElectricActual(userId, monthStart, monthEnd);
    }

    @Override
    public List<VppElectricActualVO> electricActualAllList(ResultAnalysisDTO param, List<Long> userId, String startTime, String endTime) {
        return baseMapper.electricActualAllList(param, userId, startTime, endTime);
    }

    @Override
    public List<DividendVO.UserElectricSqlVO> electricActualAll(ResultAnalysisDTO resultAnalysisDTO) {
        return baseMapper.electricActualAll(resultAnalysisDTO);
    }

    @Override
    public String getUpdateTime(GetUpdateTimeDTO param) {
        return baseMapper.getUpdateTime(param);
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecList(ResultAnalysisDTO param, String startTime, String endTime) {
        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        List<VppElectricActualConverge> vppElectricActuals = vppElectricActualConvergeService.getElecList(param, startTime, endTime);
        for (VppElectricActualConverge actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecAllList(ResultAnalysisDTO param, String startTime,
                                                              String endTime, List<Long> userIdList, List<String> periodDetails) {
        long t = System.currentTimeMillis();

        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ObjectUtil.isNotEmpty(userIdList), VppElectricActual::getUserId, userIdList);
        queryWrapper.ge(VppElectricActual::getDateDay, param.getStartDate());
        queryWrapper.le(VppElectricActual::getDateDay, param.getEndDate());
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, endTime);
        queryWrapper.eq(VppElectricActual::getIsDelete, 0);
        if (ObjectUtil.isNull(startTime) && ObjectUtil.isNull(endTime) && ObjectUtil.isNotEmpty(periodDetails) && ObjectUtil.isNotNull(periodDetails)) {
            queryWrapper.in(ObjectUtil.isNotEmpty(periodDetails), VppElectricActual::getTimeFrame, periodDetails);
        }
        List<VppElectricActual> vppElectricActuals = baseMapper.selectList(queryWrapper);
        long l = System.currentTimeMillis() - t;
        System.out.println("获取全部用户的实际用电量" + l);
        for (VppElectricActual actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setUserId(actual.getUserId());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        long l1 = System.currentTimeMillis() - t;
        System.out.println("组装数据" + l1);
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getListByTenantId(Long tenantId, Date dateDay) {
        return baseMapper.getListByTenantId(tenantId, dateDay);
    }

    @Override
    public void removeByParam(Date dateDay, Long tenantId) {
        baseMapper.removeByParam(dateDay, tenantId);
        baseMapper.remove24ByParam(dateDay, tenantId);
    }

    @Override
    public Map<String, List<BigDecimal>> getActualTwentyFourForUser(Date startDay, Date endDay, Long tenantId, List<String> userCodes) {
        //获取用户信息
        List<VppUser> userINfoByUserCode = vppLoadUserService.getUserINfoByUserCode(userCodes, tenantId);
        Map<Long, String> userMap = userINfoByUserCode.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getUserCode));
        List<Long> userIdList = userINfoByUserCode.stream().map(VppUser::getId).collect(Collectors.toList());
        //获取实际用电量
        List<VppElectricActualVO> actualTwentyFourForUser = baseMapper.getActualTwentyFourForUser(startDay, endDay, tenantId, userIdList);

        Map<String, List<BigDecimal>> resultMap = new HashMap<>();
        Map<Long, List<VppElectricActualVO>> collect = actualTwentyFourForUser.stream().collect(Collectors.groupingBy(VppElectricActualVO::getUserId));
        for (Map.Entry<Long, List<VppElectricActualVO>> entry : collect.entrySet()) {
            Long key = entry.getKey();
            String s = userMap.get(key);
            List<BigDecimal> list = new ArrayList<>();
            List<VppElectricActualVO> vppElectricActualVOS = collect.get(key);
            vppElectricActualVOS = vppElectricActualVOS.stream().sorted(Comparator.comparing(VppElectricActualVO::getTimeFrame)).collect(Collectors.toList());
            for (VppElectricActualVO electricActualVO : vppElectricActualVOS) {
                BigDecimal electricity = electricActualVO.getElectricity();
                list.add(electricity);
            }
            resultMap.put(s, list);
        }
        System.out.println(resultMap);
        return resultMap;
    }


    @Override
    public ImportExcelDetailVO importVppElectricActualListBatch(List<VppElectricQuantityDTO> electricQuantityDTOS, List<DataMaintenanceUpdateDTO> updateDTOs) {
        ImportExcelDetailVO result = initializeBatchResult();

        try {
            // 1. 数据预处理和分组，按日期分组更新DTO，按日期分组实际数据，按租户分组参数
            BatchProcessContext context = prepareBatchData(electricQuantityDTOS, updateDTOs);

            // 2. 按租户处理数据，按日期处理租户数据
            BatchProcessResult processResult = processByTenant(context);

            // 3. 批量保存数据，批量保存数据
            saveBatchData(processResult.getElectricActualList());

            // 4. 异步处理后续操作，异步处理后续操作
            executeAsyncTasks(electricQuantityDTOS, context.getUpdateMaps());

            // 5. 设置结果，设置结果
            result.setSuccessNum(processResult.getSuccessCount());
            result.setFailedNum(processResult.getFailedCount());
            result.setStatus(processResult.getFailedCount() == 0);

            if (processResult.getFailedCount() > 0) {
                result.setMessage("部分数据处理失败");
                result.setInfoList(processResult.getErrorMessages());
            }

            log.info("批量导入完成，成功: {}, 失败: {}", processResult.getSuccessCount(), processResult.getFailedCount());

        } catch (Exception e) {
            log.error("批量导入异常", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            result.setStatus(false);
            result.setMessage("批量导入异常: " + e.getMessage());
            result.setFailedNum(1);
        }

        return result;
    }

    /**
     * 初始化批量处理结果
     */
    private ImportExcelDetailVO initializeBatchResult() {
        ImportExcelDetailVO result = new ImportExcelDetailVO();
        result.setInfoList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(0);
        return result;
    }

    /**
     * 数据预处理和分组
     * @param electricQuantityDTOS 电量数据DTO列表
     * @param updateDTOs 更新DTO列表
     * @return 批处理上下文
     */
    private BatchProcessContext prepareBatchData(List<VppElectricQuantityDTO> electricQuantityDTOS,
                                                 List<DataMaintenanceUpdateDTO> updateDTOs) {
        // 1. 按日期分组更新数据维护
        Map<String, List<DataMaintenanceUpdateDTO>> updateMaps = updateDTOs.stream()
                .collect(Collectors.groupingBy(dto -> DateUtil.formatDate(dto.getDateDay())));

        // 2. 按租户分组用电量导入数据
        Map<Long, List<VppElectricQuantityDTO>> paramsByTenant = electricQuantityDTOS.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getTenantId));

        return new BatchProcessContext(updateMaps, paramsByTenant);
    }

    /**
     * 按租户处理数据
     * @param context 批处理上下文
     * @return   批处理结果
     */
    private BatchProcessResult processByTenant(BatchProcessContext context) {
        BatchProcessResult result = new BatchProcessResult();

        for (Map.Entry<Long, List<VppElectricQuantityDTO>> tenantEntry : context.getParamsByTenant().entrySet()) {
            Long tenantId = tenantEntry.getKey();
            List<VppElectricQuantityDTO> tenantParams = tenantEntry.getValue();

            // 1. 获取租户用户信息
            List<VppUser> userList = vppLoadUserService.getListByTenantId(tenantId, "");

            Map<String, VppUser> userNameMap = userList.stream().collect(Collectors.toMap(VppUser::getName, u -> u));

            Set<String> validUserNames = userList.stream().map(VppUser::getName).collect(Collectors.toSet());

            TenantUserInfo userInfo = new TenantUserInfo(userNameMap, validUserNames);

            // 2. 按日期处理租户数据
            processTenantDataByDate(tenantParams, userInfo, result);
        }

        return result;
    }

    /**
     * 按日期处理租户数据
     * @param tenantParams 租户参数
     * @param userInfo 租户用户信息
     * @param result 批处理结果
     */
    private void processTenantDataByDate(List<VppElectricQuantityDTO> tenantParams,
                                         TenantUserInfo userInfo,
                                         BatchProcessResult result) {
        // 1. 按日期分组处理
        Map<Date, List<VppElectricQuantityDTO>> paramsByDate = tenantParams.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getRunningDate));

        for (Map.Entry<Date, List<VppElectricQuantityDTO>> dateEntry : paramsByDate.entrySet()) {
            Date runningDate = dateEntry.getKey();
            List<VppElectricQuantityDTO> dateParams = dateEntry.getValue();

            // 2. 创建分区表
            try {
                vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL, runningDate);
            } catch (Exception e) {
                log.warn("创建分区表失败，日期: " + runningDate, e);
            }

            // 3. 处理单日数据
            String dateStr = DateUtil.formatDate(runningDate);
            List<VppElectricActualDTO> dayActualData = dateParams.stream().flatMap(dto -> dto.getElectricActualList().stream()).collect(Collectors.toList());

            if (dayActualData.isEmpty()) {
                result.addError("日期 " + dateStr + " 没有找到对应的实际数据");
                return;
            }

            // 4. 过滤有效用户数据
            List<VppElectricActualDTO> validActualData = dayActualData.stream().filter(dto -> userInfo.getValidUserNames().contains(dto.getName())).collect(Collectors.toList());

            if (validActualData.isEmpty()) {
                result.addError("日期 " + dateStr + " 没有有效的用户数据");
                return;
            }

            // 5. 数据校验
            if (!validateElectricData(validActualData, result)) {
                return;
            }

            // 6. 转换并添加到结果中
            List<VppElectricActual> electricActualList = convertToElectricActual(validActualData, userInfo);
            result.addElectricActualList(electricActualList);
            result.addSuccessCount(electricActualList.size());

            log.debug("处理日期 {} 完成，数据量: {}", dateStr, electricActualList.size());
        }
    }

    /**
     * 校验电量数据
     * @param actualData 实际用电量数据
     * @param result 批处理结果
     * @return 是否校验通过
     */
    private boolean validateElectricData(List<VppElectricActualDTO> actualData, BatchProcessResult result) {
        if (actualData.isEmpty()) {
            result.addError("没有绑定的用户数据");
            return false;
        }

        if (actualData.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
            result.addError("数据量错误，期望为96的倍数，实际为: " + actualData.size());
            return false;
        }

        return true;
    }

    /**
     * 转换DTO为实体对象
     * @param actualData 实际用电量数据
     * @param userInfo 租户用户信息
     * @return 实际用电量实体对象列表
     */
    private List<VppElectricActual> convertToElectricActual(List<VppElectricActualDTO> actualData,
                                                            TenantUserInfo userInfo) {
        List<VppElectricActual> result = new ArrayList<>();
        for (VppElectricActualDTO dto : actualData) {
            VppUser user = userInfo.getUserNameMap().get(dto.getName());
            if (user != null) {
                VppElectricActual actual = new VppElectricActual();
                BeanCopyUtils.copy(dto, actual);
                actual.setId(IdWorker.getId());
                actual.setIsDelete(0);
                actual.setUserCode(user.getUserCode());
                actual.setUserId(user.getId());
                result.add(actual);
            }
        }
        return result;
    }

    /**
     * 批量保存数据
     */
    private void saveBatchData(List<VppElectricActual> electricActualList) {
        if (!electricActualList.isEmpty()) {
            log.info("开始批量保存数据，数量: " + electricActualList.size());
            saveBatch(electricActualList);
            log.info("批量保存完成");
        }
    }

    /**
     * 异步执行后续任务
     */
    private void executeAsyncTasks(List<VppElectricQuantityDTO> params,
                                   Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        // 使用合理的线程池配置
        int threadCount = Math.min(params.size(), Runtime.getRuntime().availableProcessors());
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        try {
            // 等待所有任务完成，设置超时时间
            CompletableFuture.allOf(params.stream().map(param -> CompletableFuture.runAsync(() -> processAsyncTask(param, updateMaps), executor)).toArray(CompletableFuture[]::new))
                    .get(30, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 处理异步任务
     */
    private void processAsyncTask(VppElectricQuantityDTO param,
                                  Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        try {
            // 1. 创建相关分区表
            createRelatedPartitionTables(param);

            // 2. 电量数据计算，24小时数据，96点聚合数据，中位数预测数据
            importElectricCalculate(param.getTenantId(), param.getRunningDate(), param.getId());

            // 3. 更新数据维护状态
            updateMaintenanceStatus(param, updateMaps);

        } catch (Exception e) {
            log.error("异步任务处理失败，参数: " + param, e);
        }
    }

    /**
     * 创建相关分区表
     * @param param 参数
     */
    private void createRelatedPartitionTables(VppElectricQuantityDTO param) {
        // 1. 创建24小时分区表
        vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_24, param.getRunningDate());

        // 2. 创建聚合分区表
        vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_CONVERGE, param.getRunningDate());

        // 3. 创建中位数分区表
        int months = DateUtil.dayOfMonth(param.getRunningDate()) > 27 ? 3 : 2;
        for (int i = 0; i < months; i++) {
            vppPartitionTableService.createPartitionTable(
                    PartitionTableConstant.VPP_ELECTRIC_ACTUAL_MIDDLE,
                    DateUtil.offsetMonth(param.getRunningDate(), i));
        }
    }

    /**
     * 更新数据维护状态
     * @param param 参数
     * @param updateMaps 更新DTO列表
     */
    private void updateMaintenanceStatus(VppElectricQuantityDTO param,
                                         Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        String dateStr = DateUtil.formatDate(param.getRunningDate());
        List<DataMaintenanceUpdateDTO> updateDTOs = updateMaps.get(dateStr);

        if (ObjectUtil.isNotEmpty(updateDTOs)) {
            dataMaintenanceService.updateStatus(updateDTOs.get(0));
        }
    }

    // ==================== 内部辅助类 ====================

    /**
     * 批量处理上下文
     */
    @Data
    @AllArgsConstructor
    private static class BatchProcessContext {
        private final Map<String, List<DataMaintenanceUpdateDTO>> updateMaps;
        private final Map<Long, List<VppElectricQuantityDTO>> paramsByTenant;
    }

    /**
     * 租户用户信息
     */
    @Data
    @AllArgsConstructor
    private static class TenantUserInfo {
        private final Map<String, VppUser> userNameMap;
        private final Set<String> validUserNames;
    }

    /**
     * 批量处理结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class BatchProcessResult {
        private final List<VppElectricActual> electricActualList = new ArrayList<>();
        private final List<String> errorMessages = new ArrayList<>();
        private int successCount = 0;
        private int failedCount = 0;

        public void addElectricActualList(List<VppElectricActual> list) {
            electricActualList.addAll(list);
        }

        public void addError(String message) {
            errorMessages.add(message);
            failedCount++;
        }

        public void addSuccessCount(int count) {
            successCount += count;
        }
    }

    @Override
    public List<VppElectricActual> getElectricByDateList(Date startTime, Date endTime, Long tenantId, String consNo) {
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(consNo), VppElectricActual::getRegistered, consNo);
        queryWrapper.ge(VppElectricActual::getDateDay, startTime);
        queryWrapper.le(VppElectricActual::getDateDay, endTime);
        queryWrapper.le(VppElectricActual::getTenantId, tenantId);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 电量数据计算
     *
     * @param tenantId   租户ID
     * @param dateDay    日期
     * @param electricId 电量数据ID
     */
    public void importElectricCalculate(Long tenantId, Date dateDay, Long electricId) {
        log.info("开始执行电量数据计算，租户ID: {}, 日期: {}, 电量ID: {}", tenantId, DateUtil.formatDate(dateDay), electricId);

        try {
            // 1. 删除并保存24小时数据
            process24HourData(tenantId, dateDay);

            // 2. 删除并保存96点聚合数据
            process96PointConvergeData(tenantId, dateDay);

            // 3. 计算并保存30天中位数预测数据
            processMiddlePredictionData(tenantId, dateDay);

            log.info("电量数据计算完成，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));

        } catch (Exception e) {
            log.error("电量数据计算失败，租户ID: {}, 日期: {}, 错误: ", tenantId, DateUtil.formatDate(dateDay), e);
            throw new RuntimeException("电量数据计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理24小时数据：删除原有数据并重新计算保存
     *
     * @param tenantId 租户ID
     * @param dateDay  日期
     */
    private void process24HourData(Long tenantId, Date dateDay) {
        log.debug("开始处理24小时数据，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));

        // 删除原有24小时数据
        baseMapper.remove24ByParam(dateDay, tenantId);

        // 保存24小时数据：从96点数据聚合到24点
        // 通过vpp_time_cycle表将96个时点映射到24个时点
        baseMapper.insert24HourData(tenantId, dateDay);

        log.debug("24小时数据处理完成，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));
    }

    /**
     * 处理96点聚合数据：删除原有数据并重新计算保存
     *
     * @param tenantId 租户ID
     * @param dateDay  日期
     */
    private void process96PointConvergeData(Long tenantId, Date dateDay) {
        log.debug("开始处理96点聚合数据，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));

        // 删除原有96点聚合数据
        vppElectricActualConvergeService.removeByParam(dateDay, tenantId);

        // 保存96点聚合数据：按用户和时点聚合实际用电量数据
        baseMapper.insert96PointConvergeData(tenantId, dateDay);

        log.debug("96点聚合数据处理完成，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));
    }

    /**
     * 处理中位数预测数据：计算30天的中位数预测
     *
     * @param tenantId 租户ID
     * @param dateDay  日期
     */
    private void processMiddlePredictionData(Long tenantId, Date dateDay) {
        log.debug("开始处理中位数预测数据，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));

        final int DAYS = 30; // 总天数
        long dateDayTimestamp = dateDay.getTime() / 1000; // 转换为时间戳（秒）

        for (int i = 1; i <= DAYS; i++) {
            // 计算时间范围
            Date startDay = new Date((dateDayTimestamp - (DAYS - i) * 86400L) * 1000);
            Date endDay = new Date((dateDayTimestamp + (i - 1) * 86400L) * 1000);
            Date middleDay = new Date((dateDayTimestamp + i * 86400L) * 1000);

            log.trace("处理中位数预测，第{}天，开始日期: {}, 结束日期: {}, 中位数日期: {}",
                    i, DateUtil.formatDate(startDay), DateUtil.formatDate(endDay), DateUtil.formatDate(middleDay));

            // 删除原来的中位数预测数据
            baseMapper.deleteMiddlePredictionData(tenantId, middleDay);

            // 保存中位数预测数据
            baseMapper.insertMiddlePredictionData(tenantId, middleDay, startDay, endDay);
        }

        log.debug("中位数预测数据处理完成，租户ID: {}, 日期: {}", tenantId, DateUtil.formatDate(dateDay));
    }

}
