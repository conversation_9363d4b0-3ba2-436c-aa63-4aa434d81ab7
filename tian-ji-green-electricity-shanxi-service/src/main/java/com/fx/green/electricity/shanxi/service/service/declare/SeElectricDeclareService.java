package com.fx.green.electricity.shanxi.service.service.declare;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.service.entity.declare.SeElectricDeclare;

import java.util.List;

/**
 * 日前申报数据 Service 接口
 *
 * <AUTHOR>
 **/
public interface SeElectricDeclareService extends IService<SeElectricDeclare> {


    /**
     * 添加日前申报数据
     *
     * @param electricDeclareList 日前申报数据
     */
    void addElectricDeclareList(List<VppElectricDeclareDTO> electricDeclareList);

    /**
     * 删除之前的数据
     *
     * @param id
     */
    void deleteByElectricId(Long id);

    DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO);

    List<SeElectricDeclareVO> getDataByElectricId(Long id);
}
