package com.fx.green.electricity.shanxi.service.service.production.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.constant.VppUserProductionConstants;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserProductionDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataAdjustDevice;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataPowerDevice;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionStatistics;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionStatisticsPlan;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionStatisticsMapper;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityService;
import com.fx.green.electricity.shanxi.service.service.production.*;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户生产统计（主表）
 **/
@Slf4j
@Service
public class VppUserProductionStatisticsServiceImpl extends ServiceImpl<VppUserProductionStatisticsMapper, VppUserProductionStatistics> implements VppUserProductionStatisticsService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppUserProductionDataAdjustDeviceService vppUserProductionDataAdjustDeviceService;

    @Resource
    private VppUserProductionDataService vppUserProductionDataService;

    @Resource
    private VppUserProductionDataPowerDeviceService vppUserProductionDataPowerDeviceService;

    @Resource
    private VppUserProductionStatisticsPlanService vppUserProductionStatisticsPlanService;

    @Resource
    private VppPredictedElectricityService vppPredictedElectricityService;

    private LambdaQueryWrapper<VppUserProductionStatistics> buildQueryWrapper(VppUserProductionStatisticsQueryDTO param) {
        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ObjectUtil.isNotNull(param.getUserId()), VppUserProductionStatistics::getUserId, param.getUserId());
        lqw.eq(ObjectUtil.isNotNull(param.getProductionType()), VppUserProductionStatistics::getType, param.getProductionType());
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.eq(VppUserProductionStatistics::getDateDay, param.getDateDay());
        return lqw;
    }

    @Override
    public void saveUserProductionPlan(VppUserProductionStatisticsDTO param) {
        VppUserProductionStatistics statistics = BeanCopyUtils.copy(param, VppUserProductionStatistics.class);
        VppLoadUserVO userInfo = vppLoadUserService.getUserInfo(param.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            statistics.setUserName(userInfo.getName());
        }
        statistics.setIsDelete(0);
        //删除修改日期 当前用户后续预测电量
        if (ObjectUtil.isNotNull(statistics.getId())) {
            VppUserProductionStatistics statistic = this.getById(statistics.getId());
            if (!statistic.getProductionStatus().equals(param.getProductionStatus())) {
                vppPredictedElectricityService.deleteData(statistics.getUserId(), statistics.getDateDay());
            }
        }
        this.saveOrUpdate(statistics);

        //查询数据库中的数据
        List<VppUserProductionStatisticsPlan> oldList = vppUserProductionStatisticsPlanService.getDataById(statistics.getId());
        //生产计划保存列表
        List<VppUserProductionStatisticsPlan> saveList = new ArrayList<>();
        //要删除的id列表
        List<Long> removeIdList = new ArrayList<>();
        if (ObjectUtil.isNotNull(param.getPlanList())) {
            List<VppUserProductionStatisticsPlan> planList = BeanCopyUtils.copyList(param.getPlanList(), VppUserProductionStatisticsPlan.class);
            List<Long> hasIdList = planList.stream().map(VppUserProductionStatisticsPlan::getId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> removeIds = oldList.stream().map(VppUserProductionStatisticsPlan::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            removeIdList.addAll(removeIds);
            //组装保存数据
            for (VppUserProductionStatisticsPlan statisticsPlan : planList) {
                statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_PRODUCTION);
                saveList.add(statisticsPlan);
            }
        }
        if (ObjectUtil.isNotNull(param.getAdjustDevicePlanList())) {
            List<VppUserProductionStatisticsPlan> adjustDevicePlanList = BeanCopyUtils.copyList(param.getAdjustDevicePlanList(), VppUserProductionStatisticsPlan.class);
            List<Long> hasIdList = adjustDevicePlanList.stream().map(VppUserProductionStatisticsPlan::getId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> removeIds = oldList.stream().map(VppUserProductionStatisticsPlan::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            removeIdList.addAll(removeIds);
            //组装保存数据
            for (VppUserProductionStatisticsPlan statisticsPlan : adjustDevicePlanList) {
                statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_ADJUST);
                saveList.add(statisticsPlan);
            }
        }
        if (ObjectUtil.isNotNull(param.getPowerDevicePlanList())) {
            List<VppUserProductionStatisticsPlan> powerDevicePlanList = BeanCopyUtils.copyList(param.getPowerDevicePlanList(), VppUserProductionStatisticsPlan.class);
            List<Long> hasIdList = powerDevicePlanList.stream().map(VppUserProductionStatisticsPlan::getId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> removeIds = oldList.stream().map(VppUserProductionStatisticsPlan::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            removeIdList.addAll(removeIds);
            //组装保存数据
            for (VppUserProductionStatisticsPlan statisticsPlan : powerDevicePlanList) {
                statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_POWER);
                saveList.add(statisticsPlan);
            }
        }
        vppUserProductionStatisticsPlanService.removeByIds(removeIdList);
        vppUserProductionStatisticsPlanService.saveOrUpdateBatch(saveList);
    }

    @Override
    public void saveList(List<VppUserProductionStatisticsDTO> paramList) {
        //生产计划保存列表
        List<VppUserProductionStatisticsPlan> saveList = new ArrayList<>();
        for (VppUserProductionStatisticsDTO param : paramList) {
            VppUserProductionStatistics statistics = BeanCopyUtils.copy(param, VppUserProductionStatistics.class);
            VppLoadUserVO userInfo = vppLoadUserService.getUserInfo(param.getUserId());
            if (ObjectUtil.isNotNull(userInfo)) {
                statistics.setUserName(userInfo.getName());
            }
            statistics.setIsDelete(0);
            this.saveOrUpdate(statistics);

            if (ObjectUtil.isNotNull(param.getPlanList())) {
                List<VppUserProductionStatisticsPlan> planList = BeanCopyUtils.copyList(param.getPlanList(), VppUserProductionStatisticsPlan.class);
                //组装保存数据
                for (VppUserProductionStatisticsPlan statisticsPlan : planList) {
                    statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                    statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_PRODUCTION);
                    saveList.add(statisticsPlan);
                }
            }
            if (ObjectUtil.isNotNull(param.getAdjustDevicePlanList())) {
                List<VppUserProductionStatisticsPlan> adjustDevicePlanList = BeanCopyUtils.copyList(param.getAdjustDevicePlanList(), VppUserProductionStatisticsPlan.class);
                //组装保存数据
                for (VppUserProductionStatisticsPlan statisticsPlan : adjustDevicePlanList) {
                    statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                    statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_ADJUST);
                    saveList.add(statisticsPlan);
                }
            }
            if (ObjectUtil.isNotNull(param.getPowerDevicePlanList())) {
                List<VppUserProductionStatisticsPlan> powerDevicePlanList = BeanCopyUtils.copyList(param.getPowerDevicePlanList(), VppUserProductionStatisticsPlan.class);
                //组装保存数据
                for (VppUserProductionStatisticsPlan statisticsPlan : powerDevicePlanList) {
                    statisticsPlan.setUserProductionStatisticsId(statistics.getId());
                    statisticsPlan.setPlanType(VppUserProductionConstants.PLAN_TYPE_POWER);
                    saveList.add(statisticsPlan);
                }
            }
        }
        vppUserProductionStatisticsPlanService.saveOrUpdateBatch(saveList);

    }


    @Override
    public VppUserProductionStatisticsVO getProductionStatisticsByUserId(VppUserProductionStatisticsQueryDetailDTO param) {
        if (ObjectUtil.isEmpty(param.getUserId())) {
            throw new FxServiceException("用户id信息不能为空");
        }
        if (ObjectUtil.isEmpty(param.getProductionType())) {
            throw new FxServiceException("类型不能为空");
        }
        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ObjectUtil.isNotNull(param.getUserId()), VppUserProductionStatistics::getUserId, param.getUserId());
        lqw.eq(ObjectUtil.isNotNull(param.getProductionType()), VppUserProductionStatistics::getType, param.getProductionType());
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.eq(VppUserProductionStatistics::getDateDay, param.getDateDay());
        VppUserProductionStatistics statistics = getOne(lqw);

        VppUserProductionStatisticsVO resultList = new VppUserProductionStatisticsVO();
        if (ObjectUtil.isNotEmpty(statistics)) {
            List<VppUserProductionStatisticsPlan> allPlanList = vppUserProductionStatisticsPlanService.getDataById(statistics.getId());
            //生产计划
            List<VppUserProductionStatisticsPlan> productionList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_PRODUCTION).collect(Collectors.toList());
            //调节设备运行计划
            List<VppUserProductionStatisticsPlan> adjustList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_ADJUST).collect(Collectors.toList());
            //发电设备运行计划
            List<VppUserProductionStatisticsPlan> powerList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_POWER).collect(Collectors.toList());
            BeanCopyUtils.copy(statistics, resultList);
            resultList.setProductionList(BeanCopyUtils.copyList(productionList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
            resultList.setAdjustPlanList(BeanCopyUtils.copyList(adjustList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
            resultList.setPowerPlanList(BeanCopyUtils.copyList(powerList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
        }


        return resultList;
    }

    @Override
    public List<VppUserProductionStatisticsVO> getProductionStatisticsList(VppUserProductionStatisticsQueryDetailDTO param) {

        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.eq(VppUserProductionStatistics::getDateDay, param.getDateDay());
        List<VppUserProductionStatistics> statisticsList = list(lqw);
        List<VppUserProductionStatisticsVO> resultList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(statisticsList)) {
            for (VppUserProductionStatistics statistics : statisticsList) {
                VppUserProductionStatisticsVO resultPlan = new VppUserProductionStatisticsVO();
                List<VppUserProductionStatisticsPlan> allPlanList = vppUserProductionStatisticsPlanService.getDataById(statistics.getId());
                //生产计划
                List<VppUserProductionStatisticsPlan> productionList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_PRODUCTION).collect(Collectors.toList());
                //调节设备运行计划
                List<VppUserProductionStatisticsPlan> adjustList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_ADJUST).collect(Collectors.toList());
                //发电设备运行计划
                List<VppUserProductionStatisticsPlan> powerList = allPlanList.stream().filter(e -> e.getPlanType() == VppUserProductionConstants.PLAN_TYPE_POWER).collect(Collectors.toList());
                BeanCopyUtils.copy(statistics, resultPlan);
                resultPlan.setProductionList(BeanCopyUtils.copyList(productionList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
                resultPlan.setAdjustPlanList(BeanCopyUtils.copyList(adjustList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
                resultPlan.setPowerPlanList(BeanCopyUtils.copyList(powerList, VppUserProductionStatisticsVO.ProductionStatisticsPlanVO.class));
                resultList.add(resultPlan);
            }
        }
        return resultList;
    }

    @Override
    public VppUserProductionStatisticsListVO getProductionStatisticsByDay(VppUserProductionStatisticsQueryDTO param) {
        List<VppUserProductionStatistics> statisticsList = list(buildQueryWrapper(param));
        Map<Long, List<VppUserProductionStatistics>> expectedUserMap = statisticsList.stream().filter(e -> e.getType() == 1).collect(Collectors.groupingBy(VppUserProductionStatistics::getUserId));
        Map<Long, List<VppUserProductionStatistics>> actualUserMap = statisticsList.stream().filter(e -> e.getType() == 2).collect(Collectors.groupingBy(VppUserProductionStatistics::getUserId));
        VppUserProductionStatisticsListVO resultVO = new VppUserProductionStatisticsListVO();
        List<VppUserProductionStatisticsListVO.UserProductionStatisticsPlanVO> voList = new ArrayList<>();

        //根据查询的类型查询用户
        QueryTreeListDTO dto = new QueryTreeListDTO();
        dto.setDateDay(param.getDateDay());
        dto.setTenantId(param.getTenantId());
        dto.setType(param.getUserType());
        VppLoadUserVO.TreeVO treeVO = vppPredictedElectricityService.queryTreeListNew(dto).getData();
        if (ObjectUtil.isNotNull(treeVO)) {
            List<VppLoadUserVO.TreeUserVO> treeUserVOList = treeVO.getTreeUserVOList();
            if (ObjectUtil.isNotEmpty(treeUserVOList)) {
                if (ObjectUtil.isNotEmpty(param.getUserName())) {
                    treeUserVOList = treeUserVOList.stream().filter(e -> e.getName().contains(param.getUserName())).collect(Collectors.toList());
                }
                int userSize = treeUserVOList.size();
                resultVO.setUserNum(userSize);
                int actualIsWrite = 0;
                int expectedIsWrite = 0;
                for (VppLoadUserVO.TreeUserVO treeUserVO : treeUserVOList) {
                    //组装返回数据
                    VppUserProductionStatisticsListVO.UserProductionStatisticsPlanVO vo = new VppUserProductionStatisticsListVO.UserProductionStatisticsPlanVO();
                    vo.setUserId(treeUserVO.getId());
                    vo.setUserName(treeUserVO.getName());
                    vo.setDateDay(param.getDateDay());
                    vo.setActualIsWrite(VppUserProductionConstants.NO_WRITE);
                    vo.setExpectedIsWrite(VppUserProductionConstants.NO_WRITE);

                    if (ObjectUtil.isNotEmpty(statisticsList)) {
                        List<Long> idList = statisticsList.stream().map(VppUserProductionStatistics::getId).collect(Collectors.toList());
                        List<VppUserProductionStatisticsPlan> allPlanList = vppUserProductionStatisticsPlanService.getDataByIdList(idList);
                        Map<Long, List<VppUserProductionStatisticsPlan>> allPlanMap = allPlanList.stream().collect(Collectors.groupingBy(VppUserProductionStatisticsPlan::getUserProductionStatisticsId));
                        Map<Long, List<VppUserProductionStatistics>> listMap = statisticsList.stream().collect(Collectors.groupingBy(VppUserProductionStatistics::getUserId));

                        //获取所有的设备信息（调节设备、发电设备）
                        List<Long> adjustDeviceIdList = allPlanList.stream().filter(e -> e.getPlanType().equals(VppUserProductionConstants.PLAN_TYPE_ADJUST)).map(VppUserProductionStatisticsPlan::getDeviceId).distinct().collect(Collectors.toList());
                        List<Long> powerDeviceIdList = allPlanList.stream().filter(e -> e.getPlanType().equals(VppUserProductionConstants.PLAN_TYPE_POWER)).map(VppUserProductionStatisticsPlan::getDeviceId).distinct().collect(Collectors.toList());
                        Map<Long, VppUserProductionDataAdjustDevice> adjustDeviceMap = new HashMap<>();
                        if (ObjectUtil.isNotEmpty(adjustDeviceIdList)) {
                            List<VppUserProductionDataAdjustDevice> adjustDeviceList = vppUserProductionDataAdjustDeviceService.getPowerByIdList(adjustDeviceIdList);
                            adjustDeviceMap = adjustDeviceList.stream().collect(Collectors.toMap(VppUserProductionDataAdjustDevice::getId, o -> o));
                        }
                        Map<Long, VppUserProductionDataPowerDevice> powerDeviceMap = new HashMap<>();
                        if (ObjectUtil.isNotEmpty(powerDeviceIdList)) {
                            List<VppUserProductionDataPowerDevice> powerDeviceList = vppUserProductionDataPowerDeviceService.getAdjustByIdList(powerDeviceIdList);
                            powerDeviceMap = powerDeviceList.stream().collect(Collectors.toMap(VppUserProductionDataPowerDevice::getId, o -> o));
                        }
                        List<VppUserProductionStatistics> list = listMap.get(treeUserVO.getId());

                        //预计生产计划
                        List<VppUserProductionStatisticsListVO.ProductionStatisticsPlanVO> expectedPlanList = new ArrayList<>();
                        //实际生产计划
                        List<VppUserProductionStatisticsListVO.ProductionStatisticsPlanVO> actualPlanList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(list)) {
                            for (VppUserProductionStatistics statistics : list) {
                                //查询用户信息
                                if (statistics.getType() == VppUserProductionConstants.EXPECTED_PLAN) {
                                    vo.setExpectedProductionStatus(statistics.getProductionStatus());
                                    vo.setExpectedProductionStatusDetail(statistics.getProductionStatusDetail());
                                    vo.setExpectedIsWrite(VppUserProductionConstants.IS_WRITE);
                                } else {
                                    vo.setActualProductionStatus(statistics.getProductionStatus());
                                    vo.setActualProductionStatusDetail(statistics.getProductionStatusDetail());
                                    vo.setActualIsWrite(VppUserProductionConstants.IS_WRITE);
                                }
                                List<VppUserProductionStatisticsPlan> planList = allPlanMap.get(statistics.getId());
                                if (ObjectUtil.isNotNull(planList)) {
                                    for (VppUserProductionStatisticsPlan statisticsPlan : planList) {
                                        if (statistics.getType() == VppUserProductionConstants.EXPECTED_PLAN) {
                                            //预计生产计划
                                            buildPlanVO(expectedPlanList, statisticsPlan, adjustDeviceMap, powerDeviceMap);
                                        } else {
                                            //实际生产情况
                                            buildPlanVO(actualPlanList, statisticsPlan, adjustDeviceMap, powerDeviceMap);
                                        }
                                    }
                                }
                            }
                        }
                        vo.setActualList(actualPlanList);
                        vo.setExpectedPlanList(expectedPlanList);
                        List<VppUserProductionStatistics> expectedUserList = expectedUserMap.get(treeUserVO.getId());
                        List<VppUserProductionStatistics> actualUserList = actualUserMap.get(treeUserVO.getId());
                        if (ObjectUtil.isNotEmpty(actualUserList)) {
                            actualIsWrite += 1;
                            if (param.getQueryType() == 3) {
                                voList.add(vo);
                            }
                        } else {
                            if (param.getQueryType() == 4) {
                                voList.add(vo);
                            }
                        }
                        if (ObjectUtil.isNotEmpty(expectedUserList)) {
                            expectedIsWrite += 1;
                            if (param.getQueryType() == 1) {
                                voList.add(vo);
                            }
                        } else {
                            if (param.getQueryType() == 2) {
                                voList.add(vo);
                            }
                        }
                        if (ObjectUtil.isEmpty(param.getQueryType()) || param.getQueryType() == 0) {
                            voList.add(vo);
                        }
                    } else {
                        voList.add(vo);
                    }
                }
                resultVO.setActualIsWrite(actualIsWrite + "/" + userSize);
                resultVO.setActualIsNotWrite(userSize - actualIsWrite + "/" + userSize);
                resultVO.setExpectedIsWrite(expectedIsWrite + "/" + userSize);
                resultVO.setExpectedIsNotWrite(userSize - expectedIsWrite + "/" + userSize);
            }
        }
        resultVO.setList(voList);
        return resultVO;
    }

    /**
     * 构造生产计划、实际生产的返回数据
     * @param planList
     * @param statisticsPlan
     * @param adjustDeviceMap
     * @param powerDeviceMap
     */
    private void buildPlanVO(List<VppUserProductionStatisticsListVO.ProductionStatisticsPlanVO> planList, VppUserProductionStatisticsPlan statisticsPlan, Map<Long, VppUserProductionDataAdjustDevice> adjustDeviceMap, Map<Long, VppUserProductionDataPowerDevice> powerDeviceMap) {
        VppUserProductionStatisticsListVO.ProductionStatisticsPlanVO vo = new VppUserProductionStatisticsListVO.ProductionStatisticsPlanVO();
        vo.setProduction(statisticsPlan.getProduction());
        vo.setName(statisticsPlan.getName());
        vo.setPlanType(statisticsPlan.getPlanType());
        if (statisticsPlan.getPlanType() == VppUserProductionConstants.PLAN_TYPE_ADJUST) {
            VppUserProductionDataAdjustDevice adjustDevice = adjustDeviceMap.get(statisticsPlan.getDeviceId());
            if (ObjectUtil.isNotNull(adjustDevice)) {
                vo.setAdjustDeviceName(adjustDevice.getName());
            }
            vo.setContinuousOperationDuration(statisticsPlan.getContinuousOperationDuration());
            vo.setOperatingPower(statisticsPlan.getOperatingPower());
        } else if (statisticsPlan.getPlanType() == VppUserProductionConstants.PLAN_TYPE_POWER) {
            VppUserProductionDataPowerDevice powerDevice = powerDeviceMap.get(statisticsPlan.getDeviceId());
            if (ObjectUtil.isNotNull(powerDevice)) {
                vo.setPowerDeviceName(powerDevice.getDeviceName());
            }
            vo.setContinuousOperationDuration(statisticsPlan.getContinuousOperationDuration());
            vo.setOperatingPower(statisticsPlan.getOperatingPower());
        }
        vo.setStartTime(DateUtil.format(statisticsPlan.getStartTime(), "yyyy-MM-dd HH:mm"));
        vo.setEndTime(DateUtil.format(statisticsPlan.getEndTime(), "yyyy-MM-dd HH:mm"));
        planList.add(vo);
    }


    @Override
    public List<VppUserProductionDataDeviceListVO> getDeviceListByType(VppUserProductionDataDeviceQueryDTO param) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(param.getUserId());
        idDTO.setTenantId(param.getTenantId());
        VppUserProductionDataVO dataVO = vppUserProductionDataService.getByUserId(idDTO);
        List<VppUserProductionDataDeviceListVO> resultList = new ArrayList<>();
        if (ObjectUtil.isNotNull(dataVO)) {
            if (param.getType().equals(VppUserProductionConstants.DEVICE_TYPE_ADJUST)) {
                List<VppUserProductionDataAdjustDevice> adjustDeviceList = vppUserProductionDataAdjustDeviceService.getByDataId(dataVO.getId());
                for (VppUserProductionDataAdjustDevice adjustDevice : adjustDeviceList) {
                    VppUserProductionDataDeviceListVO listVO = new VppUserProductionDataDeviceListVO();
                    listVO.setId(adjustDevice.getId());
                    listVO.setName(adjustDevice.getName());
                    resultList.add(listVO);
                }
            } else if (param.getType().equals(VppUserProductionConstants.DEVICE_TYPE_POWER)) {
                List<VppUserProductionDataPowerDevice> powerDeviceList = vppUserProductionDataPowerDeviceService.getByDataId(dataVO.getId());
                for (VppUserProductionDataPowerDevice powerDevice : powerDeviceList) {
                    VppUserProductionDataDeviceListVO listVO = new VppUserProductionDataDeviceListVO();
                    listVO.setId(powerDevice.getId());
                    listVO.setName(powerDevice.getDeviceName());
                    resultList.add(listVO);
                }
            }
        }

        return resultList;
    }


    @Override
    public List<VppUserProductionStatusVO> getUserProductionStatus(VppUserProductionStatusQueryDTO param) {
        if (ObjectUtil.isEmpty(param.getStartTime()) || ObjectUtil.isEmpty(param.getEndTime())) {
            Date date = DateUtil.date();
            param.setEndTime(DateUtil.parse(DateUtil.formatDate(date), "yyyy-MM-dd"));
            param.setStartTime(DateUtil.parse(DateUtil.formatDate(DateUtil.offsetMonth(date, -8)), "yyyy-MM-dd"));
        }
        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.eq(ObjectUtil.isNotEmpty(param.getUserId()), VppUserProductionStatistics::getUserId, param.getUserId());
//        lqw.le(VppUserProductionStatistics::getDateDay, param.getEndTime());
        lqw.ge(VppUserProductionStatistics::getDateDay, param.getStartTime());
        List<VppUserProductionStatistics> list = list(lqw);
        Map<Long, List<VppUserProductionStatistics>> userMap = list.stream().collect(Collectors.groupingBy(VppUserProductionStatistics::getUserId));
        List<VppUserProductionStatusVO> voList = new ArrayList<>();
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
        Map<Long, String> mapByTenantId = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getName));

        for (Map.Entry<Long, List<VppUserProductionStatistics>> entry : userMap.entrySet()) {
            Long userId = entry.getKey();
            List<VppUserProductionStatistics> listByUser = entry.getValue();
            //实际生产
            List<VppUserProductionStatistics> actualList = listByUser.stream().filter(e -> e.getType() == VppUserProductionConstants.ACTUAL_PLAN).collect(Collectors.toList());
            //预计生产
            List<VppUserProductionStatistics> expectedList = listByUser.stream().filter(e -> e.getType() == VppUserProductionConstants.EXPECTED_PLAN).collect(Collectors.toList());
            Map<String, Integer> actualMap = actualList.stream().collect(Collectors.toMap(e -> DateUtil.formatDate(e.getDateDay()), VppUserProductionStatistics::getProductionStatus));
            Map<String, Integer> expectedMap = expectedList.stream().collect(Collectors.toMap(e -> DateUtil.formatDate(e.getDateDay()), VppUserProductionStatistics::getProductionStatus));
            VppUserProductionStatusVO vo = new VppUserProductionStatusVO();
            vo.setUserId(userId);
            vo.setUserName(mapByTenantId.get(userId));
            vo.setActualMap(actualMap);
            vo.setExpectedMap(expectedMap);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<VppUserProductionStatusVO> getUserProductionStatusByTenantId(IdDTO param) {
        Date date = DateUtil.date();
        Date startDate = DateUtil.parse(DateUtil.formatDate(DateUtil.offsetMonth(date, -12)), "yyyy-MM-dd").toJdkDate();
        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.ge(VppUserProductionStatistics::getDateDay, startDate);
        List<VppUserProductionStatistics> list = list(lqw);
        Map<Long, List<VppUserProductionStatistics>> userMap = list.stream().collect(Collectors.groupingBy(VppUserProductionStatistics::getUserId));
        List<VppUserProductionStatusVO> voList = new ArrayList<>();
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
        Map<Long, String> mapByTenantId = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getName));

        for (Map.Entry<Long, List<VppUserProductionStatistics>> entry : userMap.entrySet()) {
            Long userId = entry.getKey();
            List<VppUserProductionStatistics> listByUser = entry.getValue();
            //实际生产
            List<VppUserProductionStatistics> actualList = listByUser.stream().filter(e -> e.getType() == VppUserProductionConstants.ACTUAL_PLAN).collect(Collectors.toList());
            //预计生产
            List<VppUserProductionStatistics> expectedList = listByUser.stream().filter(e -> e.getType() == VppUserProductionConstants.EXPECTED_PLAN).collect(Collectors.toList());
            Map<String, Integer> actualMap = actualList.stream().collect(Collectors.toMap(e -> DateUtil.formatDate(e.getDateDay()), VppUserProductionStatistics::getProductionStatus));
            Map<String, Integer> expectedMap = expectedList.stream().collect(Collectors.toMap(e -> DateUtil.formatDate(e.getDateDay()), VppUserProductionStatistics::getProductionStatus));
            VppUserProductionStatusVO vo = new VppUserProductionStatusVO();
            vo.setUserId(userId);
            vo.setUserName(mapByTenantId.get(userId));
            vo.setActualMap(actualMap);
            vo.setExpectedMap(expectedMap);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<VppUserProductionStatistics> getProductionStatisticsByUserIdList(QueryUserProductionDTO param) {
        LambdaQueryWrapper<VppUserProductionStatistics> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionStatistics::getTenantId, param.getTenantId());
        lqw.eq(VppUserProductionStatistics::getUserId, param.getUserId());
        lqw.ge(VppUserProductionStatistics::getDateDay, DateUtil.beginOfMonth(param.getDateDay()));
        lqw.le(VppUserProductionStatistics::getDateDay, DateUtil.endOfMonth(param.getDateDay()));
        return this.list(lqw);
    }
}
