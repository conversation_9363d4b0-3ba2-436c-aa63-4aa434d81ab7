package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.enums.MaintenanceStatusEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.service.entity.data.VppDataMaintenance;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;
import com.fx.green.electricity.shanxi.service.mapper.data.DataMaintenanceMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据维护 Service 实现类
 */
@Slf4j
@Service
public class DataMaintenanceServiceImpl extends ServiceImpl<DataMaintenanceMapper, VppDataMaintenance> implements DataMaintenanceService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppBindCycleService vppBindCycleService;

    @Override
    public List<DataMaintenanceVO> queryList(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getDateMonth, param.getQueryMonth());
        wrapper.eq(VppDataMaintenance::getTenantId, param.getTenantId());
        List<VppDataMaintenance> list = list(wrapper);
        return BeanUtil.copyToList(list, DataMaintenanceVO.class);
    }

    @Override
    public List<ExpireUserVO> getExpireUserList(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppUserBindCycle> bindCycleWrapper = new LambdaQueryWrapper<>();
        Date date = param.getQueryMonth();
        DateTime queryStart = DateUtil.beginOfMonth(date);
        DateTime queryEnd = DateUtil.endOfMonth(date);
        bindCycleWrapper.le(VppUserBindCycle::getBindCycleEnd, queryEnd);
        bindCycleWrapper.ge(VppUserBindCycle::getBindCycleEnd, queryStart);
        bindCycleWrapper.eq(VppUserBindCycle::getTenantId, param.getTenantId());
        List<VppUserBindCycle> list = vppBindCycleService.list(bindCycleWrapper);
        List<ExpireUserVO> resultList = new ArrayList<>();
        if (!list.isEmpty()) {
            LambdaQueryWrapper<VppUser> baseWrapper = new LambdaQueryWrapper<>();
            baseWrapper.in(VppUser::getId, list.stream().map(VppUserBindCycle::getUserId).collect(Collectors.toList()));
            List<VppUser> vppUsers = vppLoadUserService.list(baseWrapper);
            Map<Long, List<VppUserBindCycle>> listMap = list.stream().collect(Collectors.groupingBy(VppUserBindCycle::getUserId));
            for (VppUser vppUser : vppUsers) {
                List<VppUserBindCycle> vppUserBindList = listMap.get(vppUser.getId());
                if (vppUserBindList != null && !vppUserBindList.isEmpty()) {
                    ExpireUserVO expireUserVO = new ExpireUserVO();
                    expireUserVO.setName(vppUser.getName());
                    expireUserVO.setExpireDate(vppUserBindList.get(0).getBindCycleEnd());
                    resultList.add(expireUserVO);
                }
            }
        }
        return resultList;
    }

    @Override
    public Integer geUserCount(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppUserBindCycle> bindCycleWrapper = new LambdaQueryWrapper<>();
        Date date = param.getQueryMonth();
        bindCycleWrapper.le(VppUserBindCycle::getBindCycleStart, date);
        bindCycleWrapper.ge(VppUserBindCycle::getBindCycleEnd, date);
        bindCycleWrapper.eq(VppUserBindCycle::getTenantId, param.getTenantId());
        return vppBindCycleService.count(bindCycleWrapper);
    }

    @Override
    public Date getdateDay(Long tenantId) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getTenantId, tenantId);
        wrapper.ne(VppDataMaintenance::getRealElectricityStatus, MaintenanceStatusEnum.NOT_MAINTAINED.getStatus());
        wrapper.orderByAsc(VppDataMaintenance::getDateDay);
        List<VppDataMaintenance> list = list(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            return list.get(list.size() - 1).getDateDay();
        } else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(DataMaintenanceUpdateDTO param) {
        try {
            log.info("开始更新数据维护状态，租户ID: {}, 日期: {}", param.getTenantId(), param.getDateDay());

            VppDataMaintenance existingData = findExistingData(param.getTenantId(), param.getDateDay());

            if (existingData != null) {
                updateExistingData(existingData, param);
                baseMapper.updateById(existingData);
                log.info("更新数据维护状态成功，记录ID: {}", existingData.getId());
            } else {
                VppDataMaintenance newData = createNewData(param);
                baseMapper.insert(newData);
                log.info("新增数据维护状态成功，租户ID: {}, 日期: {}", param.getTenantId(), param.getDateDay());
            }
        } catch (Exception e) {
            log.error("更新数据维护状态失败，租户ID: {}, 日期: {}, 错误信息: {}",
                    param.getTenantId(), param.getDateDay(), e.getMessage(), e);
            throw new RuntimeException("更新数据维护状态失败", e);
        }
    }

    /**
     * 查找已存在的数据维护记录
     */
    private VppDataMaintenance findExistingData(Long tenantId, Date dateDay) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getTenantId, tenantId)
                .eq(VppDataMaintenance::getDateDay, dateDay);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 更新已存在的数据维护记录
     */
    private void updateExistingData(VppDataMaintenance existing, DataMaintenanceUpdateDTO param) {
        // 更新状态字段（仅当参数值不为0时更新）
        updateStatusIfNotZero(param.getRealElectricityStatus(), existing::setRealElectricityStatus);
        updateStatusIfNotZero(param.getDeclareStatus(), existing::setDeclareStatus);
        updateStatusIfNotZero(param.getClearResultStatus(), existing::setClearResultStatus);
        updateStatusIfNotZero(param.getMediumLongTermStatus(), existing::setMediumLongTermStatus);
        updateStatusIfNotZero(param.getUnitRealElectricityStatus(), existing::setUnitRealElectricityStatus);
        updateStatusIfNotZero(param.getUnitPowerPredictStatus(), existing::setUnitPowerPredictStatus);
        updateStatusIfNotZero(param.getUnitNodePriceStatus(), existing::setUnitNodePriceStatus);

        // 更新数据源字段（仅当参数值不为null时更新）
        updateSourceIfNotNull(param.getClearDataSources(), existing::setClearDataSources);
        updateSourceIfNotNull(param.getDeclareSources(), existing::setDeclareSources);
        updateSourceIfNotNull(param.getRealElectricitySources(), existing::setRealElectricitySources);
        updateSourceIfNotNull(param.getMediumLongTermSources(), existing::setMediumLongTermSources);
    }

    /**
     * 创建新的数据维护记录
     */
    private VppDataMaintenance createNewData(DataMaintenanceUpdateDTO param) {
        VppDataMaintenance newData = new VppDataMaintenance();
        newData.setTenantId(param.getTenantId());
        newData.setDateDay(param.getDateDay());

        // 设置月份
        Date month = DateUtil.parse(DateUtil.format(param.getDateDay(), "yyyy-MM"), "yyyy-MM");
        newData.setDateMonth(month);

        // 设置状态字段（为0时使用默认的"未维护"状态）
        newData.setRealElectricityStatus(getStatusOrDefault(param.getRealElectricityStatus()));
        newData.setDeclareStatus(getStatusOrDefault(param.getDeclareStatus()));
        newData.setClearResultStatus(getStatusOrDefault(param.getClearResultStatus()));
        newData.setMediumLongTermStatus(getStatusOrDefault(param.getMediumLongTermStatus()));
        newData.setUnitRealElectricityStatus(getStatusOrDefault(param.getUnitRealElectricityStatus()));
        newData.setUnitPowerPredictStatus(getStatusOrDefault(param.getUnitPowerPredictStatus()));
        newData.setUnitNodePriceStatus(getStatusOrDefault(param.getUnitNodePriceStatus()));

        // 设置数据源字段
        newData.setClearDataSources(param.getClearDataSources());
        newData.setDeclareSources(param.getDeclareSources());
        newData.setRealElectricitySources(param.getRealElectricitySources());
        newData.setMediumLongTermSources(param.getMediumLongTermSources());

        return newData;
    }

    /**
     * 当状态不为0时更新字段
     */
    private void updateStatusIfNotZero(Integer status, java.util.function.Consumer<Integer> setter) {
        if (status != null && status != 0) {
            setter.accept(status);
        }
    }

    /**
     * 当数据源不为null时更新字段
     */
    private void updateSourceIfNotNull(Integer source, java.util.function.Consumer<Integer> setter) {
        if (ObjectUtil.isNotNull(source)) {
            setter.accept(source);
        }
    }

    /**
     * 获取状态值，为0或null时返回默认的"未维护"状态
     */
    private Integer getStatusOrDefault(Integer status) {
        return (status == null || status == 0) ?
                MaintenanceStatusEnum.NOT_MAINTAINED.getStatus() : status;
    }
}
