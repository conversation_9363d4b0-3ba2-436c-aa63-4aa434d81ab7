package com.fx.green.electricity.shanxi.service.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 负荷用户设备表
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("vpp_user_device")
public class VppUserDevice extends TenantBaseEntity {

    /**
     * 设备id
     */
    private Long id;

    /**
     * 负荷用户id
     */
    private Long userId;

    /**
     * 户号id
     */
    private Long accountId;

    /**
     * 用户户号
     */
    private String consNo;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 电压等级(详见VoltageLevelEnum枚举: 1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)
     */
    private Integer volLevel;

    /**
     * 设备地区编号
     */
    private String deviceAreaCode;

    /**
     * 设备详细地址
     */
    private String deviceAddressDetail;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 额定电压（kV）
     */
    private BigDecimal ratedVoltage;

    /**
     * 额变电容量/变压器报装容量（kVA）
     */
    private BigDecimal ratedCapacity;

    /**
     * 最大可调功率（kW）
     */
    private BigDecimal maxAdjustPower;

    /**
     * 最大上调功率（kW）
     */
    private BigDecimal maxUpPower;

    /**
     * 最大下调功率（kW）
     */
    private BigDecimal maxDownPower;

    /**
     * 计量点
     */
    private String meterPoint;

}