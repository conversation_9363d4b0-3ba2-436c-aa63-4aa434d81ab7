package com.fx.green.electricity.shanxi.service.service.retail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 零售合同管理 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppRetailContractsService extends IService<VppRetailContracts> {

    /**
     * 获取零售分页列表
     *
     * @param param 查询参数
     * @return 零售分页列表
     */
    FxPage<VppRetailContractsVO> getRetailContractsPage(QueryVppRetailContractsDTO param);

    /**
     * 导入零售合同
     *
     * @param file 文件
     * @param runMonth 运行月份 格式: yyyy-MM
     */
    void importRetailContract(MultipartFile file, String runMonth);

    /**
     * 获取零售合同列表
     *
     * @param param 查询参数
     * @return 零售合同列表
     */
    List<VppRetailContractsVO> getRetailContractsList(QueryVppRetailContractsDTO param);
}
