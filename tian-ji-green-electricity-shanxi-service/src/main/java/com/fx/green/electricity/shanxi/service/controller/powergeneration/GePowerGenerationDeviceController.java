package com.fx.green.electricity.shanxi.service.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceAssociatecConvergenceStationVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDevicePageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 发电设备信息 Controller
 */
@Api(tags = "发电管理- 发电设备信息")
@RestController
@RequestMapping("/gePowerGenerationDevice")
public class GePowerGenerationDeviceController {

    @Resource
    private GePowerGenerationDeviceService gePowerGenerationDeviceService;

    @ApiOperation("新增发电设备信息")
    @PostMapping("/create")
    public DataResult<Void> createPowerGenerationDevice(@RequestBody GePowerGenerationDeviceSaveDTO param) {
        gePowerGenerationDeviceService.createPowerGenerationDevice(param);
        return DataResult.success();
    }

    @ApiOperation("删除发电设备信息")
    @PostMapping("/delete")
    public DataResult<Void> deletePowerGenerationDevice(@RequestBody IdDTO param) {
        gePowerGenerationDeviceService.deletePowerGenerationDevice(param);
        return DataResult.success();
    }

    @ApiOperation("获得发电设备信息详情")
    @PostMapping("/get")
    public DataResult<GePowerGenerationDeviceVO> getPowerGenerationDevice(@RequestBody IdDTO param) {
        GePowerGenerationDeviceVO result = gePowerGenerationDeviceService.getPowerGenerationDevice(param);
        return DataResult.success(result);
    }

    @ApiOperation("更新发电设备信息")
    @PostMapping("/update")
    public DataResult<Void> updatePowerGenerationDevice(@RequestBody GePowerGenerationDeviceSaveDTO param) {
        gePowerGenerationDeviceService.updatePowerGenerationDevice(param);
        return DataResult.success();
    }

    @ApiOperation("获得发电设备信息分页")
    @PostMapping("/page")
    public DataResult<FxPage<GePowerGenerationDevicePageVO>> getPowerGenerationDevicePage(@RequestBody @Valid GePowerGenerationDevicePageDTO param) {
        FxPage<GePowerGenerationDevicePageVO> result = gePowerGenerationDeviceService.getPowerGenerationDevicePage(param);
        return DataResult.success(result);
    }

    @ApiOperation("获得发电设备信息列表")
    @PostMapping("/list")
    public DataResult<List<GePowerGenerationDeviceVO>> getPowerGenerationDeviceList(@RequestBody @Valid GePowerGenerationDeviceListDTO param) {
        List<GePowerGenerationDeviceVO> result = gePowerGenerationDeviceService.getPowerGenerationDeviceList(param);
        return DataResult.success(result);
    }

    @ApiOperation("关联机组")
    @PostMapping("/associate-unit")
    public DataResult<Void> associateUnit(@RequestBody GePowerGenerationDeviceAssociateUnitDTO param) {
        gePowerGenerationDeviceService.associateUnit(param);
        return DataResult.success();
    }

    @ApiOperation("查询关联机组列表")
    @PostMapping("/unit-list")
    public DataResult<List<Long>> getUnitList(@RequestBody IdDTO param) {
        List<Long> result = gePowerGenerationDeviceService.getUnitList(param);
        return DataResult.success(result);
    }

    @ApiOperation("批量新增发电电表信息")
    @PostMapping("/create-meter")
    public DataResult<Void> createPowerGenerationMeter(@RequestBody @Valid GePowerGenerationMeterSaveDTO param) {
        gePowerGenerationDeviceService.createPowerGenerationMeter(param);
        return DataResult.success();
    }

    @ApiOperation("查询关联电表列表")
    @PostMapping("/meter-list")
    public DataResult<List<GePowerGenerationMeterVO>> getMeterList(@RequestBody IdDTO param) {
        List<GePowerGenerationMeterVO> result = gePowerGenerationDeviceService.getMeterList(param);
        return DataResult.success(result);
    }

    @ApiOperation("汇总站关联发电设备")
    @PostMapping("/convergence-station-associate")
    public DataResult<Void> convergenceStationAssociate(@RequestBody GePowerGenerationDeviceAssociatecConvergenceStationDTO param) {
        gePowerGenerationDeviceService.convergenceStationAssociate(param);
        return DataResult.success();
    }

    @ApiOperation("查询汇总站关联的发电设备信息")
    @PostMapping("/get-convergence-station-associated")
    public DataResult<GePowerGenerationDeviceAssociatecConvergenceStationVO> getConvergenceStationAssociated(@RequestBody IdDTO param) {
        GePowerGenerationDeviceAssociatecConvergenceStationVO result = gePowerGenerationDeviceService.getConvergenceStationAssociated(param);
        return DataResult.success(result);
    }

}
