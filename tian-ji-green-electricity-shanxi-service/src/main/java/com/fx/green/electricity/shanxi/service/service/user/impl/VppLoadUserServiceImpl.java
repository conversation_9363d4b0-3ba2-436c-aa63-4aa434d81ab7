package com.fx.green.electricity.shanxi.service.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.MyDateUtil;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.data.gather.api.api.DataGatherVppApi;
import com.fx.data.gather.api.dto.vpp.TenantWithUserAssocDTO;
import com.fx.data.gather.api.dto.vpp.VppUserCodeDataQueryDTO;
import com.fx.data.gather.api.dto.vpp.VppUserDataQueryDTO;
import com.fx.data.gather.api.vo.vpp.PubVppDataVO;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.enums.DecimalPlaceEnum;
import com.fx.green.electricity.shanxi.api.enums.user.BindStatusEnum;
import com.fx.green.electricity.shanxi.api.enums.user.VoltageLevelEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.UserAnalysisVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.service.config.PasswordConfig;
import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserAccount;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;
import com.fx.green.electricity.shanxi.service.mapper.user.VppLoadUserMapper;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.service.user.VppUserAccountService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import com.fx.green.electricity.shanxi.service.utils.DecimalPlaceUtil;
import com.fx.operation.api.api.OperationTenantApi;
import com.fx.operation.api.dto.TenantCreateDTO;
import com.fx.operation.api.vo.TenantUserVO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.*;


/**
 * 负荷用户管理 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppLoadUserServiceImpl extends ServiceImpl<VppLoadUserMapper, VppUser> implements VppLoadUserService {

    /**
     * 数据未删除标识
     */
    private static final int NOT_DELETED = 0;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private VppBindCycleService vppBindCycleService;

    @Resource
    private DataGatherVppApi dataGatherVppApi;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppRetailContractsManageService vppRetailContractsManageService;

    @Resource
    private VppUserAccountService vppUserAccountService;

    @Resource
    private VppElectricActualService vppElectricActualService;

    @Resource
    private OperationTenantApi operationTenantApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(VppLoadUserDTO param) {
        try {
            // 1. 参数校验
            validateUserData(param);

            // 2. 创建用户实体并设置属性
            VppUser vppUser = createVppUserEntity(param);

            // 3. 保存或更新用户基本信息
            boolean saveResult = this.saveOrUpdate(vppUser);
            if (!saveResult) {
                log.error("[saveOrUpdate][保存用户基本信息失败, 用户ID: {}]", param.getId());
                throw new FxServiceException(USER_SAVE_OR_UPDATE_ERROR);
            }

            // 4. 设置用户 ID 到参数中，用于后续保存户号信息
            param.setId(vppUser.getId());

            // 5. 保存用户户号信息
            vppUserAccountService.saveOrUpdateData(param);

            log.info("[saveOrUpdate][成功保存或更新负荷用户信息, 用户ID: {}, 用户名: {}]", vppUser.getId(), param.getName());
        } catch (Exception e) {
            throw new FxServiceException(USER_SAVE_OR_UPDATE_ERROR);
        }
        return param.getId();
    }

    /**
     * 校验用户数据
     *
     * @param param 用户数据
     */
    private void validateUserData(VppLoadUserDTO param) {
        // 校验手机号是否存在
        if (existsUserPhone(param.getId(), param.getContactPhone())) {
            log.warn("[validateUserData][手机号已存在: {}]", param.getContactPhone());
            throw new FxServiceException(USER_PHONE_REPEAT_ERROR);
        }

        // 校验社会统一信用代码是否存在
        if (existsUserCode(param.getId(), param.getUserCode())) {
            log.warn("[validateUserData][社会统一信用代码已存在: {}]", param.getUserCode());
            throw new FxServiceException(USER_CODE_REPEAT_ERROR);
        }
    }

    /**
     * 检查用户手机号是否重复
     *
     * @param id           用户id(可以为null)
     * @param contactPhone 用户手机号
     * @return boolean
     */
    private boolean existsUserPhone(Long id, String contactPhone) {
        if (StrUtil.isBlank(contactPhone)) {
            return false;
        }

        LambdaQueryWrapper<VppUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.eq(VppUser::getContactPhone, contactPhone);
        userLqw.eq(VppUser::getIsDelete, NOT_DELETED);
        userLqw.ne(ObjectUtil.isNotNull(id), VppUser::getId, id);

        int count = this.count(userLqw);
        return count > 0;
    }

    /**
     * 检查社会统一信用代码是否重复
     *
     * @param id       用户id(可以为null)
     * @param userCode 社会统一信用代码
     * @return boolean
     */
    private boolean existsUserCode(Long id, String userCode) {
        if (StrUtil.isBlank(userCode)) {
            return false;
        }

        LambdaQueryWrapper<VppUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.eq(VppUser::getUserCode, userCode);
        //userLqw.eq(VppUser::getTenantId, tenantId);
        userLqw.eq(VppUser::getIsDelete, NOT_DELETED);
        userLqw.ne(ObjectUtil.isNotNull(id), VppUser::getId, id);

        int count = this.count(userLqw);
        return count > 0;
    }

    /**
     * 创建用户实体对象
     *
     * @param param 用户DTO
     * @return 用户实体
     */
    private VppUser createVppUserEntity(VppLoadUserDTO param) {
        if (param == null) {
            throw new IllegalArgumentException("[createVppUserEntity][用户参数不能为空]");
        }

        VppUser vppUser = new VppUser();
        BeanCopyUtils.copy(param, vppUser);

        // 设置默认密码（只在新增时设置，更新时不修改密码）
        if (param.getId() == null) {
            String encodedPassword = passwordEncoder.encode(PasswordConfig.DEFAULT_PASSWORD);
            vppUser.setPassword(encodedPassword);
        }

        //新增账户运营平台同步租户和用户
        if (ObjectUtil.isEmpty(param.getId())) {
            //调用运营平台添加虚拟电厂用户
            TenantCreateDTO tc = new TenantCreateDTO();
            tc.setTenantCode(param.getUserCode());
            tc.setAppClientId("greenElectricityShanxiUser");
            tc.setTenantName(param.getName());
            tc.setAreaCode(param.getAreaCode());
            tc.setLeader(param.getContactName());
            tc.setMobile(param.getContactPhone());
            tc.setUserName(param.getContactPhone());
            tc.setEndTime(DateUtil.parseDate("2030-12-31"));
            TenantUserVO tenantUserVO = operationTenantApi.createTenantUser(tc).successData();
            Long id = tenantUserVO.getId();
            // 修改用户的user_side_id
            vppUser.setUserSideId(id);
        }

        return vppUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(IdDTO param) {
        // 1. 验证用户信息
        validateUserData(param.getId());

        // 2. 删除用户信息
        this.removeById(param.getId());

        // 3. 删除用户户号信息
        vppUserAccountService.deleteByUserId(param.getId());
    }

    @Override
    public VppUserInfoVO findById(IdDTO param) {
        VppUserInfoVO detailVO = new VppUserInfoVO();

        // 1. 根据用户id查询用户信息
        VppUser vppUser = this.getById(param.getId());

        if (ObjectUtil.isNotNull(vppUser)) {
            // 2. 复制用户信息到VO
            BeanCopyUtils.copy(vppUser, detailVO);

            // 3. 获取用户户号信息
            detailVO.setAccountList(vppUserAccountService.getUserAccount(vppUser.getId()));
        }
        return detailVO;
    }

    @Override
    public FxPage<VppLoadUserVO> pageList(VppUserDTO.SearchDTO param) {
        try {
            // 1. 执行分页查询
            Page<VppUser> page = this.page(new Page<>(param.getPage(), param.getPageSize()), getQueryWrapper(param));
            if (CollUtil.isEmpty(page.getRecords())) {
                return FxPage.page(new ArrayList<>(), 0L, param.getPage(), param.getPageSize());
            }

            // 2. 批量预加载所有需要的数据
            List<Long> userIds = page.getRecords().stream().map(VppUser::getId).collect(Collectors.toList());
            PreloadedData preloadedData = preloadPageListData(param.getTenantId(), userIds, param);

            // 3. 转换用户数据
            List<VppLoadUserVO> detailVOList = page.getRecords().stream()
                    .map(user -> convertToVppLoadUserVO(user, preloadedData))
                    .collect(Collectors.toList());

            return FxPage.page(detailVOList, page.getTotal(), param.getPage(), param.getPageSize());

        } catch (Exception e) {
            log.error("[pageList][查询负荷用户分页列表失败, 租户ID: {}, 错误信息: {}]",
                    param.getTenantId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<VppUserDTO> getAllUserList() {
        return baseMapper.getAllUserList();
    }

    @Override
    public List<Long> getAllUser(Long vppId) {
        return baseMapper.getAllUser(vppId);
    }

    @Override
    public VppUserDTO getLoadUserInfo(String name, Long tenantId) {
        return baseMapper.getLoadUserInfo(name, tenantId);
    }

    @Override
    public List<VppUser> getAllList(GetAllListDTO getAllListDTO) {
        LambdaQueryWrapper<VppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUser::getName, getAllListDTO.getKeysList());
        queryWrapper.in(VppUser::getTenantId, Long.valueOf(getAllListDTO.getTenantId()));
        queryWrapper.in(VppUser::getIsDelete, NOT_DELETED);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<VppUser> getAllUserList(GetAllListDTO getAllListDTO) {
        LambdaQueryWrapper<VppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppUser::getTenantId, Long.parseLong(getAllListDTO.getTenantId()));
        queryWrapper.eq(VppUser::getIsDelete, NOT_DELETED);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void resetPassword(VppUserDTO param) {
        VppUser vppUser = new VppUser();
        vppUser.setId(param.getId());
        String encode = passwordEncoder.encode(PasswordConfig.DEFAULT_PASSWORD);
        vppUser.setPassword(encode);
        baseMapper.updateById(vppUser);
    }

    /**
     * 验证用户信息
     *
     * @param id 用户id
     */
    private void validateUserData(Long id) {
        VppUser vppUser = this.getById(id);
        if (ObjectUtil.isNull(vppUser)) {
            throw new FxServiceException(USER_NOT_FOUND_ERROR);
        }
    }


    /**
     * 预加载分页列表所需的所有数据
     *
     * @param tenantId 租户ID
     * @param userIds  用户ID列表
     * @param param    查询参数
     * @return 预加载的数据
     */
    private PreloadedData preloadPageListData(Long tenantId, List<Long> userIds, VppUserDTO.SearchDTO param) {
        // 1. 批量查询用户账户信息
        List<VppUserAccount> allAccounts = vppUserAccountService.getList(tenantId);
        Map<Long, List<VppUserAccount>> accountMap = allAccounts.stream()
                .filter(account -> userIds.contains(account.getUserId()))
                .collect(Collectors.groupingBy(VppUserAccount::getUserId));

        // 2. 批量查询绑定周期信息
        Map<Long, List<VppUserBindCycle>> bindCycleMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            List<VppUserBindCycle> allBindCycles = vppBindCycleService.findCycleList(userIds);
            bindCycleMap = allBindCycles.stream()
                    .collect(Collectors.groupingBy(VppUserBindCycle::getUserId));
        }

        // 3. 批量查询红利系数信息
        List<VppRetailContractsManage> userDividendList = vppRetailContractsManageService.getUserDividendList(param);
        Map<Long, VppRetailContractsManage> dividendMap = userDividendList.stream()
                .collect(Collectors.toMap(
                        VppRetailContractsManage::getUserId,
                        dividend -> dividend,
                        (existing, replacement) -> existing
                ));

        return new PreloadedData(accountMap, bindCycleMap, dividendMap);
    }

    /**
     * 转换用户实体为VO对象
     *
     * @param user          用户实体
     * @param preloadedData 预加载的数据
     * @return 用户VO对象
     */
    private VppLoadUserVO convertToVppLoadUserVO(VppUser user, PreloadedData preloadedData) {
        VppLoadUserVO detailVO = new VppLoadUserVO();
        BeanUtil.copyProperties(user, detailVO);

        // 设置绑定状态
        setBindStatus(detailVO, preloadedData.getBindCycleMap().get(user.getId()));

        // 设置红利比例
        setDividendRatio(detailVO, preloadedData.getDividendMap().get(user.getId()));

        // 设置户号相关信息
        setAccountInfo(detailVO, preloadedData.getAccountMap().get(user.getId()));

        return detailVO;
    }

    /**
     * 设置绑定状态
     *
     * @param detailVO   用户VO
     * @param bindCycles 绑定周期列表
     */
    private void setBindStatus(VppLoadUserVO detailVO, List<VppUserBindCycle> bindCycles) {
        if (CollUtil.isEmpty(bindCycles)) {
            detailVO.setBindStatus(BindStatusEnum.UNBOUND.getCode());
            return;
        }

        Date currentDate = new Date();
        for (VppUserBindCycle cycle : bindCycles) {
            boolean isInRange = DateUtil.isIn(currentDate, cycle.getBindCycleStart(), cycle.getBindCycleEnd());
            if (isInRange) {
                detailVO.setBindStatus(BindStatusEnum.BOUND.getCode());
                return;
            }
        }
        detailVO.setBindStatus(BindStatusEnum.UNBOUND.getCode());
    }

    /**
     * 设置红利比例
     *
     * @param detailVO 用户VO
     * @param dividend 红利合约管理对象
     */
    private void setDividendRatio(VppLoadUserVO detailVO, VppRetailContractsManage dividend) {
        if (dividend == null || dividend.getDividendSharCoefficient() == null) {
            return;
        }

        BigDecimal dividendRatio = dividend.getDividendSharCoefficient()
                .setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
        detailVO.setDividendRatio(dividendRatio);
    }

    /**
     * 设置户号相关信息
     *
     * @param detailVO 用户VO
     * @param accounts 账户列表
     */
    private void setAccountInfo(VppLoadUserVO detailVO, List<VppUserAccount> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }

        // 设置户号列表
        List<String> consNos = accounts.stream()
                .map(VppUserAccount::getConsNo)
                .collect(Collectors.toList());
        detailVO.setConsNo(consNos);

        // 设置额定容量列表
        List<BigDecimal> ratedCapacities = accounts.stream()
                .map(VppUserAccount::getRatedCapacity)
                .collect(Collectors.toList());
        detailVO.setRatedCapacity(ratedCapacities);

        // 设置馈线列表
        List<String> conFeeders = accounts.stream()
                .map(VppUserAccount::getConFeeder)
                .collect(Collectors.toList());
        detailVO.setConFeeder(conFeeders);

        // 设置变电站列表
        List<String> conTransSubs = accounts.stream()
                .map(VppUserAccount::getConTransSub)
                .collect(Collectors.toList());
        detailVO.setConTransSub(conTransSubs);

        // 设置电压等级列表
        List<String> volLevels = accounts.stream()
                .map(VppUserAccount::getVolLevel)
                .map(VoltageLevelEnum::getValueByCode)
                .collect(Collectors.toList());
        detailVO.setVolLevel(volLevels);

        // 设置额定功率列表
        List<BigDecimal> ratedPowers = accounts.stream()
                .map(VppUserAccount::getRatedPower)
                .collect(Collectors.toList());
        detailVO.setRatedPower(ratedPowers);
    }

    /**
     * 获取查询条件
     *
     * @param param 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<VppUser> getQueryWrapper(VppUserDTO.SearchDTO param) {
        LambdaQueryWrapper<VppUser> vppLoadUserLqw = new LambdaQueryWrapper<>();
        vppLoadUserLqw.like(StrUtil.isNotBlank(param.getName()), VppUser::getName, param.getName());
        vppLoadUserLqw.like(StrUtil.isNotBlank(param.getUserCode()), VppUser::getUserCode, param.getUserCode());
        vppLoadUserLqw.eq(ObjectUtil.isNotNull(param.getElectricityType()), VppUser::getElectricityType, param.getElectricityType());
        vppLoadUserLqw.eq(VppUser::getTenantId, param.getTenantId());
        vppLoadUserLqw.orderByDesc(BaseEntity::getCreateTime);
        return vppLoadUserLqw;
    }

    @Override
    public VppLoadUserVO findUserName(VppUserDTO param) {
        LambdaQueryWrapper<VppUser> wrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(param.getContactPhone())) {
            wrapper.eq(VppUser::getContactPhone, param.getContactPhone());
        }
        if (ObjectUtil.isNotNull(param.getId())) {
            wrapper.eq(VppUser::getId, param.getId());
        }

        VppUser one = this.getOne(wrapper);
        VppLoadUserVO detailVO = null;
        if (ObjectUtil.isNotNull(one)) {
            detailVO = new VppLoadUserVO();
            BeanUtil.copyProperties(one, detailVO);
        }
        return detailVO;
    }


    @Override
    public List<VppUser> getListByTenantId(Long tenantId, String userName) {
        LambdaQueryWrapper<VppUser> vppUserLqw = new LambdaQueryWrapper<>();
        vppUserLqw.eq(VppUser::getTenantId, tenantId);
        vppUserLqw.eq(VppUser::getIsDelete, 0);
        vppUserLqw.like(StrUtil.isNotBlank(userName), VppUser::getName, userName);
        return list(vppUserLqw);
    }

    @Override
    public VppUser queryByUserCode(String userCode, Long tenantId) {
        LambdaQueryWrapper<VppUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.eq(VppUser::getUserCode, userCode);
        userLqw.eq(VppUser::getTenantId, tenantId);
        return this.getOne(userLqw);
    }

    /**
     * 按多字段分组
     *
     * @param value
     * @param paras
     * @return
     */
    public static String format(String value, Object... paras) {
        return MessageFormat.format(value, paras);
    }


    /**
     * 单个时间判断绑定周期
     *
     * @param queryDate   查询日期
     * @param vppUserList 用户列表
     * @return 用户列表
     */
    public List<VppUser> getUserList(Date queryDate, List<VppUser> vppUserList) {
        try {
            // 参数校验
            if (queryDate == null || CollUtil.isEmpty(vppUserList)) {
                return new ArrayList<>();
            }

            // 1. 构建用户ID映射
            Map<Long, VppUser> vppUserMap = vppUserList.stream()
                    .collect(Collectors.toMap(VppUser::getId, Function.identity(), (v1, v2) -> v1));
            List<Long> userIds = new ArrayList<>(vppUserMap.keySet());

            // 2. 批量查询用户绑定周期
            List<VppUserBindCycle> cycleList = vppBindCycleService.findCycleList(userIds);
            if (CollUtil.isEmpty(cycleList)) {
                return new ArrayList<>();
            }

            // 3. 筛选在指定日期范围内的用户
            Set<Long> validUserIds = new HashSet<>();
            for (VppUserBindCycle cycle : cycleList) {
                if (DateUtil.isIn(queryDate, cycle.getBindCycleStart(), cycle.getBindCycleEnd())) {
                    validUserIds.add(cycle.getUserId());
                }
            }

            // 4. 构建结果列表
            return validUserIds.stream()
                    .map(vppUserMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("getUserList失败", e);
            throw new RuntimeException("查询有效用户列表失败", e);
        }
    }


    public List<VppUser> getUserListByTwoDate(Date startDate, Date endDate, List<VppUser> listByTenantId) {
        try {
            // 参数校验
            if (startDate == null || endDate == null || CollUtil.isEmpty(listByTenantId)) {
                return new ArrayList<>();
            }

            // 1. 构建用户ID映射
            Map<Long, VppUser> vppUserMap = listByTenantId.stream()
                    .collect(Collectors.toMap(VppUser::getId, Function.identity(), (v1, v2) -> v1));
            List<Long> userIds = new ArrayList<>(vppUserMap.keySet());

            // 2. 批量查询用户绑定周期
            List<VppUserBindCycle> cycleList = vppBindCycleService.findCycleList(userIds);
            if (CollUtil.isEmpty(cycleList)) {
                return new ArrayList<>();
            }

            // 3. 筛选在日期范围内有交集的用户
            Set<Long> validUserIds = new HashSet<>();
            for (VppUserBindCycle cycle : cycleList) {
                // 判断查询日期范围与绑定周期是否有交集
                boolean hasOverlap = !startDate.after(cycle.getBindCycleEnd()) && !endDate.before(cycle.getBindCycleStart());
                if (hasOverlap) {
                    validUserIds.add(cycle.getUserId());
                }
            }

            // 4. 构建结果列表
            List<VppUser> resultList = validUserIds.stream()
                    .map(vppUserMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.debug("getUserListByTwoDate: 输入{}个用户，返回{}个有效用户", listByTenantId.size(), resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("getUserListByTwoDate失败", e);
            throw new RuntimeException("查询日期范围有效用户列表失败", e);
        }
    }

    @Override
    public VppLoadUserVO.TreeVO queryTreeListByDate(QueryDateDTO param) {
        // 1. 查询虚拟电厂下负荷用户列表
        List<VppUser> vppUserLists = this.getListByTenantId(param.getTenantId(), null);

        // 2. 绑定周期判断
        List<VppUser> vppUserList = getUserListByTwoDate(param.getStartDate(), param.getEndDate(), vppUserLists);

        // 3. 构建返回值
        VppLoadUserVO.TreeVO treeVO = new VppLoadUserVO.TreeVO();
        treeVO.setName("虚拟电厂");
        treeVO.setType(1);
        List<VppLoadUserVO.TreeUserVO> treeUserVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vppUserList)) {
            for (VppUser vppUser : vppUserList) {
                VppLoadUserVO.TreeUserVO treeUserVO = new VppLoadUserVO.TreeUserVO();
                BeanUtil.copyProperties(vppUser, treeUserVO);
                treeUserVO.setType(2);
                treeUserVOList.add(treeUserVO);
            }
        }
        treeVO.setTreeUserVOList(treeUserVOList);

        return treeVO;
    }

    @Override
    public List<VppUser> getUserINfoByUserCode(List<String> userCodes, Long tenantId) {
        LambdaQueryWrapper<VppUser> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(userCodes)) {
            queryWrapper.in(VppUser::getUserCode, userCodes);
            queryWrapper.eq(VppUser::getTenantId, tenantId);
            queryWrapper.eq(VppUser::getIsDelete, NOT_DELETED);
            return baseMapper.selectList(queryWrapper);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public VppUser getUser(Long userId) {
        LambdaQueryWrapper<VppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUser::getId, userId);
        queryWrapper.eq(VppUser::getIsDelete, NOT_DELETED);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public VppLoadUserVO getUserInfo(Long userId) {
        VppUser vppUser = this.getById(userId);
        return BeanUtil.copyProperties(vppUser, VppLoadUserVO.class);
    }

    @Override
    public void updatePwd(VppUserUpdateDTO vppUserUpdateDTO, Long userId) {
        VppUser vppUser = new VppUser();
        vppUser.setId(userId);
        String encode = passwordEncoder.encode(vppUserUpdateDTO.getNewPassword());
        vppUser.setPassword(encode);
        baseMapper.updateById(vppUser);
    }

    @Override
    public void updateOpenId(VppUserDTO.OpenIdDTO param) {
        //清除之前的openId
        baseMapper.updateAllOpenId(param.getOpenId());
        //将openId赋值给新的账号
        VppUser vppUser = new VppUser();
        vppUser.setId(param.getId());
        vppUser.setOpenId(param.getOpenId());
        baseMapper.updateById(vppUser);
    }

    @Override
    public List<VppUser> getAllUserCode() {
        return baseMapper.getAllUserCode();
    }

    @Override
    public List<TenantWithUserAssocDTO> getTenantWithUserAssocByMonth(Date month, String userCode) {
        // 查询所有代理关系
        List<VppUser> vppUserVOS = vppLoadUserService.getAllUserCode();
        if (StrUtil.isNotBlank(userCode)) {
            // 过滤剩下userCode相关的用户
            List<VppUser> vppUser = vppUserVOS.stream().filter(user -> userCode.equals(user.getUserCode())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(vppUser)) {
                Long tenantId = vppUser.get(0).getTenantId();
                // 过滤剩下该tenantId下的所有用户
                vppUserVOS = vppUserVOS.stream().filter(user -> tenantId.equals(user.getTenantId())).collect(Collectors.toList());
            }
        }
        List<VppUser> userList = vppLoadUserService.getUserList(month, vppUserVOS);
        return BeanCopyUtils.copyList(userList, TenantWithUserAssocDTO.class);
    }

    @Override
    public List<VppUser> getDataByIdList(Long tenantId, List<Long> userIdList) {
        if (ObjectUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VppUser> vppUserLqw = new LambdaQueryWrapper<>();
        vppUserLqw.eq(VppUser::getTenantId, tenantId);
        vppUserLqw.eq(VppUser::getIsDelete, 0);
        vppUserLqw.in(VppUser::getId, userIdList);
        return list(vppUserLqw);
    }


    @Override
    public VppLoadUserVO.TreeVO queryTreeList(AdjustDeclareDTO.QueryUserTreeDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        try {
            if (param.getQueryDate() == null) {
                throw new FxServiceException(QUERY_PARAM_ERROR);
            }

            // 1. 查询虚拟电厂下负荷用户列表
            List<VppUser> allUserList = this.getListByTenantId(tenantId, param.getName());

            // 2. 根据绑定周期过滤有效用户
            List<VppUser> validUserList = getUserList(param.getQueryDate(), allUserList);

            // 3. 构建树形用户列表
            List<VppLoadUserVO.TreeUserVO> treeUserVOList = buildTreeUserVOList(validUserList);

            // 4. 构建返回结果
            VppLoadUserVO.TreeVO result = new VppLoadUserVO.TreeVO()
                    .setName("虚拟电厂")
                    .setType(1)
                    .setTreeUserVOList(treeUserVOList);

            log.debug("queryTreeList: 租户{}查询到{}个有效用户", tenantId, treeUserVOList.size());
            return result;

        } catch (Exception e) {
            log.error("queryTreeList失败，租户ID：{}", tenantId, e);
            throw new RuntimeException("查询树形用户列表失败", e);
        }
    }

    @Override
    public VppLoadUserVO.TreeVO queryTreeAllList(AdjustDeclareDTO.QueryUserTreeDTO param) {
        // 1. 查询虚拟电厂下负荷用户列表
        List<VppUser> vppUserList = this.getListByTenantId(param.getTenantId(), param.getName());

        // 2. 构建负荷用户列表
        List<VppLoadUserVO.TreeUserVO> treeUserVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vppUserList)) {
            for (VppUser vppUser : vppUserList) {
                VppLoadUserVO.TreeUserVO treeUserVO = new VppLoadUserVO.TreeUserVO();
                BeanUtil.copyProperties(vppUser, treeUserVO);
                treeUserVO.setType(2);
                treeUserVOList.add(treeUserVO);
            }
        }

        // 3. 构建返回值
        return new VppLoadUserVO.TreeVO()
                .setName("虚拟电厂")
                .setType(1)
                .setTreeUserVOList(treeUserVOList);
    }

    @Override
    public VppLoadUserVO.TreeVO queryThreeFloorsTreeList(AdjustDeclareDTO.QueryUserTreeDTO param) {
//        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        if (param.getQueryDate() == null) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }
        // 1. 查询虚拟电厂下负荷用户列表
        List<VppUser> allUserList = this.getListByTenantId(param.getTenantId(), param.getName());

        // 2. 根据绑定周期过滤有效用户
        List<VppUser> validUserList = getUserList(param.getQueryDate(), allUserList);

        // 3.查询用户户号Map
        List<VppUserAccount> vppUserAccountList = vppUserAccountService.getList(param.getTenantId());
        Map<Long, List<VppUserAccount>> vppUserAccountMap = vppUserAccountList.stream().collect(Collectors.groupingBy(VppUserAccount::getUserId));

        // 4. 构建树形用户列表
        List<VppLoadUserVO.TreeUserVO> treeUserVOList = buildTreeUserVOList(validUserList, vppUserAccountMap);

        // 5. 构建返回结果
        VppLoadUserVO.TreeVO result = new VppLoadUserVO.TreeVO()
                .setName("虚拟电厂")
                .setType(1)
                .setTreeUserVOList(treeUserVOList);

        return result;

    }

    @Override
    public VppLoadUserVO.TreeVO queryThreeFloorsTreeAllList(AdjustDeclareDTO.QueryUserTreeDTO param) {
        // 1. 查询虚拟电厂下负荷用户列表
        List<VppUser> vppUserList = this.getListByTenantId(param.getTenantId(), param.getName());

        // 2.查询用户户号Map
        List<VppUserAccount> vppUserAccountList = vppUserAccountService.getList(param.getTenantId());
        Map<Long, List<VppUserAccount>> vppUserAccountMap = vppUserAccountList.stream().collect(Collectors.groupingBy(VppUserAccount::getUserId));

        // 3. 构建树形用户列表
        List<VppLoadUserVO.TreeUserVO> treeUserVOList = buildTreeUserVOList(vppUserList, vppUserAccountMap);

        // 4. 构建返回值
        return new VppLoadUserVO.TreeVO()
                .setName("虚拟电厂")
                .setType(1)
                .setTreeUserVOList(treeUserVOList);
    }


    @Override
    public UserAnalysisVO queryUserElectricity(UserAnalysisDTO param) {
        UserAnalysisVO result = new UserAnalysisVO();
        result.init();
        // 1. 拼接X轴时间点
        List<String> dateOfXList = new ArrayList<>();
        List<String> dateRange = MyDateUtil.getDateRange(param.getStartTime(), param.getEndTime());
        for (String date : dateRange) {
            for (String ninetySixTime : VppConstant.NINETY_SIX_TIMES) {
                dateOfXList.add(date + " " + ninetySixTime);
            }
        }
        // 根据统一信用代码查询负荷用户信息
        if (ObjectUtil.isNotNull(param.getConsNo()) && ObjectUtil.isNotEmpty(param.getConsNo())) {
            //查询户号信息
            //调用data-gather
            VppUserCodeDataQueryDTO vppUserCodeDataQueryDTO = new VppUserCodeDataQueryDTO();
            vppUserCodeDataQueryDTO.setTenantId(param.getTenantId());
            vppUserCodeDataQueryDTO.setStartTime(param.getStartTime());
            vppUserCodeDataQueryDTO.setEndTime(DateUtil.offsetDay(param.getEndTime(), 1).toJdkDate());
            vppUserCodeDataQueryDTO.setUserCode(param.getConsNo());
            vppUserCodeDataQueryDTO.setGroupType(15);
            DataResult<List<PubVppDataVO>> dataResult = dataGatherVppApi.queryUserCodeElectricity(vppUserCodeDataQueryDTO);
            // 2. 查询关口表，获取数据
            Map<String, List<PubVppDataVO>> dataGatherMapBy96 = new HashMap<>();
            log.info("{},{},查询关口表数据结果,入参：{}", this.getClass(), "queryUserAnalysis()", dataResult);
            if (ObjectUtil.isNotNull(dataResult) && dataResult.isSuccess() && CollUtil.isNotEmpty(dataResult.getData())) {
                dataGatherMapBy96 = dataResult.getData().stream().collect(Collectors.groupingBy(o -> o.getDateStr() + " " + o.getTimeFrame96()));
            }
            //查询聚合的实际用电量
            List<VppElectricActual> electricActualConvergeList = vppElectricActualService.getElectricByDateList(param.getStartTime(), param.getEndTime(), param.getTenantId(), param.getConsNo());
            Map<String, List<VppElectricActual>> realElectricGroupByDate = electricActualConvergeList.stream().collect(Collectors.groupingBy(data -> format("{0} {1}", MyDateUtil.dateToDateStr(data.getDateDay(), MyDateUtil.YYYY_MM_DD_SYMBOL), data.getTimeFrame())));
            // 3. 根据X轴时间点拼接数据
            for (String dateOfX : dateOfXList) {
                List<PubVppDataVO> pubDeviceDataSumList = dataGatherMapBy96.get(dateOfX);
                BigDecimal electricSum = null;
                if (CollUtil.isNotEmpty(pubDeviceDataSumList)) {
                    List<BigDecimal> collect = pubDeviceDataSumList.stream().map(PubVppDataVO::getElectricity).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        electricSum = collect.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                } else {
                    result.getElectricityList().add(null);
                }
                result.getElectricityList().add(DecimalPlaceUtil.formatForString(electricSum == null ? "" : DecimalPlaceUtil.decimalToStr(electricSum, DecimalPlaceEnum.TRANSITION), DecimalPlaceEnum.ELECTRIC_QUANTITY));

                // 实际用电量
                String realElectricity = "";
                List<VppElectricActual> realElectricityDataList = realElectricGroupByDate.get(dateOfX);
                if (CollUtil.isNotEmpty(realElectricityDataList)) {
                    realElectricity = DecimalPlaceUtil.decimalToStr(realElectricityDataList.stream().map(VppElectricActual::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add), DecimalPlaceEnum.ELECTRIC_QUANTITY);
                } else {
                    result.getRealElectricityList().add(null);
                }
                result.getRealElectricityList().add(realElectricity);
            }

        } else {
            VppUser vppUser = this.queryByUserCode(param.getUserCode(), param.getTenantId());
            // 2. 查询关口表，获取数据
            Map<String, List<PubVppDataVO>> dataGatherMapBy96 = new HashMap<>();
            VppUserDataQueryDTO vppUserDataQueryDTO = new VppUserDataQueryDTO();
            vppUserDataQueryDTO.setTenantId(param.getTenantId());
            vppUserDataQueryDTO.setStartTime(param.getStartTime());
            vppUserDataQueryDTO.setEndTime(DateUtil.offsetDay(param.getEndTime(), 1).toJdkDate());
            vppUserDataQueryDTO.setUserCode(param.getUserCode());
            vppUserDataQueryDTO.setGroupType(15);
            DataResult<List<PubVppDataVO>> dataResult = dataGatherVppApi.queryUserElectricityBy15(vppUserDataQueryDTO);

            log.info("{},{},查询关口表数据结果,入参：{}", this.getClass(), "queryUserAnalysis()", dataResult);
            if (ObjectUtil.isNotNull(dataResult) && dataResult.isSuccess() && CollUtil.isNotEmpty(dataResult.getData())) {
                dataGatherMapBy96 = dataResult.getData().stream().collect(Collectors.groupingBy(o -> o.getDateStr() + " " + o.getTimeFrame96()));
            }
            //查询聚合的实际用电量
            ArrayList<Long> userIdList = Lists.newArrayList(vppUser.getId());
            List<VppElectricActualConverge> electricActualConvergeList = vppElectricActualConvergeService.queryElectricByUserIdList(userIdList, param.getStartTime(), param.getEndTime(), param.getTenantId());
            Map<String, List<VppElectricActualConverge>> realElectricGroupByDate = electricActualConvergeList.stream().collect(Collectors.groupingBy(data -> format("{0} {1}", MyDateUtil.dateToDateStr(data.getDateDay(), MyDateUtil.YYYY_MM_DD_SYMBOL), data.getTimeFrame())));
            // 3. 根据X轴时间点拼接数据
            for (String dateOfX : dateOfXList) {
                List<PubVppDataVO> pubDeviceDataSumList = dataGatherMapBy96.get(dateOfX);
                BigDecimal electricSum = null;
                if (CollUtil.isNotEmpty(pubDeviceDataSumList)) {
                    List<BigDecimal> collect = pubDeviceDataSumList.stream().map(PubVppDataVO::getElectricity).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        electricSum = collect.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                result.getElectricityList().add(DecimalPlaceUtil.formatForString(electricSum == null ? "" : DecimalPlaceUtil.decimalToStr(electricSum, DecimalPlaceEnum.TRANSITION), DecimalPlaceEnum.ELECTRIC_QUANTITY));

                // 实际用电量
                String realElectricity = null;
                List<VppElectricActualConverge> realElectricityDataList = realElectricGroupByDate.get(dateOfX);
                if (CollUtil.isNotEmpty(realElectricityDataList)) {
                    realElectricity = DecimalPlaceUtil.decimalToStr(realElectricityDataList.stream().map(VppElectricActualConverge::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add), DecimalPlaceEnum.ELECTRIC_QUANTITY);
                }
                result.getRealElectricityList().add(realElectricity == null ? "" : realElectricity);
            }
        }

        dateOfXList.replaceAll(s -> s.replace("-", "/"));
        result.setDateList(dateOfXList);

        List<String> realElectricityList = result.getRealElectricityList().stream()
                .filter(s -> s != null && !s.isEmpty()).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(realElectricityList)) {
            BigDecimal realElectricityTotal = realElectricityList.stream()
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setRealElectricityTotal(realElectricityTotal);
        }

        List<String> passDataListList = result.getElectricityList().stream()
                .filter(s -> s != null && !s.isEmpty()).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(passDataListList)) {
            BigDecimal electricityTotal = passDataListList.stream()
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setElectricityTotal(electricityTotal);
        }
        return result;
    }

    /**
     * 预加载数据容器
     */
    @Data
    @AllArgsConstructor
    private static class PreloadedData {
        /**
         * 账户映射
         */
        private Map<Long, List<VppUserAccount>> accountMap;
        /**
         * 绑定周期映射
         */
        private Map<Long, List<VppUserBindCycle>> bindCycleMap;
        /**
         * 零售合同映射
         */
        private Map<Long, VppRetailContractsManage> dividendMap;
    }

    /**
     * 构建树形用户VO列表
     *
     * @param userList 用户列表
     * @return 树形用户VO列表
     */
    private List<VppLoadUserVO.TreeUserVO> buildTreeUserVOList(List<VppUser> userList) {
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }

        return userList.stream()
                .map(user -> {
                    VppLoadUserVO.TreeUserVO treeUserVO = new VppLoadUserVO.TreeUserVO();
                    BeanUtil.copyProperties(user, treeUserVO);
                    treeUserVO.setType(2); // 用户类型
                    return treeUserVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建虚拟电厂用户户号三层树形列表
     *
     * @param userList   用户列表
     * @param accountMap 账户映射
     * @return 树形用户VO列表
     */
    private List<VppLoadUserVO.TreeUserVO> buildTreeUserVOList(List<VppUser> userList, Map<Long, List<VppUserAccount>> accountMap) {
        //初始化返回List
        List<VppLoadUserVO.TreeUserVO> returnList = new ArrayList<>();
        //校验入参
        if (CollUtil.isEmpty(userList) || CollUtil.isEmpty(accountMap)) {
            return new ArrayList<>();
        }
        for (VppUser user : userList) {
            VppLoadUserVO.TreeUserVO treeUserVO = new VppLoadUserVO.TreeUserVO();
            BeanUtil.copyProperties(user, treeUserVO);
            treeUserVO.setType(2); // 类型(1-虚拟电厂,2-负荷用户,3-户号)
            //用户下放入户号信息
            List<VppUserAccount> accountList = accountMap.get(user.getId());
            List<VppLoadUserVO.TreeUserVO> accountTreeUserVOList = new ArrayList<>();
            if (CollUtil.isNotEmpty(accountList)) {
                for (VppUserAccount account : accountList) {
                    VppLoadUserVO.TreeUserVO accountTreeVO = new VppLoadUserVO.TreeUserVO();
                    accountTreeVO.setId(account.getId());
                    accountTreeVO.setConsNo(account.getConsNo());
                    accountTreeVO.setName(account.getConsNo());
                    accountTreeVO.setType(3); // 类型(1-虚拟电厂,2-负荷用户,3-户号)
                    accountTreeUserVOList.add(accountTreeVO);
                }
            }
            treeUserVO.setTreeUserVOList(accountTreeUserVOList);
            returnList.add(treeUserVO);
        }
        return returnList;
    }
}
