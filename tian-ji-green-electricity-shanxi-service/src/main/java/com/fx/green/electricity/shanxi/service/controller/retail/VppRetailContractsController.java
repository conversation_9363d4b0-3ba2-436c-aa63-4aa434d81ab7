package com.fx.green.electricity.shanxi.service.controller.retail;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.VppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 *  零售合同管理 Controller
 *
 * <AUTHOR>
 **/
@RestController
@Api(tags = "零售合同管理")
@RequestMapping("/vppRetailContracts")
public class VppRetailContractsController {

    @Resource
    private VppRetailContractsService vppRetailContractsService;

    @CommonNoRepeat
    @ApiOperation("添加")
    @PostMapping("/add")
    public DataResult<Long> add(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public DataResult<Long> delete(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("修改")
    @PostMapping("/edit")
    public DataResult<Long> edit(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("获取零售分页列表")
    @PostMapping("/page")
    public DataResult<FxPage<VppRetailContractsVO>> getRetailContractsPage(@RequestBody QueryVppRetailContractsDTO param) {
        return DataResult.success(vppRetailContractsService.getRetailContractsPage(param));
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("导入零售合同")
    @PostMapping("/importRetailContract")
    public DataResult<Void> importRetailContract(@RequestPart("file") MultipartFile file,
                                                 @RequestParam("runMonth") @ApiParam("运行月份 格式: yyyy-MM") String runMonth) {
        vppRetailContractsService.importRetailContract(file, runMonth);
        return DataResult.success();
    }

}