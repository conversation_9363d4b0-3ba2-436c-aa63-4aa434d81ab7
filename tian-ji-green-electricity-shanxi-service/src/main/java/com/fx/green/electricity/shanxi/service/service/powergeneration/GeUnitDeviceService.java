package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitDevice;

/**
 * 机组设备信息
 */
public interface GeUnitDeviceService extends IService<GeUnitDevice> {

    /**
     * 新增机组设备信息
     *
     * @param param 机组设备信息
     */
    void createUnitDevice(GeUnitDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param 设备信息
     */
    void deleteUnitDevice(IdDTO param);
}
