package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.enums.powergeneration.MeterDirectionTypeEnum;
import com.fx.green.electricity.shanxi.api.enums.powergeneration.PowerGenerationDeviceTypeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.*;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationDevice;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationDeviceUnit;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationEnterprise;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationMeter;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GePowerGenerationDeviceMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationDeviceService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationDeviceUnitService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationEnterpriseService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GePowerGenerationMeterService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发电设备信息 Service 实现类
 */
@Service
public class GePowerGenerationDeviceServiceImpl extends ServiceImpl<GePowerGenerationDeviceMapper, GePowerGenerationDevice> implements GePowerGenerationDeviceService {

    @Resource
    private GePowerGenerationDeviceUnitService gePowerGenerationDeviceUnitService;

    @Resource
    private GePowerGenerationMeterService gePowerGenerationMeterService;

    @Resource
    private GePowerGenerationEnterpriseService gePowerGenerationEnterpriseService;

    @Override
    public void createPowerGenerationDevice(GePowerGenerationDeviceSaveDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        // 1. 校验设备名称是否重复
        validateDeviceNameUnique(param.getName(), tenantId, null);

        // 2. 校验设备编号是否重复
        validateDeviceCodeUnique(param.getCode(), tenantId, null);

        // 3. 保存设备信息
        GePowerGenerationDevice gePowerGenerationDevice = new GePowerGenerationDevice();
        BeanUtil.copyProperties(param, gePowerGenerationDevice);
        gePowerGenerationDevice.setTenantId(tenantId);
        this.save(gePowerGenerationDevice);
    }

    /**
     * 校验设备编号是否重复
     *
     * @param code     设备编号
     * @param tenantId 租户ID
     * @param id       设备ID
     */
    private void validateDeviceCodeUnique(String code, Long tenantId, Long id) {
        LambdaQueryWrapper<GePowerGenerationDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        wrapper.eq(GePowerGenerationDevice::getCode, code);
        wrapper.ne(ObjUtil.isNotEmpty(id), GePowerGenerationDevice::getId, id);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_INSERT_REPEAT_ERROR.getMessage());
        }
    }

    /**
     * 校验设备名称是否重复
     *
     * @param name     设备名称
     * @param tenantId 租户ID
     * @param id       设备ID
     */
    private void validateDeviceNameUnique(String name, Long tenantId, Long id) {
        LambdaQueryWrapper<GePowerGenerationDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        wrapper.eq(GePowerGenerationDevice::getName, name);
        wrapper.ne(ObjUtil.isNotEmpty(id), GePowerGenerationDevice::getId, id);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_INSERT_REPEAT_ERROR.getMessage());
        }
    }

    /**
     * 校验设备是否存在
     *
     * @param id       设备ID
     * @param tenantId 租户ID
     */
    private void validateDeviceExists(Long id, Long tenantId) {
        LambdaQueryWrapper<GePowerGenerationDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        wrapper.eq(GePowerGenerationDevice::getId, id);
        Integer integer = baseMapper.selectCount(wrapper);

        if (integer == 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_NOT_EXIST_ERROR.getMessage());
        }
    }


    @Override
    public void deletePowerGenerationDevice(IdDTO param) {
        // 1. 校验设备是否存在
        validateDeviceExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 删除设备
        this.removeById(param.getId());
    }

    @Override
    public void updatePowerGenerationDevice(GePowerGenerationDeviceSaveDTO param) {
        // 1. 校验设备是否存在
        validateDeviceExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 校验设备编号是否重复, 且不能与当前设备编号相同
        validateDeviceCodeUnique(param.getCode(), RequestHeadersUtil.getRequestHeaders().getTenantId(), param.getId());

        // 3. 校验设备名称是否重复
        validateDeviceNameUnique(param.getName(), RequestHeadersUtil.getRequestHeaders().getTenantId(), param.getId());

        // 4. 更新设备信息
        GePowerGenerationDevice gePowerGenerationDeviceUpdate = new GePowerGenerationDevice();
        BeanUtil.copyProperties(param, gePowerGenerationDeviceUpdate);
        this.updateById(gePowerGenerationDeviceUpdate);
    }

    @Override
    public GePowerGenerationDeviceVO getPowerGenerationDevice(IdDTO param) {
        // 1. 校验设备是否存在
        validateDeviceExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 查询设备
        GePowerGenerationDevice gePowerGenerationDevice = getById(param.getId());

        return BeanUtil.copyProperties(gePowerGenerationDevice, GePowerGenerationDeviceVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void associateUnit(GePowerGenerationDeviceAssociateUnitDTO param) {
        // 1. 参数校验
        if (Objects.isNull(param.getDeviceId())) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_PARAM_ERROR.getMessage());
        }

        // 2. 校验设备是否存在
        GePowerGenerationDevice device = getById(param.getDeviceId());
        if (Objects.isNull(device)) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_NOT_EXIST_ERROR.getMessage());
        }

        // 3. 先删除该设备的所有关联关系
        gePowerGenerationDeviceUnitService.deleteByDeviceId(param.getDeviceId());

        // 4. 如果有新的机组ID列表，则保存新的关联关系
        if (!CollectionUtils.isEmpty(param.getUnitIds())) {
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

            List<GePowerGenerationDeviceUnit> deviceUnitList = param.getUnitIds().stream()
                    .filter(Objects::nonNull)
                    .map(unitId -> {
                        GePowerGenerationDeviceUnit deviceUnit = new GePowerGenerationDeviceUnit();
                        deviceUnit.setEnterpriseId(device.getEnterpriseId());
                        deviceUnit.setDeviceId(param.getDeviceId());
                        deviceUnit.setUnitId(unitId);
                        deviceUnit.setTenantId(tenantId);
                        return deviceUnit;
                    })
                    .collect(Collectors.toList());

            gePowerGenerationDeviceUnitService.batchSave(deviceUnitList);
        }
    }

    @Override
    public List<Long> getUnitList(IdDTO param) {
        return gePowerGenerationDeviceUnitService.getUnitIdsByDeviceId(param.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPowerGenerationMeter(GePowerGenerationMeterSaveDTO param) {
        // 1. 参数校验
        if (Objects.isNull(param.getDeviceId())) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_PARAM_ERROR.getMessage());
        }

        if (CollectionUtils.isEmpty(param.getMeterList())) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_PARAM_ERROR.getMessage());
        }

        // 2. 校验设备是否存在
        GePowerGenerationDevice device = getById(param.getDeviceId());
        if (Objects.isNull(device)) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.POWER_GENERATION_DEVICE_NOT_EXIST_ERROR.getMessage());
        }

        // 3. 先删除该设备已有的电表
        LambdaQueryWrapper<GePowerGenerationMeter> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(GePowerGenerationMeter::getDeviceId, param.getDeviceId());
        deleteWrapper.eq(GePowerGenerationMeter::getTenantId, RequestHeadersUtil.getRequestHeaders().getTenantId());
        gePowerGenerationMeterService.remove(deleteWrapper);

        // 4. 批量创建电表
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        List<GePowerGenerationMeter> meterList = param.getMeterList().stream()
                .map(meterDto -> {
                    GePowerGenerationMeter meter = new GePowerGenerationMeter();
                    meter.setEnterpriseId(device.getEnterpriseId());
                    meter.setDeviceId(param.getDeviceId());
                    meter.setMeterName(meterDto.getMeterName());
                    meter.setMeterNo(meterDto.getMeterNo());
                    meter.setDirectionType(meterDto.getDirectionType());
                    meter.setTenantId(tenantId);
                    return meter;
                })
                .collect(Collectors.toList());

        // 5. 批量保存电表
        gePowerGenerationMeterService.saveBatch(meterList);
    }

    @Override
    public FxPage<GePowerGenerationDevicePageVO> getPowerGenerationDevicePage(GePowerGenerationDevicePageDTO param) {
        // 1. 创建分页对象
        Page<GePowerGenerationDevice> page = new Page<>(param.getPage(), param.getPageSize());

        // 2. 构建查询条件
        LambdaQueryWrapper<GePowerGenerationDevice> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotEmpty(param.getEnterpriseName())) {
            List<GePowerGenerationEnterpriseVO> enterprises = gePowerGenerationEnterpriseService.getPowerGenerationEnterpriseListByName(param.getEnterpriseName());
            if (CollectionUtils.isEmpty(enterprises)) {
                // 如果没有找到匹配的企业，直接返回空结果
                return FxPage.page(Collections.emptyList(), 0L, param.getPage(), param.getPageSize());
            }
            queryWrapper.in(GePowerGenerationDevice::getEnterpriseId, enterprises.stream().map(GePowerGenerationEnterpriseVO::getId).collect(Collectors.toList()));
        }

        if (StrUtil.isNotEmpty(param.getName())) {
            queryWrapper.like(GePowerGenerationDevice::getName, param.getName());
        }

        if (StrUtil.isNotEmpty(param.getCode())) {
            queryWrapper.like(GePowerGenerationDevice::getCode, param.getCode());
        }

        if (ObjectUtil.isNotEmpty(param.getType())) {
            queryWrapper.eq(GePowerGenerationDevice::getType, param.getType());
        }

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        queryWrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        queryWrapper.orderByDesc(GePowerGenerationDevice::getCreateTime);

        // 3. 执行分页查询
        IPage<GePowerGenerationDevice> devicePage = page(page, queryWrapper);

        if (CollectionUtils.isEmpty(devicePage.getRecords())) {
            return BeanCopyUtils.convertToFxPage(new Page<GePowerGenerationDevicePageVO>(devicePage.getCurrent(), devicePage.getSize(), devicePage.getTotal()));
        }

        // 4. 批量查询相关数据，避免N+1问题
        List<Long> deviceIds = devicePage.getRecords().stream().map(GePowerGenerationDevice::getId).collect(Collectors.toList());
        List<Long> enterpriseIds = devicePage.getRecords().stream().map(GePowerGenerationDevice::getEnterpriseId).distinct().collect(Collectors.toList());

        // 批量查询企业信息
        List<GePowerGenerationEnterprise> enterprises = gePowerGenerationEnterpriseService.listByIds(enterpriseIds);
        Map<Long, String> enterpriseNameMap = enterprises.stream()
                .collect(Collectors.toMap(GePowerGenerationEnterprise::getId, GePowerGenerationEnterprise::getName));

        // 批量查询机组关联数量
        Map<Long, Integer> unitCountMap = gePowerGenerationDeviceUnitService.getUnitCountsByDeviceIds(deviceIds);

        // 批量查询电表数量信息
        LambdaQueryWrapper<GePowerGenerationMeter> meterQueryWrapper = new LambdaQueryWrapper<>();
        meterQueryWrapper.in(GePowerGenerationMeter::getDeviceId, deviceIds);
        meterQueryWrapper.eq(GePowerGenerationMeter::getTenantId, tenantId);
        List<GePowerGenerationMeter> allMeters = gePowerGenerationMeterService.list(meterQueryWrapper);

        // 按设备ID分组统计电表数量
        Map<Long, Long> meterCountMap = allMeters.stream()
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getDeviceId, Collectors.counting()));

        // 按设备ID和方向类型分组统计电表数量
        Map<Long, Long> upMeterCountMap = allMeters.stream()
                .filter(meter -> MeterDirectionTypeEnum.UP.getDirectionType().equals(meter.getDirectionType()))
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getDeviceId, Collectors.counting()));

        Map<Long, Long> downMeterCountMap = allMeters.stream()
                .filter(meter -> MeterDirectionTypeEnum.DOWN.getDirectionType().equals(meter.getDirectionType()))
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getDeviceId, Collectors.counting()));

        Map<Long, Long> convergenceStationMeterCountMap = allMeters.stream()
                .filter(meter -> MeterDirectionTypeEnum.CONVERGENCE_STATION.getDirectionType().equals(meter.getDirectionType()))
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getDeviceId, Collectors.counting()));

        Map<Long, Long> inverterStationMeterCountMap = allMeters.stream()
                .filter(meter -> MeterDirectionTypeEnum.INVERTER_STATION.getDirectionType().equals(meter.getDirectionType()))
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getDeviceId, Collectors.counting()));

        // 批量查询关联汇流站的设备数量
        LambdaQueryWrapper<GePowerGenerationDevice> devicesQueryWrapper = new LambdaQueryWrapper<>();
        devicesQueryWrapper.in(GePowerGenerationDevice::getParentId, deviceIds);
        devicesQueryWrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        List<GePowerGenerationDevice> parentDevices = this.list(devicesQueryWrapper);

        Map<Long, Long> storageDeviceCountMap = parentDevices.stream()
                .filter(device -> PowerGenerationDeviceTypeEnum.STORAGE.getType().equals(device.getType()))
                .collect(Collectors.groupingBy(GePowerGenerationDevice::getParentId, Collectors.counting()));

        Map<Long, Long> systemDeviceCountMap = parentDevices.stream()
                .filter(device -> PowerGenerationDeviceTypeEnum.SYSTEM.getType().equals(device.getType()))
                .collect(Collectors.groupingBy(GePowerGenerationDevice::getParentId, Collectors.counting()));

        Map<Long, Long> boosterDeviceCountMap = parentDevices.stream()
                .filter(device -> PowerGenerationDeviceTypeEnum.BOOSTER.getType().equals(device.getType()))
                .collect(Collectors.groupingBy(GePowerGenerationDevice::getParentId, Collectors.counting()));


        // 5. 转换为VO并设置关联数量
        List<GePowerGenerationDevicePageVO> voList = devicePage.getRecords().stream()
                .map(device -> {
                    GePowerGenerationDevicePageVO vo = BeanUtil.copyProperties(device, GePowerGenerationDevicePageVO.class);

                    // 设置发电企业名称
                    vo.setEnterpriseName(enterpriseNameMap.getOrDefault(device.getEnterpriseId(), ""));

                    // 设置关联机组数量
                    vo.setUnitCount(unitCountMap.getOrDefault(device.getId(), 0));

                    // 设置关联电表数量
                    vo.setMeterCount(meterCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置上网方向电表数量
                    vo.setUpMeterCount(upMeterCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置下网方向电表数量
                    vo.setDownMeterCount(downMeterCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置汇流站方向电表数量
                    vo.setConvergenceStationMeterCount(convergenceStationMeterCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置逆变站方向电表数量
                    vo.setInverterStationMeterCount(inverterStationMeterCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置关联储能设备数量
                    vo.setStorageDeviceCount(storageDeviceCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置关联系统站数量
                    vo.setSystemDeviceCount(systemDeviceCountMap.getOrDefault(device.getId(), 0L).intValue());

                    // 设置关联升压站数量
                    vo.setBoosterDeviceCount(boosterDeviceCountMap.getOrDefault(device.getId(), 0L).intValue());

                    return vo;
                })
                .collect(Collectors.toList());

        // 6. 构建返回结果
        Page<GePowerGenerationDevicePageVO> result = new Page<>(devicePage.getCurrent(), devicePage.getSize(), devicePage.getTotal());
        result.setRecords(voList);

        return BeanCopyUtils.convertToFxPage(result);
    }

    @Override
    public List<GePowerGenerationDeviceVO> getPowerGenerationDeviceList(GePowerGenerationDeviceListDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        LambdaQueryWrapper<GePowerGenerationDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        wrapper.eq(ObjectUtil.isNotEmpty(param.getType()), GePowerGenerationDevice::getType, param.getType());
        wrapper.like(StrUtil.isNotEmpty(param.getName()), GePowerGenerationDevice::getName, param.getName());
        wrapper.like(StrUtil.isNotEmpty(param.getCode()), GePowerGenerationDevice::getCode, param.getCode());
        wrapper.orderByDesc(GePowerGenerationDevice::getCreateTime);
        List<GePowerGenerationDevice> list = baseMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyToList(list, GePowerGenerationDeviceVO.class);
    }

    @Override
    public List<GePowerGenerationMeterVO> getMeterList(IdDTO param) {
        return gePowerGenerationMeterService.getMeterListByDeviceId(param.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void convergenceStationAssociate(GePowerGenerationDeviceAssociatecConvergenceStationDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        Long convergenceStationId = param.getConvergenceStationId();

        // 1. 批量清理现有关联关系
        clearExistingAssociations(convergenceStationId, tenantId);

        // 2. 收集所有需要更新的设备和新增的电表
        List<GePowerGenerationDevice> devicesToUpdate = new ArrayList<>();
        List<GePowerGenerationMeter> metersToSave = new ArrayList<>();

        // 3. 处理储能设备
        if (ObjectUtil.isNotEmpty(param.getStorageDevices())) {
            processStorageDevices(param.getStorageDevices(), convergenceStationId, tenantId, devicesToUpdate, metersToSave);
        }

        // 4. 处理系统站设备
        if (ObjectUtil.isNotEmpty(param.getSystemDevices())) {
            processSystemDevices(param.getSystemDevices(), convergenceStationId, devicesToUpdate);
        }

        // 5. 处理升压站设备
        if (ObjectUtil.isNotEmpty(param.getBoosterDevices())) {
            processBoosterDevices(param.getBoosterDevices(), convergenceStationId, tenantId, devicesToUpdate, metersToSave);
        }

        // 6. 处理用户站设备
        if (ObjectUtil.isNotEmpty(param.getGridDevices())) {
            processGridDevices(param.getGridDevices(), convergenceStationId, tenantId, metersToSave);
        }

        // 7. 批量执行数据库操作
        if (!devicesToUpdate.isEmpty()) {
            this.updateBatchById(devicesToUpdate);
        }
        if (!metersToSave.isEmpty()) {
            gePowerGenerationMeterService.saveBatch(metersToSave);
        }
    }

    /**
     * 清理现有的关联关系
     */
    private void clearExistingAssociations(Long convergenceStationId, Long tenantId) {
        // 1. 批量清理设备父级关联
        LambdaQueryWrapper<GePowerGenerationDevice> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(GePowerGenerationDevice::getParentId, convergenceStationId);
        deviceQueryWrapper.in(GePowerGenerationDevice::getType,
                PowerGenerationDeviceTypeEnum.STORAGE.getType(),
                PowerGenerationDeviceTypeEnum.SYSTEM.getType(),
                PowerGenerationDeviceTypeEnum.BOOSTER.getType());
        deviceQueryWrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);

        List<GePowerGenerationDevice> existingDevices = this.list(deviceQueryWrapper);
        if (ObjectUtil.isNotEmpty(existingDevices)) {
            existingDevices.forEach(device -> device.setParentId(null));
            this.updateBatchById(existingDevices);
        }

        // 2. 批量删除电表
        LambdaQueryWrapper<GePowerGenerationMeter> meterQueryWrapper = new LambdaQueryWrapper<>();
        meterQueryWrapper.eq(GePowerGenerationMeter::getDeviceId, convergenceStationId);
        meterQueryWrapper.eq(GePowerGenerationMeter::getTenantId, tenantId);
        gePowerGenerationMeterService.remove(meterQueryWrapper);
    }

    /**
     * 处理储能设备
     */
    private void processStorageDevices(List<GePowerGenerationDeviceAssociatecConvergenceStationDTO.StorageDeviceDTO> storageDevices,
                                       Long convergenceStationId, Long tenantId,
                                       List<GePowerGenerationDevice> devicesToUpdate,
                                       List<GePowerGenerationMeter> metersToSave) {
        for (GePowerGenerationDeviceAssociatecConvergenceStationDTO.StorageDeviceDTO storageDevice : storageDevices) {
            // 设置设备父级编号
            GePowerGenerationDevice device = new GePowerGenerationDevice();
            device.setId(storageDevice.getDeviceId());
            device.setParentId(convergenceStationId);
            devicesToUpdate.add(device);

            // 处理电表
            if (ObjectUtil.isNotEmpty(storageDevice.getMeterDevices())) {
                List<GePowerGenerationMeter> meters = createMeters(
                        storageDevice.getMeterDevices(),
                        convergenceStationId,
                        storageDevice.getDeviceId(),
                        tenantId
                );
                metersToSave.addAll(meters);
            }
        }
    }

    /**
     * 处理系统站设备
     */
    private void processSystemDevices(List<GePowerGenerationDeviceAssociatecConvergenceStationDTO.SystemDeviceDTO> systemDevices,
                                      Long convergenceStationId,
                                      List<GePowerGenerationDevice> devicesToUpdate) {
        for (GePowerGenerationDeviceAssociatecConvergenceStationDTO.SystemDeviceDTO systemDevice : systemDevices) {
            GePowerGenerationDevice device = new GePowerGenerationDevice();
            device.setId(systemDevice.getDeviceId());
            device.setParentId(convergenceStationId);
            devicesToUpdate.add(device);
        }
    }

    /**
     * 处理升压站设备
     */
    private void processBoosterDevices(List<GePowerGenerationDeviceAssociatecConvergenceStationDTO.BoosterDeviceDTO> boosterDevices,
                                       Long convergenceStationId, Long tenantId,
                                       List<GePowerGenerationDevice> devicesToUpdate,
                                       List<GePowerGenerationMeter> metersToSave) {
        for (GePowerGenerationDeviceAssociatecConvergenceStationDTO.BoosterDeviceDTO boosterDevice : boosterDevices) {
            // 设置设备父级编号
            GePowerGenerationDevice device = new GePowerGenerationDevice();
            device.setId(boosterDevice.getDeviceId());
            device.setParentId(convergenceStationId);
            devicesToUpdate.add(device);

            // 处理电表
            if (ObjectUtil.isNotEmpty(boosterDevice.getMeterDevices())) {
                List<GePowerGenerationMeter> meters = createMeters(
                        boosterDevice.getMeterDevices(),
                        convergenceStationId,
                        boosterDevice.getDeviceId(),
                        tenantId
                );
                metersToSave.addAll(meters);
            }
        }
    }

    /**
     * 处理用户站设备
     */
    private void processGridDevices(List<GePowerGenerationDeviceAssociatecConvergenceStationDTO.MeterDeviceDTO> gridDevices,
                                    Long convergenceStationId, Long tenantId,
                                    List<GePowerGenerationMeter> metersToSave) {
        // 查找或创建用户站
        Long userDeviceId = findOrCreateUserDevice(convergenceStationId, tenantId);

        // 创建电表
        List<GePowerGenerationMeter> meters = createMeters(gridDevices, convergenceStationId, userDeviceId, tenantId);
        metersToSave.addAll(meters);
    }

    /**
     * 查找或创建用户站
     */
    private Long findOrCreateUserDevice(Long convergenceStationId, Long tenantId) {
        // 查找现有用户站
        LambdaQueryWrapper<GePowerGenerationDevice> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(GePowerGenerationDevice::getParentId, convergenceStationId);
        userQueryWrapper.eq(GePowerGenerationDevice::getType, PowerGenerationDeviceTypeEnum.USER.getType());
        userQueryWrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        GePowerGenerationDevice existingUserDevice = this.getOne(userQueryWrapper);

        if (existingUserDevice != null) {
            return existingUserDevice.getId();
        }

        // 创建新的用户站
        GePowerGenerationDevice userDevice = new GePowerGenerationDevice();
        userDevice.setName("用户站");
        userDevice.setCode("用户站");
        userDevice.setAddress("用户站");
        userDevice.setLongitude(BigDecimal.ZERO);
        userDevice.setLatitude(BigDecimal.ZERO);
        userDevice.setParentId(convergenceStationId);
        userDevice.setType(PowerGenerationDeviceTypeEnum.USER.getType());
        userDevice.setTenantId(tenantId);
        this.save(userDevice);

        return userDevice.getId();
    }

    /**
     * 创建电表列表 - 提取公共逻辑
     */
    private List<GePowerGenerationMeter> createMeters(List<GePowerGenerationDeviceAssociatecConvergenceStationDTO.MeterDeviceDTO> meterDevices,
                                                      Long convergenceStationId, Long associatedDeviceId, Long tenantId) {
        return meterDevices.stream()
                .map(meterDevice -> {
                    GePowerGenerationMeter meter = new GePowerGenerationMeter();
                    meter.setDeviceId(convergenceStationId); // 汇总站
                    meter.setMeterName(meterDevice.getMeterName());
                    meter.setMeterNo(meterDevice.getMeterNo());
                    meter.setDirectionType(meterDevice.getDirectionType());
                    meter.setTenantId(tenantId);
                    meter.setAssociatedDeviceId(associatedDeviceId); // 关联设备
                    return meter;
                })
                .collect(Collectors.toList());
    }

    @Override
    public GePowerGenerationDeviceAssociatecConvergenceStationVO getConvergenceStationAssociated(IdDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        Long convergenceStationId = param.getId();

        GePowerGenerationDeviceAssociatecConvergenceStationVO result = new GePowerGenerationDeviceAssociatecConvergenceStationVO();
        result.setConvergenceStationId(convergenceStationId);

        // 1. 查询所有关联的子设备
        LambdaQueryWrapper<GePowerGenerationDevice> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(GePowerGenerationDevice::getParentId, convergenceStationId);
        deviceQueryWrapper.eq(GePowerGenerationDevice::getTenantId, tenantId);
        List<GePowerGenerationDevice> associatedDevices = this.list(deviceQueryWrapper);

        if (CollectionUtils.isEmpty(associatedDevices)) {
            return result;
        }

        // 2. 查询所有相关的电表
        List<Long> deviceIds = associatedDevices.stream().map(GePowerGenerationDevice::getId).collect(Collectors.toList());
        deviceIds.add(convergenceStationId); // 包含汇流站本身的电表

        LambdaQueryWrapper<GePowerGenerationMeter> meterQueryWrapper = new LambdaQueryWrapper<>();
        meterQueryWrapper.eq(GePowerGenerationMeter::getDeviceId, convergenceStationId);
        meterQueryWrapper.eq(GePowerGenerationMeter::getTenantId, tenantId);
        List<GePowerGenerationMeter> allMeters = gePowerGenerationMeterService.list(meterQueryWrapper);

        // 按关联设备ID分组电表
        Map<Long, List<GePowerGenerationMeter>> metersByAssociatedDevice = allMeters.stream()
                .filter(meter -> meter.getAssociatedDeviceId() != null)
                .collect(Collectors.groupingBy(GePowerGenerationMeter::getAssociatedDeviceId));

        // 3. 按设备类型分组处理
        Map<Integer, List<GePowerGenerationDevice>> devicesByType = associatedDevices.stream()
                .collect(Collectors.groupingBy(GePowerGenerationDevice::getType));

        // 4. 处理储能设备
        List<GePowerGenerationDevice> storageDevices = devicesByType.get(PowerGenerationDeviceTypeEnum.STORAGE.getType());
        if (ObjectUtil.isNotEmpty(storageDevices)) {
            List<GePowerGenerationDeviceAssociatecConvergenceStationVO.StorageDeviceDTO> storageDeviceDTOs = storageDevices.stream()
                    .map(device -> {
                        GePowerGenerationDeviceAssociatecConvergenceStationVO.StorageDeviceDTO dto = new GePowerGenerationDeviceAssociatecConvergenceStationVO.StorageDeviceDTO();
                        dto.setDeviceId(device.getId());

                        // 设置电表信息
                        List<GePowerGenerationMeter> meters = metersByAssociatedDevice.get(device.getId());
                        if (ObjectUtil.isNotEmpty(meters)) {
                            List<GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO> meterDTOs = convertToMeterDTOs(meters);
                            dto.setMeterDevices(meterDTOs);
                        }

                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setStorageDevices(storageDeviceDTOs);
        }

        // 5. 处理系统站设备
        List<GePowerGenerationDevice> systemDevices = devicesByType.get(PowerGenerationDeviceTypeEnum.SYSTEM.getType());
        if (ObjectUtil.isNotEmpty(systemDevices)) {
            List<GePowerGenerationDeviceAssociatecConvergenceStationVO.SystemDeviceDTO> systemDeviceDTOs = systemDevices.stream()
                    .map(device -> {
                        GePowerGenerationDeviceAssociatecConvergenceStationVO.SystemDeviceDTO dto = new GePowerGenerationDeviceAssociatecConvergenceStationVO.SystemDeviceDTO();
                        dto.setDeviceId(device.getId());
                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setSystemDevices(systemDeviceDTOs);
        }

        // 6. 处理升压站设备
        List<GePowerGenerationDevice> boosterDevices = devicesByType.get(PowerGenerationDeviceTypeEnum.BOOSTER.getType());
        if (ObjectUtil.isNotEmpty(boosterDevices)) {
            List<GePowerGenerationDeviceAssociatecConvergenceStationVO.BoosterDeviceDTO> boosterDeviceDTOs = boosterDevices.stream()
                    .map(device -> {
                        GePowerGenerationDeviceAssociatecConvergenceStationVO.BoosterDeviceDTO dto = new GePowerGenerationDeviceAssociatecConvergenceStationVO.BoosterDeviceDTO();
                        dto.setDeviceId(device.getId());

                        // 设置电表信息
                        List<GePowerGenerationMeter> meters = metersByAssociatedDevice.get(device.getId());
                        if (ObjectUtil.isNotEmpty(meters)) {
                            List<GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO> meterDTOs = convertToMeterDTOs(meters);
                            dto.setMeterDevices(meterDTOs);
                        }

                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setBoosterDevices(boosterDeviceDTOs);
        }

        // 7. 处理用户站设备（用户站的电表）
        List<GePowerGenerationDevice> userDevices = devicesByType.get(PowerGenerationDeviceTypeEnum.USER.getType());
        if (ObjectUtil.isNotEmpty(userDevices)) {
            // 用户站可能有多个，但通常只有一个，取第一个的电表作为gridDevices
            GePowerGenerationDevice userDevice = userDevices.get(0);
            List<GePowerGenerationMeter> userMeters = metersByAssociatedDevice.get(userDevice.getId());
            if (ObjectUtil.isNotEmpty(userMeters)) {
                List<GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO> gridMeterDTOs = convertToMeterDTOs(userMeters);
                result.setGridDevices(gridMeterDTOs);
            }
        }

        return result;
    }

    /**
     * 转换电表实体为DTO
     */
    private List<GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO> convertToMeterDTOs(List<GePowerGenerationMeter> meters) {
        return meters.stream()
                .map(meter -> {
                    GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO dto = new GePowerGenerationDeviceAssociatecConvergenceStationVO.MeterDeviceDTO();
                    dto.setMeterName(meter.getMeterName());
                    dto.setMeterNo(meter.getMeterNo());
                    dto.setDirectionType(meter.getDirectionType());
                    return dto;
                })
                .collect(Collectors.toList());
    }


}
