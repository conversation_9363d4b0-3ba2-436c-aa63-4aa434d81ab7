package com.fx.green.electricity.shanxi.service.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.data.gather.api.dto.vpp.TenantWithUserAssocDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.UserAnalysisVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 负荷用户管理 Service 接口
 * 
 * <AUTHOR>
 **/
public interface VppLoadUserService extends IService<VppUser> {

    /**
     * 保存或修改基础信息
     *
     * @param param 负荷用户DTO
     * @return      用户 ID
     */
    Long saveOrUpdate(VppLoadUserDTO param);

    /**
     * 根据id删除
     *
     * @param param 负荷用户id
     */
    void delete(IdDTO param);

    /**
     * 获取用户信息
     * @param param 用户ID
     * @return 用户信息
     */
    VppUserInfoVO findById(IdDTO param);

    /**
     * 分页查询负荷用户
     *
     * @param param 筛选条件
     * @return 负荷用户
     */
    FxPage<VppLoadUserVO> pageList(VppUserDTO.SearchDTO param);

    /**
     * 根据tenantId查询负荷用户列表
     *
     * @param tenantId 租户id
     * @param name     租户名字
     * @return 负荷用户列表
     */
    List<VppUser> getListByTenantId(Long tenantId, String name);

    /**
     * 根据统一信用代码查询
     *
     * @param userCode 统一信用代码
     * @return 用户信息
     */
    VppUser queryByUserCode(String userCode, Long tenantId);

    /**
     * 登录获取用户信息
     *
     * @param param 用户DTO
     * @return 用户信息
     */
    VppLoadUserVO findUserName(VppUserDTO param);


    /**
     * 获取用户详细信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    VppLoadUserVO getUserInfo(Long userId);

    /**
     * 修改密码
     *
     * @param vppUserUpdateDTO 用户DTO
     * @param userId 用户ID
     */
    void updatePwd(VppUserUpdateDTO vppUserUpdateDTO, Long userId);

    /**
     * 修改负荷用户的openId
     *
     * @param param 用户ID和openId
     */
    void updateOpenId(VppUserDTO.OpenIdDTO param);

    /**
     * 查询所有统一信用代码
     *
     * @return 所有统一信用代码
     */
    List<VppUser> getAllUserCode();

    /**
     * 获取全部用户信息
     *
     * @return 全部用户信息
     */
    List<VppUserDTO> getAllUserList();

    /**
     * 获取全部用户信息
     *
     * @param vppId 虚拟电厂ID
     * @return
     */
    List<Long> getAllUser(Long vppId);


    /**
     * 通过负荷用户名字获取信息
     *
     * @param name 用户名字
     * @return
     */
    VppUserDTO getLoadUserInfo(String name, Long tenantId);


    /**
     * 获取用户信息
     *
     * @param getAllListDTO 获取全部用户信息DTO
     * @return
     */
    List<VppUser> getAllList(GetAllListDTO getAllListDTO);

    /**
     * 根据租户id获取所有用户信息
     *
     * @return 全部用户信息
     */
    List<VppUser> getAllUserList(GetAllListDTO getAllListDTO);


    /**
     * 重置密码
     *
     * @param param 用户DTO
     */
    void resetPassword(VppUserDTO param);

    /**
     * 单个时间判断绑定周期
     *
     * @param vppUserList 用户列表
     * @return 用户列表
     */
    List<VppUser> getUserList(Date queryDate, List<VppUser> vppUserList);

    /**
     * 两个时间获取绑定周期
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param listByTenantId 用户列表
     * @return 用户列表
     */
    List<VppUser> getUserListByTwoDate(Date startDate, Date endDate, List<VppUser> listByTenantId);

    /**
     * 获取多天的多个用户的绑定周期
     *
     * @param param 用户DTO
     * @return 用户树形结构列表
     */
    VppLoadUserVO.TreeVO queryTreeListByDate(QueryDateDTO param);

    /**
     * 根据统一信用代码获取用户信息
     *
     * @param userCodes 统一信用代码列表
     * @param tenantId  租户id
     * @return 用户列表
     */
    List<VppUser> getUserINfoByUserCode(List<String> userCodes, Long tenantId);

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    VppUser getUser(Long userId);

    /**
     * 按月进行租户与用电用户关联信息统计
     *
     * @return 关联列表
     */
    List<TenantWithUserAssocDTO> getTenantWithUserAssocByMonth(Date month, String userCode);


    /**
     * 根据租户id和用户id列表获取用户列表
     * @param tenantId 租户id
     * @param userIdList 用户id列表
     * @return 用户列表
     */
    List<VppUser> getDataByIdList(Long tenantId, List<Long> userIdList);

    /**
     * 获取用户或者户号的电量信息
     * @param param 用户DTO
     * @return 用户电量信息
     */
    UserAnalysisVO queryUserElectricity(@Valid UserAnalysisDTO param);

    /**
     * 查询用户树形结构列表
     *
     * @param param 查询用户树形结构DTO
     * @return 用户树形结构列表
     */
    VppLoadUserVO.TreeVO queryTreeList(AdjustDeclareDTO.QueryUserTreeDTO param);


    /**
     * 查询用户树形结构列表（不看绑定周期  全部用户）
     *
     * @param param 查询用户树形结构DTO
     * @return 用户树形结构列表
     */
    VppLoadUserVO.TreeVO queryTreeAllList(AdjustDeclareDTO.QueryUserTreeDTO param);

    /**
     * 获取虚拟电厂用户户号三层树状列表
     *
     * @param param 查询参数
     * @return 树状列表
     */
    VppLoadUserVO.TreeVO queryThreeFloorsTreeList(AdjustDeclareDTO.QueryUserTreeDTO param);

    /**
     * 获取虚拟电厂用户户号三层树状列表（不看绑定周期  全部用户）
     *
     * @param param 查询参数
     * @return 树状列表
     */
    VppLoadUserVO.TreeVO queryThreeFloorsTreeAllList(AdjustDeclareDTO.QueryUserTreeDTO param);

}
