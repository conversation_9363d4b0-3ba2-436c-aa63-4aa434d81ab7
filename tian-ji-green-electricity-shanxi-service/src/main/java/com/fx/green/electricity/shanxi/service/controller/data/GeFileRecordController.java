package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.service.service.data.GeFileRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 文件记录数据 Controller
 */
@RestController
@Api(tags = "数据维护 - 文件记录数据")
@RequestMapping("/geFileRecord")
public class GeFileRecordController {

    @Resource
    private GeFileRecordService geFileRecordService;

    @ApiOperation("导入文件记录表")
    @PostMapping("/insertFileRecord")
    public DataResult<Void> insertFileRecord(@RequestBody List<GeFileRecordDTO> param) {
        geFileRecordService.insertFileRecord(param);
        return DataResult.success();
    }

    @ApiOperation("文件维护记录")
    @PostMapping("/getFileRecordByDay")
    public DataResult<List<GeDayRecordVO>> getFileRecordByDay(@RequestBody GeFileRecordDTO param) {
        List<GeDayRecordVO> fileRecordByDay = geFileRecordService.getFileRecordByDay(param);
        return DataResult.success(fileRecordByDay);
    }

    @ApiOperation("文件列表状态")
    @PostMapping("/fileStatus")
    public DataResult<List<GeFileRecordVO>> fileStatus(@RequestBody GeFileRecordDTO param) {
        List<GeFileRecordVO> geFileRecordVOS = geFileRecordService.fileStatus(param);
        return DataResult.success(geFileRecordVOS);
    }

    @ApiOperation("获取当日下数据")
    @PostMapping("/getInfoDataValue")
    public DataResult<Map<String, String>> getInfoDataValue(@RequestBody GeFileRecordDTO param) {
        Map<String, String> infoDataValue = geFileRecordService.getInfoDataValue(param);
        return DataResult.success(infoDataValue);
    }
}
