package com.fx.green.electricity.shanxi.service.service.elemanage;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;

/**
 *
 **/
public interface VppEleManageService{

    DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(QueryUserTreeListDTO param);

    DataResult<EleManageVO> queryEleManage(QueryEleManageDTO param);
}
