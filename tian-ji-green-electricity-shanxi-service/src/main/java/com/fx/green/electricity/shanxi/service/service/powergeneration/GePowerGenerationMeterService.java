package com.fx.green.electricity.shanxi.service.service.powergeneration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationMeterSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GePowerGenerationMeter;

import java.util.List;


/**
 * 发电电表 Service 接口
 *
 * <AUTHOR>
 */
public interface GePowerGenerationMeterService extends IService<GePowerGenerationMeter> {

    /**
     * 新增发电电表信息
     *
     * @param param 发电电表保存DTO
     */
    void createPowerGenerationMeter(GePowerGenerationMeterSaveDTO param);

    /**
     * 根据发电设备id查询关联电表列表
     *
     * @param deviceId 发电设备id
     * @return 关联电表列表
     */
    List<GePowerGenerationMeterVO> getMeterListByDeviceId(Long deviceId);
}
