package com.fx.green.electricity.shanxi.service.service.data;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.service.entity.data.VppDataMaintenance;

import java.util.Date;
import java.util.List;

/**
 * 数据维护 Service 接口
 */
public interface DataMaintenanceService extends IService<VppDataMaintenance> {

    /**
     * 查询数据维护列表（月）
     *
     * @param param 数据维护DTO
     * @return 数据维护列表
     */
    List<DataMaintenanceVO> queryList(DataMaintenanceDTO param);

    /**
     * 获取即将失效的用户
     */

    List<ExpireUserVO> getExpireUserList(DataMaintenanceDTO param);

    /**
     * 修改数据维护状态
     *
     * @param param 数据维护DTO
     */
    void updateStatus(DataMaintenanceUpdateDTO param);


    /**
     * 获取生效的用户的数量
     * @param param
     * @return
     */
    Integer geUserCount(DataMaintenanceDTO param);

    /**
     * 获取虚拟电厂最近一天有实际用电量的日期
     * @param userId
     * @return
     */
    Date getdateDay(Long tenantId);


}
