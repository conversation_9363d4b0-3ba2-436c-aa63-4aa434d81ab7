package com.fx.green.electricity.shanxi.service.service.powergeneration.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitDeviceVO;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeContractMaintenance;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.entity.powergeneration.GeUnitDevice;
import com.fx.green.electricity.shanxi.service.mapper.powergeneration.GeUnitBasicMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitBasicService;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeUnitDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 机组信息维护 Service 实现类
 */
@Service
public class GeUnitBasicServiceImpl extends ServiceImpl<GeUnitBasicMapper, GeUnitBasic> implements GeUnitBasicService {

    @Resource
    private GeUnitDeviceService geUnitDeviceService;

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @Override
    public void insertUnitBase(GeUnitBasicDTO param) {
        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();

        // 1. 校验机组名称是否重复
        validateUnitNameUnique(param.getUnitName(), tenantId);

        // 2. 保存机组信息
        GeUnitBasic geUnitBasic = new GeUnitBasic();
        BeanUtil.copyProperties(param, geUnitBasic);
        geUnitBasic.setTenantId(tenantId);
        baseMapper.insert(geUnitBasic);
    }

    /**
     * 校验机组名称是否重复
     *
     * @param unitName 机组名称
     * @param tenantId 租户ID
     */
    private void validateUnitNameUnique(String unitName, Long tenantId) {
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getUnitName, unitName);
        wrapper.eq(GeUnitBasic::getTenantId, tenantId);
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_BASIC_INSERT_REPEAT_ERROR.getMessage());
        }
    }

    @Override
    public void deleteUnitBase(IdDTO param) {
        // 1. 先查询机组下是否存在设备
        validateUnitDeviceExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 2. 再查询机组下是否存在合同
        validateUnitContractExists(param.getId(), RequestHeadersUtil.getRequestHeaders().getTenantId());

        // 3. 删除机组
        baseMapper.deleteById(param.getId());
    }

    /**
     * 校验机组下是否存在设备
     *
     * @param unitId   机组ID
     * @param tenantId 租户ID
     */
    private void validateUnitDeviceExists(Long unitId, Long tenantId) {
        LambdaQueryWrapper<GeUnitDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitDevice::getUnitId, unitId);
        wrapper.eq(GeUnitDevice::getTenantId, tenantId);
        Integer integer = geUnitDeviceService.getBaseMapper().selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_BASIC_DEVICE_DELETE_ERROR.getMessage());
        }
    }

    /**
     * 校验机组下是否存在合同
     *
     * @param unitId   机组ID
     * @param tenantId 租户ID
     */
    private void validateUnitContractExists(Long unitId, Long tenantId) {
        LambdaQueryWrapper<GeContractMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeContractMaintenance::getUnitId, unitId);
        wrapper.eq(GeContractMaintenance::getTenantId, tenantId);
        Integer integer = geContractMaintenanceService.getBaseMapper().selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UNIT_BASIC_CONTRACT_DELETE_ERROR.getMessage());
        }
    }

    @Override
    public List<GeUnitBasicVO> unitBaseList(IdDTO param) {
        // 1. 查询机组信息
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getEnterpriseId, param.getId());
        List<GeUnitBasic> geUnitBasics = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(geUnitBasics)) {
            return Collections.emptyList();
        }

        // 2. 查询设备信息
        LambdaQueryWrapper<GeUnitDevice> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(GeUnitDevice::getUnitId, geUnitBasics.stream().map(GeUnitBasic::getId).collect(Collectors.toList()));
        List<GeUnitDevice> geUnitDevices = geUnitDeviceService.getBaseMapper().selectList(deviceWrapper);
        Map<Long, List<GeUnitDeviceVO>> deviceMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(geUnitDevices)) {
            deviceMap = geUnitDevices.stream().map(t -> {
                GeUnitDeviceVO geUnitDeviceVO = new GeUnitDeviceVO();
                BeanUtil.copyProperties(t, geUnitDeviceVO);
                return geUnitDeviceVO;
            }).collect(Collectors.groupingBy(GeUnitDeviceVO::getUnitId));
        }

        // 3. 组装返回结果
        List<GeUnitBasicVO> resultList = new ArrayList<>();
        for (GeUnitBasic geUnitBasic : geUnitBasics) {
            GeUnitBasicVO geUnitBasicVO = new GeUnitBasicVO();
            BeanUtil.copyProperties(geUnitBasic, geUnitBasicVO);
            geUnitBasicVO.setDeviceList(deviceMap.getOrDefault(geUnitBasic.getId(), Collections.emptyList()));
            resultList.add(geUnitBasicVO);
        }
        return resultList;
    }

    @Override
    public List<GeUnitBasicVO> getUnitBasicListByIds(List<Long> unitIds) {
        // 1. 查询机组信息
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeUnitBasic::getId, unitIds);
        List<GeUnitBasic> geUnitBasics = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(geUnitBasics)) {
            return Collections.emptyList();
        }
        return geUnitBasics.stream().map(t -> {
            GeUnitBasicVO geUnitBasicVO = new GeUnitBasicVO();
            BeanUtil.copyProperties(t, geUnitBasicVO);
            return geUnitBasicVO;
        }).collect(Collectors.toList());
    }
}
