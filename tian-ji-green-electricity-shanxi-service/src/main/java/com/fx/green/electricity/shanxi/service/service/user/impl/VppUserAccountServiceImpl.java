package com.fx.green.electricity.shanxi.service.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserAccountDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserAccountVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserAccount;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserAccountMapper;
import com.fx.green.electricity.shanxi.service.service.user.VppUserAccountService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.USER_CONS_NO_ERROR;


/**
 * 用户户号 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppUserAccountServiceImpl extends ServiceImpl<VppUserAccountMapper, VppUserAccount> implements VppUserAccountService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateData(VppLoadUserDTO param) {
        if (param.getAccountList() == null || param.getAccountList().isEmpty()) {
            return;
        }

        // 1. 检查传入的户号列表内部是否有重复
        boolean hasDuplicateConsNo = this.hasDuplicateConsNoInList(param.getAccountList());
        if (hasDuplicateConsNo) {
            throw new FxServiceException(USER_CONS_NO_ERROR);
        }

        // 2. 判断户号是否已经绑定了其他虚拟电厂或者用户
        boolean userAccountFlag = this.existsUserAccount(param.getAccountList(), param.getId());
        if (userAccountFlag) {
            throw new FxServiceException(USER_CONS_NO_ERROR);
        }

        // 3. 保存或更新户号
        List<VppUserAccountDTO> accountList = param.getAccountList();
        for (VppUserAccountDTO vppUserAccountDTO : accountList) {
            VppUserAccount vppUserAccount = BeanCopyUtils.copy(vppUserAccountDTO, VppUserAccount.class);
            vppUserAccount.setUserId(param.getId());
            vppUserAccount.setTenantId(param.getTenantId());
            saveOrUpdate(vppUserAccount);
        }
    }

    @Override
    public List<VppUserAccount> getList(Long tenantId) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getTenantId, tenantId);
        queryWrapper.in(VppUserAccount::getIsDelete, 0);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByUserId(Long vppUserId) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getUserId, vppUserId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<VppUserAccountVO> getUserAccount(Long id) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getUserId, id);
        List<VppUserAccount> vppUserAccounts = baseMapper.selectList(queryWrapper);
        return BeanCopyUtils.copyList(vppUserAccounts, VppUserAccountVO.class);
    }


    /**
     * 检查传入的户号列表内部是否有重复户号
     *
     * @param accountList 户号列表
     * @return 是否有重复户号
     */
    private boolean hasDuplicateConsNoInList(List<VppUserAccountDTO> accountList) {
        // 获取所有户号
        List<String> consNoList = accountList.stream()
                .map(VppUserAccountDTO::getConsNo)
                .filter(consNo -> consNo != null && !consNo.trim().isEmpty())
                .collect(Collectors.toList());

        // 使用Set检查是否有重复
        Set<String> consNoSet = new HashSet<>(consNoList);
        return consNoSet.size() != consNoList.size();
    }

    /**
     * 判断户号是否已经绑定了其他虚拟电厂或者用户
     *
     * @param accountList 户号列表
     * @param currentUserId 当前用户ID
     * @return 是否已经绑定了其他虚拟电厂或者用户
     */
    public boolean existsUserAccount(List<VppUserAccountDTO> accountList, Long currentUserId) {
        // 1. 获取户号列表
        List<String> consNoList = accountList.stream()
                .map(VppUserAccountDTO::getConsNo)
                .filter(consNo -> consNo != null && !consNo.trim().isEmpty())
                .collect(Collectors.toList());

        if (consNoList.isEmpty()) {
            return false;
        }

        // 2. 获取有效的户号ID列表（过滤掉null值）
        List<Long> idList = accountList.stream()
                .map(VppUserAccountDTO::getId)
                .filter(id -> id != null)
                .collect(Collectors.toList());

        // 3. 查询户号是否已经绑定了其他用户
        LambdaQueryWrapper<VppUserAccount> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(VppUserAccount::getConsNo, consNoList);
        userLqw.eq(VppUserAccount::getIsDelete, 0);

        // 排除当前用户的户号记录
        if (currentUserId != null) {
            userLqw.ne(VppUserAccount::getUserId, currentUserId);
        }

        // 排除当前正在更新的户号记录
        if (!idList.isEmpty()) {
            userLqw.notIn(VppUserAccount::getId, idList);
        }

        int count = this.count(userLqw);
        return count > 0;
    }
}
