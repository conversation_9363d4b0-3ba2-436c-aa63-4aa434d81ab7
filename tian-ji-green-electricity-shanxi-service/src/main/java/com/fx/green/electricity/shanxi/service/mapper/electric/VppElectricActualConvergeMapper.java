package com.fx.green.electricity.shanxi.service.mapper.electric;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface VppElectricActualConvergeMapper extends BaseMapper<VppElectricActualConverge> {

    /**
     * 获取用户数据
     *
     * @param userIdList
     * @param startTime
     * @param endTime
     * @param tenantId
     * @return
     */
    List<VppElectricActualConverge> queryElectricByUserIdLists(@Param("userIdList") List<Long> userIdList,
                                                               @Param("startTime") Date startTime,
                                                               @Param("endTime") Date endTime,
                                                               @Param("tenantId") Long tenantId);

    /**
     * 获取用户数据
     *
     * @param userIdList
     * @param startTime
     * @param endTime
     * @param tenantId
     * @return
     */
    List<VppElectricActualConverge> queryElectricByUserIdListNew(@Param("userIdList") List<Long> userIdList,
                                                                 @Param("startTime") Date startTime,
                                                                 @Param("endTime") Date endTime,
                                                                 @Param("tenantId") Long tenantId);

    /**
     * 根据日期和租户 id 删除实际用电量合并
     *
     * @param dateDay  日期
     * @param tenantId 租户id
     */
    void removeByParam(@Param("dateDay") Date dateDay, @Param("tenantId") Long tenantId);

    List<VppElectricActualConverge> getElectricByDate(@Param("tenantId") Long tenantId,
                                                      @Param("startTime") DateTime startTime,
                                                      @Param("endTime") DateTime endTime);
}

