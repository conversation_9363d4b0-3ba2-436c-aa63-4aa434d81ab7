package com.fx.green.electricity.shanxi.service.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserElectricityContract;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户电量合同 Mapper
 *
 * <AUTHOR>
 **/
@Repository
public interface VppUserElectricityContractMapper extends BaseMapper<VppUserElectricityContract> {

    /**
     * 获取用户电量合同列表
     *
     * @param param 用户电量合同DTO
     * @return 用户电量合同列表
     */
    List<VppUserElectricityContractVO> getUserElectricityContractList(@Param("param") VppUserElectricityContractDTO param);

    /**
     * 获取用户电量合同分页列表
     *
     * @param page  分页参数
     * @param param 用户电量合同DTO
     * @return 用户电量合同分页列表
     */
    IPage<VppUserElectricityContractVO> getUserElectricityContractPage(IPage<VppUserElectricityContractVO> page, @Param("param") VppUserElectricityContractDTO param);
}
