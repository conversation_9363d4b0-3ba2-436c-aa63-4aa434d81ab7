package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发电电表
 *
 * <AUTHOR>
 **/
@TableName("ge_power_generation_meter")
@EqualsAndHashCode(callSuper = true)
@Data
public class GePowerGenerationMeter extends TenantBaseEntity {

    /**
     * 电表编号
     */
    private Long id;

    /**
     * 发电企业编号
     * <p>
     * 关联 {@link GePowerGenerationEnterprise#getId()}
     */
    private Long enterpriseId;

    /**
     * 发电设备编号
     * <p>
     * 关联 {@link GePowerGenerationDevice#getId()}
     */
    private Long deviceId;

    /**
     * 电表名称
     */
    private String meterName;

    /**
     * 电表表号
     */
    private String meterNo;

    /**
     * 方向类型
     * 方向类型(0 无方向、1汇流站方向/上网方向、2逆变站方向/下网方向)
     * <p>
     * 枚举 {@link com.fx.green.electricity.shanxi.api.enums.powergeneration.MeterDirectionTypeEnum}
     */
    private Integer directionType;

    /**
     * 汇总站关联的设备编号
     * <p>
     * 关联 {@link GePowerGenerationDevice#getId()}
     */
    private Long associatedDeviceId;

}