package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 机组设备表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("机组设备表")
public class GeUnitDevice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -525886433686176836L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("机组id")
    private Long unitId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备编号")
    private String deviceCode;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经纬度")
    private String coordinate;

}
