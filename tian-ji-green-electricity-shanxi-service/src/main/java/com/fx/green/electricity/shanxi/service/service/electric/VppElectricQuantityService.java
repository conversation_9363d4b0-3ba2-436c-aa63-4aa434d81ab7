package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.WholesaleAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 电量记录 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppElectricQuantityService extends IService<VppElectricQuantity> {

    /**
     * 导入实际用电量
     *
     * @param file 文件
     * @return 结果
     */
    ImportExcelVO importActualElectricityConsumption(MultipartFile file);

    /**
     * 获取实际用电量详情
     *
     * @param param 查询参数
     * @return 实际用电量详情
     */
    List<ElectricityDetailVO> getActualElectricityConsumptionDetail(DataMaintenanceQueryDTO param);

    /**
     * 删除实际用电量
     *
     * @param param 查询参数
     */
    void deleteActualElectricityConsumption(DataMaintenanceQueryDTO param);

    /**
     * 根据时间和类型查询详情
     *
     * @param param 时间、类型
     * @return 查询结果
     */
    VppElectricQuantityVO findByTimeAndType(VppElectricQuantityDTO param);

    /**
     * 根据id删除
     *
     * @param param id
     */
    void delete(IdDTO param);

    /**
     * 获取导入分页列表
     *
     * @param param
     * @return
     */
    FxPage<ImportExcelDetailPageListVO> queryImportRecord(VppElectricQuantityDTO.QueryDTO param);

    /**
     * 申报电量导入
     *
     * @param file 文件
     * @param time 时间
     * @return 导入结果
     */
    ImportExcelVO importRecord(MultipartFile file, String time);

    /**
     * 获取申报电量 通过vppId
     *
     * @param param
     */
    List<VppElectricDeclareVO> getAllReportElectricity(WholesaleAnalysisDTO param);

    /**
     * 删除莫一天的申报数据
     *
     * @param deleteRecordDTO
     * @return
     */
    DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载日前申报数据
     *
     * @param param
     * @return
     */
    DataResult<List<SeElectricDeclareVO>> downloadRecord(CommonDTO.DateDTO param);
}
