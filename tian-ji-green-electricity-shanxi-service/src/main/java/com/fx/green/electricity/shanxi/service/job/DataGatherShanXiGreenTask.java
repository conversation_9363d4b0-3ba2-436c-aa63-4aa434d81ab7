package com.fx.green.electricity.shanxi.service.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fx.green.electricity.shanxi.service.service.data.DataGatherService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 计算虚拟电厂关口表总数
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class DataGatherShanXiGreenTask {

    @Resource
    private DataGatherService dataGatherService;

    /**
     * 每1分钟计算5分钟以内的负荷电量聚合计算至虚拟电厂表中
     *
     * @return 结果
     */
    @XxlJob("minuteGroupData")
    public ReturnT<String> minuteGroupData(String params) {
        log.info("DataGatherTask.minuteGroupData params: 开始执行");
        try {
//            RpcContext.getContext().setAttachment(DubboConstant.PROVINCE_TAG_KEY, "hubei");
            dataGatherService.minuteGroupData();
        } catch (Exception e) {
            log.error("DataGatherTask.minuteGroupData 执行异常：", e);
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setContent(e.getMessage());
            return fail;
        }
        log.info("DataGatherTask.minuteGroupData params: 执行结束");
        return ReturnT.SUCCESS;
    }

    /**
     * 每天计算昨天的负荷电量聚合计算至虚拟电厂表中
     *
     * @return 结果
     */
    @XxlJob("dayGroupData")
    public ReturnT<String> dayGroupData(String params) {
        log.info("DataGatherTask.dayGroupData params: 开始执行");
        try {
//            RpcContext.getContext().setAttachment(DubboConstant.PROVINCE_TAG_KEY, "hubei");
            dataGatherService.dayGroupData();
        } catch (Exception e) {
            log.error("DataGatherTask.dayGroupData 执行异常：", e);
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setContent(e.getMessage());
            return fail;
        }
        log.info("DataGatherTask.dayGroupData params: 执行结束");
        return ReturnT.SUCCESS;
    }

    /**
     * 传入开始时间结束时间
     * {"startTime": "2024-12-05 00:00:00", "endTime": "2024-12-06 00:00:00"}
     *
     * @return 结果
     */
    @XxlJob("dateGroupData")
    public ReturnT<String> dateGroupData(String params) {
        log.info("DataGroupJob.dateGroupData params: 开始执行");
        try {
//            RpcContext.getContext().setAttachment(DubboConstant.PROVINCE_TAG_KEY, "hubei");
            JSONObject dateJson = JSONUtil.parseObj(params);
            Date endTime = dateJson.getDate("endTime");
            endTime = DateUtil.offsetDay(endTime, 1);
            dataGatherService.dateGroupData(dateJson.getDate("startTime"), endTime);
        } catch (Exception e) {
            log.error("DataGroupJob.dateGroupData 执行异常：", e);
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setContent(e.getMessage());
            return fail;
        }
        log.info("DataGroupJob.dateGroupData params: 执行结束");
        return ReturnT.SUCCESS;
    }


}
