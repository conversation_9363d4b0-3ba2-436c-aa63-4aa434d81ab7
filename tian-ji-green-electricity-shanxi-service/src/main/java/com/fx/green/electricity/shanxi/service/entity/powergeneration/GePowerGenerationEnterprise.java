package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.fx.green.electricity.shanxi.service.entity.TenantBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发电企业信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("发电企业信息")
public class GePowerGenerationEnterprise extends TenantBaseEntity {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("联系人")
    private String leader;

    @ApiModelProperty("联系方式")
    private String phone;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("地区名称")
    private String areaName;

}
