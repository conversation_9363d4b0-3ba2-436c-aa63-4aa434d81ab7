package com.fx.green.electricity.shanxi.service.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserElectricityContract;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserElectricityContractUnit;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserElectricityContractMapper;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserElectricityContractUnitMapper;
import com.fx.green.electricity.shanxi.service.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.user.VppUserElectricityContractService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 用户电量合同 Service 实现
 *
 * <AUTHOR>
 **/
@Service
public class VppUserElectricityContractServiceImpl extends ServiceImpl<VppUserElectricityContractMapper, VppUserElectricityContract> implements VppUserElectricityContractService {

    @Resource
    private VppUserElectricityContractUnitMapper vppUserElectricityContractUnitMapper;

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @Override
    public FxPage<VppUserElectricityContractVO> getUserElectricityContractPage(VppUserElectricityContractDTO param) {
        // 1. 创建分页对象并获取合同数据
        Page<VppUserElectricityContractVO> page = new Page<>(param.getPage(), param.getPageSize());
        IPage<VppUserElectricityContractVO> resultPage = baseMapper.getUserElectricityContractPage(page, param);

        // 2. 如果没有数据，直接返回空结果
        if (ObjUtil.isEmpty(resultPage.getRecords())) {
            return BeanCopyUtils.convertToFxPage(resultPage, VppUserElectricityContractVO.class);
        }

        // 3. 获取用户ID列表
        List<Long> userIds = resultPage.getRecords().stream()
                .map(VppUserElectricityContractVO::getUserId)
                .collect(Collectors.toList());

        // 4. 批量查询用户对应的机组关联关系
        List<VppUserElectricityContractUnit> unitList = getUserContractUnits(userIds);

        // 5. 获取机组基础信息
        Map<Long, GeContractMaintenanceVO> contractMaintenanceMap = getContractMaintenanceMap(unitList);

        // 6. 按用户ID分组机组数据
        Map<Long, List<VppUserElectricityContractUnit>> unitsByUserId = unitList.stream()
                .collect(Collectors.groupingBy(VppUserElectricityContractUnit::getUserId));

        // 7. 填充机组信息到VO
        resultPage.getRecords().forEach(vo -> {
            List<VppUserElectricityContractUnit> userUnits = unitsByUserId.getOrDefault(vo.getUserId(), Collections.emptyList());
            vo.setUnitList(buildUnitVOList(userUnits, contractMaintenanceMap));
        });

        return BeanCopyUtils.convertToFxPage(resultPage, VppUserElectricityContractVO.class);
    }

    /**
     * 获取用户合同机组关联关系
     */
    private List<VppUserElectricityContractUnit> getUserContractUnits(List<Long> userIds) {
        if (ObjUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<VppUserElectricityContractUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(VppUserElectricityContractUnit::getUserId, userIds);
        return vppUserElectricityContractUnitMapper.selectList(wrapper);
    }

    /**
     * 获取机组基础信息映射
     */
    private Map<Long, GeContractMaintenanceVO> getContractMaintenanceMap(List<VppUserElectricityContractUnit> unitList) {
        if (ObjUtil.isEmpty(unitList)) {
            return Collections.emptyMap();
        }

        List<Long> unitIds = unitList.stream()
                .map(VppUserElectricityContractUnit::getUnitId)
                .distinct()
                .collect(Collectors.toList());

        List<GeContractMaintenanceVO> allContracts = geContractMaintenanceService.getContractByUnitIds(unitIds);
        return allContracts.stream()
                .collect(Collectors.toMap(GeContractMaintenanceVO::getUnitId, Function.identity(), (existing, replacement) -> existing));
    }

    /**
     * 构建机组VO列表
     */
    private List<VppUserElectricityContractVO.VppUserElectricityContractUnitVO> buildUnitVOList(
            List<VppUserElectricityContractUnit> userUnits, Map<Long, GeContractMaintenanceVO> unitBasicMap) {

        return userUnits.stream()
                .map(unit -> {
                    VppUserElectricityContractVO.VppUserElectricityContractUnitVO unitVO =
                            new VppUserElectricityContractVO.VppUserElectricityContractUnitVO();
                    unitVO.setUnitId(unit.getUnitId());

                    // 设置机组名称
                    GeContractMaintenanceVO unitBasic = unitBasicMap.get(unit.getUnitId());
                    if (unitBasic != null) {
                        unitVO.setUnitName(unitBasic.getUnitName());
                        unitVO.setContractPrice(unitBasic.getContractPrice());
                    }

                    return unitVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserElectricityContract(VppUserElectricityContractSaveDTO param) {
        // 1. 查询现有合同
        VppUserElectricityContract existingContract = getExistingContract(param.getUserId());

        // 2. 计算合同价格（各机组最新价格的算术平均数）
        BigDecimal contractPrice = calculateContractPrice(param.getUnitIds());

        // 3. 保存或更新合同
        saveOrUpdateContract(existingContract, param, contractPrice);

        // 4. 更新合同机组关联关系（先删除再创建）
        updateContractUnitRelations(param.getUserId(), param.getUnitIds());
    }

    /**
     * 查询现有合同
     */
    private VppUserElectricityContract getExistingContract(Long userId) {
        LambdaQueryWrapper<VppUserElectricityContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppUserElectricityContract::getUserId, userId)
                .eq(VppUserElectricityContract::getIsDelete, 0)
                .orderByDesc(VppUserElectricityContract::getCreateTime)
                .last("LIMIT 1");
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 计算合同价格（各机组最新价格的算术平均数）
     */
    private BigDecimal calculateContractPrice(List<Long> unitIds) {
        // 查询所有机组的合同价格
        List<GeContractMaintenanceVO> allContracts = geContractMaintenanceService.getContractByUnitIds(unitIds);

        if (ObjUtil.isEmpty(allContracts)) {
            return BigDecimal.ZERO;
        }

        // 按机组ID分组，获取每个机组的最新价格
        Map<Long, BigDecimal> unitLatestPriceMap = allContracts.stream()
                .collect(Collectors.groupingBy(
                        GeContractMaintenanceVO::getUnitId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy((c1, c2) -> {
                                    // 按合同标的结束时间降序，获取最新的合同
                                    if (c1.getTargetEndDate() == null && c2.getTargetEndDate() == null) {
                                        return 0;
                                    }
                                    if (c1.getTargetEndDate() == null) {
                                        return -1;
                                    }
                                    if (c2.getTargetEndDate() == null) {
                                        return 1;
                                    }
                                    return c1.getTargetEndDate().compareTo(c2.getTargetEndDate());
                                }),
                                optional -> optional.map(GeContractMaintenanceVO::getContractPrice)
                                        .orElse(BigDecimal.ZERO)
                        )
                ));

        // 计算算术平均数
        BigDecimal totalPrice = unitLatestPriceMap.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        int unitCount = unitLatestPriceMap.size();
        return unitCount > 0 ? totalPrice.divide(BigDecimal.valueOf(unitCount), 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;
    }

    /**
     * 保存或更新合同
     */
    private void saveOrUpdateContract(VppUserElectricityContract existingContract,
                                      VppUserElectricityContractSaveDTO param,
                                      BigDecimal contractPrice) {
        VppUserElectricityContract contract;

        if (existingContract != null) {
            // 更新现有合同
            contract = existingContract;
            BeanUtil.copyProperties(param, contract, "id", "createTime");
            contract.setContractPrice(contractPrice);
            baseMapper.updateById(contract);
        } else {
            // 创建新合同
            contract = new VppUserElectricityContract();
            BeanUtil.copyProperties(param, contract);
            contract.setContractPrice(contractPrice);
            baseMapper.insert(contract);
        }

    }

    /**
     * 更新合同机组关联关系（先删除再创建）
     */
    private void updateContractUnitRelations(Long userId, List<Long> unitIds) {
        // 1. 删除现有关联关系
        LambdaQueryWrapper<VppUserElectricityContractUnit> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(VppUserElectricityContractUnit::getUserId, userId);
        vppUserElectricityContractUnitMapper.delete(deleteWrapper);

        // 2. 创建新的关联关系
        if (ObjUtil.isNotEmpty(unitIds)) {
            List<VppUserElectricityContractUnit> contractUnits = unitIds.stream()
                    .map(unitId -> {
                        VppUserElectricityContractUnit contractUnit = new VppUserElectricityContractUnit();
                        contractUnit.setUserId(userId);
                        contractUnit.setUnitId(unitId);
                        return contractUnit;
                    })
                    .collect(Collectors.toList());

            // 批量插入
            contractUnits.forEach(vppUserElectricityContractUnitMapper::insert);
        }
    }

    @Override
    public List<VppUserElectricityContractExportVO> exportUserElectricityContract(VppUserElectricityContractDTO param) {
        // 1. 获取所有合同数据（不分页）
        List<VppUserElectricityContractVO> allContracts = getAllContractsForExport(param);

        if (ObjUtil.isEmpty(allContracts)) {
            return Collections.emptyList();
        }

        // 2. 获取用户ID列表
        List<Long> userIds = allContracts.stream()
                .map(VppUserElectricityContractVO::getUserId)
                .collect(Collectors.toList());

        // 3. 批量查询用户对应的机组关联关系
        List<VppUserElectricityContractUnit> unitList = getUserContractUnits(userIds);

        // 4. 获取机组基础信息
        Map<Long, GeContractMaintenanceVO> contractMaintenanceMap = getContractMaintenanceMap(unitList);

        // 5. 按用户ID分组机组数据
        Map<Long, List<VppUserElectricityContractUnit>> unitsByUserId = unitList.stream()
                .collect(Collectors.groupingBy(VppUserElectricityContractUnit::getUserId));

        // 6. 转换为导出VO
        return allContracts.stream()
                .map(contract -> buildExportVO(contract, unitsByUserId.getOrDefault(contract.getUserId(), Collections.emptyList()), contractMaintenanceMap))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有合同数据（用于导出，不分页）
     */
    private List<VppUserElectricityContractVO> getAllContractsForExport(VppUserElectricityContractDTO param) {
        return baseMapper.getUserElectricityContractList(param);
    }

    /**
     * 构建导出VO
     */
    private VppUserElectricityContractExportVO buildExportVO(VppUserElectricityContractVO contract,
                                                             List<VppUserElectricityContractUnit> userUnits,
                                                             Map<Long, GeContractMaintenanceVO> contractMaintenanceMap) {
        VppUserElectricityContractExportVO exportVO = new VppUserElectricityContractExportVO();

        // 1. 用户名称
        exportVO.setUserName(contract.getUserName());

        // 2. 合同时间（格式：2025-01 ~ 2034-12）
        exportVO.setContractTime(formatContractTime(contract.getContractStart(), contract.getContractEnd()));

        // 3. 关联机组（格式：千佛寺风电场（285.00）,百佛寺风电场（282.00）,万佛寺风电场（280.00））
        exportVO.setRelatedUnits(formatRelatedUnits(userUnits, contractMaintenanceMap));

        // 4. 合同价格
        exportVO.setContractPrice(contract.getContractPrice());

        return exportVO;
    }

    /**
     * 格式化合同时间
     */
    private String formatContractTime(java.util.Date contractStart, java.util.Date contractEnd) {
        if (contractStart == null || contractEnd == null) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(contractStart) + " ~ " + sdf.format(contractEnd);
    }

    /**
     * 格式化关联机组
     */
    private String formatRelatedUnits(List<VppUserElectricityContractUnit> userUnits,
                                      Map<Long, GeContractMaintenanceVO> contractMaintenanceMap) {
        if (ObjUtil.isEmpty(userUnits)) {
            return "";
        }

        return userUnits.stream()
                .map(unit -> {
                    GeContractMaintenanceVO unitBasic = contractMaintenanceMap.get(unit.getUnitId());
                    if (unitBasic != null && unitBasic.getUnitName() != null && unitBasic.getContractPrice() != null) {
                        return unitBasic.getUnitName() + "（" + unitBasic.getContractPrice().toString() + "）";
                    }
                    return "";
                })
                .filter(str -> !str.isEmpty())
                .collect(Collectors.joining(","));
    }

}
