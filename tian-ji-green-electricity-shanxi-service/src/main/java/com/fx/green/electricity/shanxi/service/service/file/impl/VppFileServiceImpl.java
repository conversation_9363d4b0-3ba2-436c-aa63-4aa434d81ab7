package com.fx.green.electricity.shanxi.service.service.file.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.fx.common.constant.Separator;
import com.fx.common.exception.FxServiceException;
import com.fx.common.file.config.FileProperties;
import com.fx.common.file.constant.FileConstant;
import com.fx.common.file.service.interfaces.FileService;
import com.fx.green.electricity.shanxi.api.constant.OssConstant;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.service.file.VppFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum.IMPORT_FILE_TYPE_ERROR;

/**
 * 文件上传 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class VppFileServiceImpl implements VppFileService {

    // 支持的文件类型集合（使用 Set 提高查询效率）
    private static final Set<String> ALLOWED_FILE_TYPES = new HashSet<>();

    // 支持的文件格式提示信息
    private static final String SUPPORTED_FILE_FORMATS = "pdf,doc,docx,md,txt,xlsx,xls,csv,jpg,jpeg,png,gif,bmp,webp";

    static {
        // 文档类型
        ALLOWED_FILE_TYPES.add("doc");
        ALLOWED_FILE_TYPES.add("docx");
        ALLOWED_FILE_TYPES.add("pdf");
        ALLOWED_FILE_TYPES.add("md");
        ALLOWED_FILE_TYPES.add("txt");
        ALLOWED_FILE_TYPES.add("xlsx");
        ALLOWED_FILE_TYPES.add("xls");
        ALLOWED_FILE_TYPES.add("csv");

        // 图片类型
        ALLOWED_FILE_TYPES.add("jpg");
        ALLOWED_FILE_TYPES.add("jpeg");
        ALLOWED_FILE_TYPES.add("png");
        ALLOWED_FILE_TYPES.add("gif");
        ALLOWED_FILE_TYPES.add("bmp");
        ALLOWED_FILE_TYPES.add("webp");
    }

    @Resource
    private FileProperties fileProperties;

    @Resource
    private FileService fileService;

    @Override
    public VppFileUploadVO uploadFile(MultipartFile file) {
        // 验证文件类型
        if (!isValidFile(file)) {
            throw new FxServiceException(IMPORT_FILE_TYPE_ERROR);
        }

        try {
            // 生成文件路径
            String fileName = file.getOriginalFilename();
            String fileKey = fileService.generateKey(OssConstant.SYSTEM_NAME, OssConstant.SERVICE_VPP, fileName);

            // 使用 fileService 上传文件
            String uploadedPath = fileService.uploadObject(file.getInputStream(), fileKey);

            // 构建返回对象
            return convertToVppFileUploadVO(file, uploadedPath, fileKey);
        } catch (IOException e) {
            throw new FxServiceException("文件上传失败：" + e.getMessage());
        }
    }

    @Override
    public List<VppFileUploadVO> uploadFiles(MultipartFile[] files) {
        // 验证所有文件类型
        for (MultipartFile file : files) {
            if (!isValidFile(file)) {
                throw new FxServiceException(IMPORT_FILE_TYPE_ERROR);
            }
        }

        List<VppFileUploadVO> vppFileUploadVOList = new ArrayList<>();

        try {
            for (MultipartFile file : files) {
                // 生成文件路径
                String fileName = file.getOriginalFilename();
                String fileKey = fileService.generateKey(OssConstant.SYSTEM_NAME, OssConstant.SERVICE_VPP, fileName);

                // 使用 fileService 上传文件
                String uploadedPath = fileService.uploadObject(file.getInputStream(), fileKey);

                // 构建返回对象并添加到列表
                VppFileUploadVO vppFileUploadVO = convertToVppFileUploadVO(file, uploadedPath, fileKey);
                vppFileUploadVOList.add(vppFileUploadVO);
            }

            return vppFileUploadVOList;
        } catch (IOException e) {
            throw new FxServiceException("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件类型是否合法
     *
     * @param file 待验证的文件
     * @return true: 文件类型合法, false: 文件类型不合法
     */
    private boolean isValidFile(MultipartFile file) {
        // 1. 检查文件基本信息
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            return false;
        }

        // 2. 使用 hutool 提取文件扩展名
        String extension = FileUtil.extName(fileName).toLowerCase();
        if (StrUtil.isBlank(extension)) {
            return false; // 无扩展名
        }

        // 3. 检查扩展名是否在允许列表中
        if (!ALLOWED_FILE_TYPES.contains(extension)) {
            return false; // 扩展名不在允许列表中
        }

        // 4. 使用 Apache Tika 检测真实文件类型（防止文件伪造）
        try {
            String detectedType = FileTypeUtil.detectFileType(file);
            if (StrUtil.isNotBlank(detectedType)) {
                // 如果检测到的类型与扩展名不一致，验证检测到的类型是否也在允许列表中
                if (!detectedType.equalsIgnoreCase(extension) && !ALLOWED_FILE_TYPES.contains(detectedType)) {
                    return false; // 检测到的类型不在允许列表中，可能是伪造文件
                }
            }
            // 注意：某些文本文件（如 md, txt, csv）Tika 可能无法准确检测，返回 null 是正常的
        } catch (Exception e) {
            return false; // 文件读取异常
        }

        return true;
    }

    /**
     * 将文件信息转换为 VppFileUploadVO
     *
     * @param file         原始文件
     * @param uploadedPath 上传后的路径
     * @param fileKey      文件键
     * @return VppFileUploadVO
     */
    private VppFileUploadVO convertToVppFileUploadVO(MultipartFile file, String uploadedPath, String fileKey) {
        VppFileUploadVO vppFileUploadVO = new VppFileUploadVO();
        vppFileUploadVO.setName(file.getOriginalFilename());
        vppFileUploadVO.setPath(fileKey);

        // 根据存储模式生成文件访问 URL
        String objectUrl = generateObjectUrl(fileKey);
        vppFileUploadVO.setUrl(objectUrl);

        vppFileUploadVO.setSize(file.getSize());
        vppFileUploadVO.setPace("100%"); // 上传完成后进度为 100%
        return vppFileUploadVO;
    }

    /**
     * 根据存储模式生成文件访问 URL
     *
     * @param fileKey 文件键
     * @return 文件访问 URL
     */
    private String generateObjectUrl(String fileKey) {
        if (FileConstant.OSS_MODE.equals(fileProperties.getMode())) {
            // OSS 模式：使用 OSS 端点
            return fileProperties.getOssEndpoint() + Separator.LEFT_DIVIDE + fileKey;
        } else {
            // MinIO 模式：使用 fileService 获取 URL
            return fileService.getObjectUrl(fileKey);
        }
    }
}
