package com.fx.green.electricity.shanxi.service.entity.powergeneration;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机组信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("机组信息表")
public class GeUnitBasic extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -4090623415869697097L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("发电企业id")
    private Long enterpriseId;

    @ApiModelProperty("机组名称")
    private String unitName;

    @ApiModelProperty("装机容量")
    private BigDecimal capacity;

    @ApiModelProperty("机组类型")
    private Integer type;

}
