package com.fx.green.electricity.shanxi.server.service.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;

import java.util.List;

/**
 * 合同维护数据 Service 接口
 */
public interface GeContractMaintenanceService {

    /**
     * 新增合同数据
     *
     * @param param 合同数据
     * @return 结果
     */
    DataResult<Void> insertContract(GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param 合同数据
     * @return 结果
     */
    DataResult<Void> deleteContract(IdDTO param);

    /**
     * 合同数据列表
     *
     * @param param 合同数据
     * @return 合同数据列表
     */
    DataResult<List<GeContractMaintenanceVO>> contractList(GeContractMaintenanceDTO param);

    /**
     * 合同数据列表分页
     *
     * @param param 合同数据
     * @return 合同数据列表分页
     */
    DataResult<FxPage<GeContractMaintenanceVO>> getPowerGenerationContractPage(GeContractMaintenanceDTO param);

    /**
     * 导出合同数据
     *
     * @param param 合同数据
     */
    void exportContract(GeContractMaintenanceDTO param);

}
