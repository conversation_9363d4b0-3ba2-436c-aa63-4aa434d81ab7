package com.fx.green.electricity.shanxi.server.utils;


import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 合并相同数据行策略
 *
 * 功能说明：
 * 1. 支持指定列的相同数据行合并
 * 2. 优化性能，减少不必要的遍历
 * 3. 增强数据类型处理和异常处理
 * 4. 支持缓存机制，提高合并效率
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class MergeSameRowsStrategy implements CellWriteHandler {

    /**
     * 缓存合并区域信息，提高性能
     * Key: "行索引_列索引", Value: 合并区域索引
     */
    private final Map<String, Integer> mergedRegionCache = new HashMap<>();
    /**
     * 需要合并的列索引数组
     */
    private int[] mergeColumnIndex;
    /**
     * 开始合并的行索引（通常是数据行的起始位置，排除表头）
     */
    private int mergeRowIndex;

    /**
     * 默认构造函数
     */
    public MergeSameRowsStrategy() {
    }

    /**
     * 构造函数
     *
     * @param mergeRowIndex    开始合并的行索引
     * @param mergeColumnIndex 需要合并的列索引数组
     */
    public MergeSameRowsStrategy(int mergeRowIndex, int[] mergeColumnIndex) {
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnIndex = mergeColumnIndex;

        // 参数校验
        if (mergeRowIndex < 0) {
            throw new IllegalArgumentException("合并行索引不能为负数");
        }
        if (mergeColumnIndex == null || mergeColumnIndex.length == 0) {
            throw new IllegalArgumentException("合并列索引数组不能为空");
        }
        for (int index : mergeColumnIndex) {
            if (index < 0) {
                throw new IllegalArgumentException("合并列索引不能为负数");
            }
        }
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 在单元格创建前不需要特殊处理
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在单元格创建后不需要特殊处理
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                       WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在单元格数据转换后不需要特殊处理
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        try {
            // 跳过表头行和无效数据
            if (isHead || cell == null || mergeColumnIndex == null) {
                return;
            }

            int curRowIndex = cell.getRowIndex();
            int curColIndex = cell.getColumnIndex();

            // 只处理数据行，且行索引大于起始合并行
            if (curRowIndex <= mergeRowIndex) {
                return;
            }

            // 检查当前列是否需要合并
            if (shouldMergeColumn(curColIndex)) {
                performMerge(writeSheetHolder, cell, curRowIndex, curColIndex);
            }

        } catch (Exception e) {
            log.warn("合并单元格时发生异常，行: {}, 列: {}, 错误: {}",
                    cell != null ? cell.getRowIndex() : -1,
                    cell != null ? cell.getColumnIndex() : -1,
                    e.getMessage());
        }
    }

    /**
     * 检查指定列是否需要合并
     *
     * @param columnIndex 列索引
     * @return 是否需要合并
     */
    private boolean shouldMergeColumn(int columnIndex) {
        for (int mergeIndex : mergeColumnIndex) {
            if (columnIndex == mergeIndex) {
                return true;
            }
        }
        return false;
    }

    /**
     * 执行单元格合并操作（优化版本）
     *
     * @param writeSheetHolder sheet保持对象
     * @param cell             当前单元格
     * @param curRowIndex      当前行索引
     * @param curColIndex      当前列索引
     */
    private void performMerge(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        try {
            Sheet sheet = writeSheetHolder.getSheet();
            Row prevRow = sheet.getRow(curRowIndex - 1);

            // 检查上一行是否存在
            if (prevRow == null) {
                return;
            }

            Cell prevCell = prevRow.getCell(curColIndex);
            if (prevCell == null) {
                return;
            }

            // 获取并比较单元格数据
            Object currentData = getCellValue(cell);
            Object previousData = getCellValue(prevCell);

            // 数据相同才进行合并
            if (Objects.equals(currentData, previousData)) {
                mergeWithPreviousCell(sheet, curRowIndex, curColIndex);
            }

        } catch (Exception e) {
            log.error("执行单元格合并时发生异常，行: {}, 列: {}", curRowIndex, curColIndex, e);
        }
    }

    /**
     * 获取单元格的值，支持多种数据类型
     *
     * @param cell 单元格
     * @return 单元格的值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    // 处理数值类型，避免精度问题
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    }
                    return BigDecimal.valueOf(numericValue);
                case BOOLEAN:
                    return cell.getBooleanCellValue();
                case FORMULA:
                    // 对于公式单元格，获取计算后的值
                    return cell.getStringCellValue();
                case BLANK:
                case _NONE:
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格值时发生异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 与上一行相同列的单元格进行合并（优化版本）
     *
     * @param sheet       工作表
     * @param rowIndex    当前行索引
     * @param columnIndex 列索引
     */
    private void mergeWithPreviousCell(Sheet sheet, int rowIndex, int columnIndex) {
        String cacheKey = (rowIndex - 1) + "_" + columnIndex;
        Integer existingMergeIndex = mergedRegionCache.get(cacheKey);

        if (existingMergeIndex != null && existingMergeIndex < sheet.getNumMergedRegions()) {
            // 从缓存中获取已存在的合并区域
            CellRangeAddress existingRange = sheet.getMergedRegion(existingMergeIndex);
            if (existingRange != null && existingRange.isInRange(rowIndex - 1, columnIndex)) {
                // 扩展现有合并区域
                sheet.removeMergedRegion(existingMergeIndex);
                CellRangeAddress newRange = new CellRangeAddress(
                        existingRange.getFirstRow(),
                        rowIndex,
                        columnIndex,
                        columnIndex
                );
                int newMergeIndex = sheet.addMergedRegion(newRange);

                // 更新缓存
                updateMergeCache(existingRange.getFirstRow(), rowIndex, columnIndex, newMergeIndex);
                return;
            }
        }

        // 创建新的合并区域
        CellRangeAddress newRange = new CellRangeAddress(rowIndex - 1, rowIndex, columnIndex, columnIndex);
        int newMergeIndex = sheet.addMergedRegion(newRange);

        // 更新缓存
        mergedRegionCache.put((rowIndex - 1) + "_" + columnIndex, newMergeIndex);
        mergedRegionCache.put(rowIndex + "_" + columnIndex, newMergeIndex);
    }

    /**
     * 更新合并区域缓存
     *
     * @param startRow    起始行
     * @param endRow      结束行
     * @param columnIndex 列索引
     * @param mergeIndex  合并区域索引
     */
    private void updateMergeCache(int startRow, int endRow, int columnIndex, int mergeIndex) {
        for (int row = startRow; row <= endRow; row++) {
            mergedRegionCache.put(row + "_" + columnIndex, mergeIndex);
        }
    }

    /**
     * 清理缓存（可选方法，用于释放内存）
     * 在处理完成后可以调用此方法清理缓存
     */
    public void clearCache() {
        mergedRegionCache.clear();
    }

    /**
     * 获取缓存大小（用于调试和监控）
     *
     * @return 缓存中的条目数量
     */
    public int getCacheSize() {
        return mergedRegionCache.size();
    }
}
