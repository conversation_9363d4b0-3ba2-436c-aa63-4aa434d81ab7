package com.fx.green.electricity.shanxi.server.controller.user;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import com.fx.green.electricity.shanxi.server.service.user.VppUserElectricityContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户电量合同管理 Controller
 *
 * <AUTHOR>
 **/
@Api(tags = "用户管理 - 用户电量合同管理")
@RestController
@RequestMapping("/vppUserElectricityContract")
public class VppUserElectricityContractController {

    @Resource
    private VppUserElectricityContractService vppUserElectricityContractService;

    @ApiOperation("获取用户电量合同分页")
    @PostMapping("/page")
    public DataResult<FxPage<VppUserElectricityContractVO>> getUserElectricityContractPage(@RequestBody VppUserElectricityContractDTO param) {
        return vppUserElectricityContractService.getUserElectricityContractPage(param);
    }

    @ApiOperation("修改合同")
    @PostMapping("/update")
    public DataResult<Void> updateUserElectricityContract(@RequestBody VppUserElectricityContractSaveDTO param) {
        return vppUserElectricityContractService.updateUserElectricityContract(param);
    }

    @ApiOperation("导出用户电量合同数据")
    @PostMapping("/export")
    public void exportUserElectricityContract(@RequestBody VppUserElectricityContractDTO param) {
        vppUserElectricityContractService.exportUserElectricityContract(param);
    }

}
