package com.fx.green.electricity.shanxi.server.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeUnitBasicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发电企业机组信息维护 Controller
 */
@Api(tags = "发电企业管理 - 发电企业机组信息维护")
@RestController
@RequestMapping("/geUnitBasic")
public class GeUnitBasicController {

    @Resource
    private GeUnitBasicService geUnitBasicService;

    @ApiOperation("新增机组信息")
    @PostMapping("/insertUnitBase")
    public DataResult<Void> insertUnitBase(@RequestBody GeUnitBasicDTO param) {
        return geUnitBasicService.insertUnitBase(param);
    }

    @ApiOperation("删除机组信息")
    @PostMapping("/deleteUnitBase")
    public DataResult<Void> deleteUnitBase(@RequestBody IdDTO param) {
        return geUnitBasicService.deleteUnitBase(param);
    }

    @ApiOperation("根据租户id查询对应机组")
    @PostMapping("/unitBaseList")
    public DataResult<List<GeUnitBasicVO>> unitBaseList(@RequestBody IdDTO param) {
        return geUnitBasicService.unitBaseList(param);
    }
}
