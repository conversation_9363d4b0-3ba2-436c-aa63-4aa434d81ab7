package com.fx.green.electricity.shanxi.server.service.user.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.excel.EasyExcel;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.api.user.VppUserElectricityContractApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;
import com.fx.green.electricity.shanxi.server.service.user.VppUserElectricityContractService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;


/**
 * 用户电量合同 Service 实现
 *
 * <AUTHOR>
 **/
@Service
public class VppUserElectricityContractServiceImpl implements VppUserElectricityContractService {

    @Resource
    private VppUserElectricityContractApi vppUserElectricityContractApi;

    @Override
    public DataResult<FxPage<VppUserElectricityContractVO>> getUserElectricityContractPage(VppUserElectricityContractDTO param) {
        return vppUserElectricityContractApi.getUserElectricityContractPage(param);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> updateUserElectricityContract(VppUserElectricityContractSaveDTO param) {
        return vppUserElectricityContractApi.updateUserElectricityContract(param);
    }

    @Override
    public void exportUserElectricityContract(VppUserElectricityContractDTO param) {
        DataResult<List<VppUserElectricityContractExportVO>> list = vppUserElectricityContractApi.exportUserElectricityContract(param);

        if (ObjUtil.isNotEmpty(list.getData())) {
            String fileName = "用户电量合同信息" + DateUtil.formatDate(param.getContractStart()) + "-" + DateUtil.formatDate(param.getContractEnd()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, VppUserElectricityContractExportVO.class)
                    .sheet()
                    .doWrite(list.getData());
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
    }

}
