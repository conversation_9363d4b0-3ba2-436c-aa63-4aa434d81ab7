package com.fx.green.electricity.shanxi.server.service.powergeneration.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.powergeneration.GePowerGenerationDeviceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceAssociatecConvergenceStationVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDevicePageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationDeviceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationMeterVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GePowerGenerationDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发电设备信息 Service 实现类
 */
@Service
public class GePowerGenerationDeviceServiceImpl implements GePowerGenerationDeviceService {

    @Resource
    private GePowerGenerationDeviceApi gePowerGenerationDeviceApi;

    @Override
    public DataResult<Void> createPowerGenerationDevice(GePowerGenerationDeviceSaveDTO param) {
        return gePowerGenerationDeviceApi.createPowerGenerationDevice(param);
    }

    @Override
    public DataResult<Void> deletePowerGenerationDevice(IdDTO param) {
        return gePowerGenerationDeviceApi.deletePowerGenerationDevice(param);
    }

    @Override
    public DataResult<GePowerGenerationDeviceVO> getPowerGenerationDevice(IdDTO param) {
        return gePowerGenerationDeviceApi.getPowerGenerationDevice(param);
    }

    @Override
    public DataResult<Void> updatePowerGenerationDevice(GePowerGenerationDeviceSaveDTO param) {
        return gePowerGenerationDeviceApi.updatePowerGenerationDevice(param);
    }

    @Override
    public DataResult<Void> associateUnit(GePowerGenerationDeviceAssociateUnitDTO param) {
        return gePowerGenerationDeviceApi.associateUnit(param);
    }

    @Override
    public DataResult<List<Long>> getUnitList(IdDTO param) {
        return gePowerGenerationDeviceApi.getUnitList(param);
    }

    @Override
    public DataResult<Void> createPowerGenerationMeter(GePowerGenerationMeterSaveDTO param) {
        return gePowerGenerationDeviceApi.createPowerGenerationMeter(param);
    }

    @Override
    public DataResult<FxPage<GePowerGenerationDevicePageVO>> getPowerGenerationDevicePage(GePowerGenerationDevicePageDTO param) {
        return gePowerGenerationDeviceApi.getPowerGenerationDevicePage(param);
    }

    @Override
    public DataResult<List<GePowerGenerationMeterVO>> getMeterList(IdDTO param) {
        return gePowerGenerationDeviceApi.getMeterList(param);
    }

    @Override
    public DataResult<Void> convergenceStationAssociate(GePowerGenerationDeviceAssociatecConvergenceStationDTO param) {
        return gePowerGenerationDeviceApi.convergenceStationAssociate(param);
    }

    @Override
    public DataResult<GePowerGenerationDeviceAssociatecConvergenceStationVO> getConvergenceStationAssociated(IdDTO param) {
        return gePowerGenerationDeviceApi.getConvergenceStationAssociated(param);
    }

    @Override
    public DataResult<List<GePowerGenerationDeviceVO>> getPowerGenerationDeviceList(GePowerGenerationDeviceListDTO param) {
        return gePowerGenerationDeviceApi.getPowerGenerationDeviceList(param);
    }
}
