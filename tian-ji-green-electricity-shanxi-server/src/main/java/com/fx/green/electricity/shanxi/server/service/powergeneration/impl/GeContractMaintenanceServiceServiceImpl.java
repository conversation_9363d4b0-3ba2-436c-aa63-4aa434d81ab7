package com.fx.green.electricity.shanxi.server.service.powergeneration.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.excel.EasyExcel;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.powergeneration.GeContractMaintenanceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceExportVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeContractMaintenanceService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 合同维护数据 Service 实现类
 */
@Slf4j
@Service
public class GeContractMaintenanceServiceServiceImpl implements GeContractMaintenanceService {

    @Resource
    private GeContractMaintenanceApi geContractMaintenanceApi;

    @Override
    public DataResult<Void> insertContract(GeContractMaintenanceDTO param) {
        return geContractMaintenanceApi.insertContract(param);
    }

    @Override
    public DataResult<Void> deleteContract(IdDTO param) {
        return geContractMaintenanceApi.deleteContract(param);
    }

    @Override
    public DataResult<List<GeContractMaintenanceVO>> contractList(GeContractMaintenanceDTO param) {
        return geContractMaintenanceApi.contractList(param);
    }

    @Override
    public DataResult<FxPage<GeContractMaintenanceVO>> getPowerGenerationContractPage(GeContractMaintenanceDTO param) {
        return geContractMaintenanceApi.getPowerGenerationContractPage(param);
    }

    @Override
    public void exportContract(GeContractMaintenanceDTO param) {
        DataResult<List<GeContractMaintenanceExportVO>> list = geContractMaintenanceApi.exportContract(param);

        if (ObjUtil.isNotEmpty(list.getData())) {
            String fileName = "发电合同数据" + DateUtil.formatDate(param.getTargetStartDate()) + "-" + DateUtil.formatDate(param.getTargetEndDate()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, GeContractMaintenanceExportVO.class)
                    .sheet("发电合同")
                    .doWrite(list.getData());
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
    }
}
