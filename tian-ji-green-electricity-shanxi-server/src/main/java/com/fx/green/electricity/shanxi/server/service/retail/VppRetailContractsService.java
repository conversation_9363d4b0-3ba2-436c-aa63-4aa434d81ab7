package com.fx.green.electricity.shanxi.server.service.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;

/**
 * <AUTHOR>
 **/
public interface VppRetailContractsService {

    /**
     * 获取零售列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsVO>> getRetailContractsPage(QueryVppRetailContractsDTO param);
}