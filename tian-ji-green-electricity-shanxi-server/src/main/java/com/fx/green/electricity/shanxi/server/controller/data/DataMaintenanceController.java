package com.fx.green.electricity.shanxi.server.controller.data;


import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 数据维护 Controller
 */
@Api(tags = "数据维护 - 数据维护")
@RestController
@RequestMapping("/dataMaintenance")
public class DataMaintenanceController {

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @ApiOperation("数据维护列表展示")
    @PostMapping("/queryList")
    public DataResult<List<DataMaintenanceVO>> queryList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.queryList(param);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("零售合同 - 导入")
    @PostMapping("/importRetailContract")
    public DataResult<Void> importRetailContract(@RequestPart("file") MultipartFile file,
                                                 @RequestParam("runMonth") @ApiParam("运行月份") String runMonth) {
        return dataMaintenanceService.importRetailContract(file, runMonth);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("日前申报 - 导入")
    @PostMapping("/importRecord")
    public DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file,
                                                  @RequestParam("time") @ApiParam("运行时间") String time) {
        return dataMaintenanceService.importRecord(file, time);
    }

    @ApiOperation("日前申报 - 下载")
    @PostMapping("/downloadRecord")
    public DataResult<Void> downloadRecord(@RequestBody CommonDTO.DateDTO param) {
        return dataMaintenanceService.downloadRecord(param);
    }

    @ApiOperation("日前申报 - 删除")
    @PostMapping("/deleteRecordData")
    public DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO) {
        return dataMaintenanceService.deleteRecordData(deleteRecordDTO);
    }

    @ApiOperation("获取即将失效的用户")
    @PostMapping("/getExpireUserList")
    public DataResult<List<ExpireUserVO>> getExpireUserList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.getExpireUserList(param);
    }

    @ApiOperation("获取生效的用户的数量")
    @PostMapping("/geUserCount")
    public DataResult<Integer> geUserCount(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.geUserCount(param);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("实际用电量 - 导入")
    @PostMapping("/importElectric")
    public DataResult<ImportExcelVO> importActualElectricityConsumption(@RequestPart("file") MultipartFile file) {
        return dataMaintenanceService.importActualElectricityConsumption(file);
    }

    @ApiOperation("实际用电量 - 详情")
    @PostMapping("/getElectricDetail")
    public DataResult<List<ElectricityDetailVO>> getActualElectricityConsumptionDetail(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.getActualElectricityConsumptionDetail(param);
    }

    @ApiOperation("实际用电量 - 删除")
    @PostMapping("/delElectric")
    public DataResult<Void> deleteActualElectricityConsumption(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.deleteActualElectricityConsumption(param);
    }

    @ApiOperation("导入机组实际发电量、功率预测、节点价格")
    @PostMapping("/importPowerGenerationData")
    public DataResult<ImportExcelVO> importPowerGenerationData(@RequestPart("file") MultipartFile file, @RequestParam("type") Integer type) {
        return dataMaintenanceService.importPowerGenerationData(file, type);
    }

}
