package com.fx.green.electricity.shanxi.server.service.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;

import java.util.List;

/**
 * 绿电机组信息 Service 接口
 */
public interface GeUnitBasicService {

    /**
     * 新增机组信息
     *
     * @param param 机组信息
     */
    DataResult<Void> insertUnitBase(GeUnitBasicDTO param);

    /**
     * 根据租户id查询对应机组
     *
     * @param param 机组信息
     * @return 机组信息列表
     */
    DataResult<List<GeUnitBasicVO>> unitBaseList(IdDTO param);

    /**
     * 删除机组信息
     *
     * @param param 机组信息
     */
    DataResult<Void> deleteUnitBase(IdDTO param);
}
