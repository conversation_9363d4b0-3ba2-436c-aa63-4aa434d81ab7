package com.fx.green.electricity.shanxi.server.service.elemanage;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;

import javax.servlet.http.HttpServletResponse;

/**
 *<AUTHOR>
 **/
public interface VppEleManageService {

    DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(QueryUserTreeListDTO param);

    DataResult<EleManageVO> queryEleManage(QueryEleManageDTO param);

    DataResult<Void> exportContrastData(QueryEleManageDTO param, HttpServletResponse response);

}
