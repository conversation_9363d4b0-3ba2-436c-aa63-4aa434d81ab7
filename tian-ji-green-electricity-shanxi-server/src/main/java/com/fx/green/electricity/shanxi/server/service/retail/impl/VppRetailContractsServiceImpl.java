package com.fx.green.electricity.shanxi.server.service.retail.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.api.data.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.api.user.VppLoadUserApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 零售合同管理 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppRetailContractsServiceImpl implements VppRetailContractsService {

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private VppFileApi vppFileApi;

    @Resource
    private VppLoadUserApi vppLoadUserApi;

    @Override
    public DataResult<FxPage<VppRetailContractsVO>> getRetailContractsPage(QueryVppRetailContractsDTO param) {
        return vppRetailContractsApi.getRetailContractsPage(param);
    }

}
