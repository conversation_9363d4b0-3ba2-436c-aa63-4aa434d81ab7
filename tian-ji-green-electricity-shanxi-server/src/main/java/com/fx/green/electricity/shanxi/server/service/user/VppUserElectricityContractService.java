package com.fx.green.electricity.shanxi.server.service.user;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityContractSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserElectricityContractVO;

/**
 * 用户电量合同 Service
 *
 * <AUTHOR>
 **/
public interface VppUserElectricityContractService {

    /**
     * 获取用户电量合同列表
     *
     * @param param 用户电量合同DTO
     * @return 用户电量合同列表
     */
    DataResult<FxPage<VppUserElectricityContractVO>> getUserElectricityContractPage(VppUserElectricityContractDTO param);

    /**
     * 修改用户电量合同
     *
     * @param param 用户电量合同DTO
     */
    DataResult<Void> updateUserElectricityContract(VppUserElectricityContractSaveDTO param);

    /**
     * 导出用户电量合同数据
     *
     * @param param 用户电量合同DTO
     */
    void exportUserElectricityContract(VppUserElectricityContractDTO param);
}
