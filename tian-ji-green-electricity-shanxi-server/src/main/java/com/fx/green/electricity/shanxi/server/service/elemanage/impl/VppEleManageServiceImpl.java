package com.fx.green.electricity.shanxi.server.service.elemanage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.elemanage.VppEleManageApi;
import com.fx.green.electricity.shanxi.api.enums.VppTimeSharingEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.server.service.elemanage.VppEleManageService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import com.fx.green.electricity.shanxi.server.utils.MergeSameRowsStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VppEleManageServiceImpl implements VppEleManageService {

    @Resource
    private VppEleManageApi vppEleManageApi;

    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryThreeFloorsTreeList(QueryUserTreeListDTO param) {
        return vppEleManageApi.queryThreeFloorsTreeList(param);
    }

    @Override
    public DataResult<EleManageVO> queryEleManage(QueryEleManageDTO param) {
        return vppEleManageApi.queryEleManage(param);
    }

    @Override
    public DataResult<Void> exportContrastData(QueryEleManageDTO param, HttpServletResponse response) {
        DataResult<EleManageVO> dataResult = vppEleManageApi.queryEleManage(param);
        if (ObjectUtil.isNotNull(dataResult.getData())) {
            EleManageVO data = dataResult.getData();
            List<List<String>> header = headers(data,param);
            List<List<String>> formatTableDataList = this.formatTableDataLists(data);
            // 查询数据并处理格式
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            // easyExcel导出
            EasyExcelFactory.write(stream)
                    .head(header)
                    .registerWriteHandler(new MergeSameRowsStrategy(1, new int[]{0}))
                    .sheet("sheet")
                    .doWrite(formatTableDataList);
            byte[] fileBytes = stream.toByteArray();
            String fileName = DateUtil.formatDate(param.getStartDate()) + "至" + DateUtil.formatDate(param.getEndDate()) + "用户用电统计";
            DownloadUtil.downloadJsonType(fileName + ".xlsx", fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }

    private List<List<String>> headers(EleManageVO data,QueryEleManageDTO param) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("日期"));
        headers.add(Collections.singletonList("用户名称"));
        headers.add(Collections.singletonList("户号"));
        headers.add(Collections.singletonList("数据类型"));
        headers.add(Collections.singletonList("总电量"));
        if(VppTimeSharingEnum.isGroupType15(param.getTimeSharing())){
            List<String> list = data.getTimeFrame();
            if (ObjectUtil.isNotEmpty(list)) {
                for (String s : list) {
                    headers.add(Collections.singletonList(s));
                }
            }
        }
        return headers;
    }

    private List<List<String>> formatTableDataLists(EleManageVO data) {
        List<List<String>> listList = new ArrayList<>();
        if (CollUtil.isEmpty(data.getDataList())) {
            return new ArrayList<>();
        }
        // 数据格式转换
        dataConversion(data.getDataList(), listList);
        return listList;
    }

    private void dataConversion(List<EleManageVO.PredictedElectricityInfo> dataList, List<List<String>> listList) {
        for (EleManageVO.PredictedElectricityInfo tableVO : dataList) {
            //日期
            Date dateDay = tableVO.getDateDay();
            String dateDayStr = DateUtil.formatDate(dateDay);
            String name = tableVO.getName();
            String registered = tableVO.getRegistered();
            //实际用电量
            List<BigDecimal> actualElectricityList = tableVO.getActualElectricityList();
            //关口表电量
            List<BigDecimal> dataGatherElectricityList = tableVO.getDataGatherElectricityList();
            listList.add(toStringList(ObjectUtil.isNotNull(actualElectricityList) ? actualElectricityList : new ArrayList<>()
                    , "实际用电量",registered,name,dateDayStr));
            listList.add(toStringList(ObjectUtil.isNotNull(dataGatherElectricityList) ? dataGatherElectricityList : new ArrayList<>()
                    , "关口表电量",registered,name,dateDayStr));
        }
    }

    private List<String> toStringList(List<BigDecimal> bigDecimalList, String... params) {
        List<String> strings = new ArrayList<>();
        for (BigDecimal bigDecimal : bigDecimalList) {
            strings.add(ObjectUtil.isNull(bigDecimal) ? "-" : bigDecimal.toPlainString());
        }
        for (String param : params) {
            strings.add(0, param);
        }
        return strings;
    }

    private List<String> toStringLists(List<BigDecimal> bigDecimalList, List<BigDecimal> ratioList, String... params) {
        List<String> strings = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(bigDecimalList)) {
            for (int i = 0; i < bigDecimalList.size(); i++) {
                BigDecimal electricity = bigDecimalList.get(i);
                BigDecimal ratio = null;
                if (ObjectUtil.isNotEmpty(ratioList)) {
                    ratio = ratioList.get(i);
                }
                if (ObjectUtil.isNotNull(electricity)) {
                    String electricityPlainString = electricity.toPlainString();
                    if (ObjectUtil.isNotNull(ratio)) {
                        String ratioPlainString = ratio.toPlainString();
                        strings.add(electricityPlainString + "(" + ratioPlainString + "%)");
                    } else {
                        strings.add(electricityPlainString);
                    }
                } else {
                    strings.add("-");
                }
            }
        }

        for (String param : params) {
            strings.add(0, param);
        }

        return strings;
    }

}
