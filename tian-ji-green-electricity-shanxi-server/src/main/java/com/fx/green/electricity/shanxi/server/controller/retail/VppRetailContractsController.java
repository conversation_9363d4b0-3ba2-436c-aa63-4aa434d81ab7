package com.fx.green.electricity.shanxi.server.controller.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 零售合同 Controller
 * <AUTHOR>
 **/
@Api(tags = "零售合同管理")
@RestController
@RequestMapping("/vppRetailContractsController")
public class VppRetailContractsController {

    @Resource
    private VppRetailContractsService vppRetailContractsService;

    @ApiOperation("获取零售分页")
    @PostMapping("/page")
    public DataResult<FxPage<VppRetailContractsVO>> getRetailContractsPage(@RequestBody QueryVppRetailContractsDTO param) {
        return vppRetailContractsService.getRetailContractsPage(param);
    }

}
