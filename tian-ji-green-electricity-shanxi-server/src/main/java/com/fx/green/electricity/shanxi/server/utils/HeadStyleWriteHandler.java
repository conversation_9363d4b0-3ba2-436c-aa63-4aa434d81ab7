package com.fx.green.electricity.shanxi.server.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.concurrent.ArrayBlockingQueue;


/**
 * 自定义样式拦截器-复杂表头样式的使用
 *
 * @Author: nxf
 * @Date: 2021/1/17 14:31
 */
public class HeadStyleWriteHandler extends AbstractCellStyleStrategy {


    /**
     * 复杂表头自定义样式队列，先进先出，方便存储
     */
    private ArrayBlockingQueue<ComplexHeadStyles> headStylesQueue;
    /**
     * WorkBoot
     */
    private Workbook workbook;

    /**
     * 构造方法，创建对象时传入需要定制的表头信息队列
     */
    public HeadStyleWriteHandler(ArrayBlockingQueue<ComplexHeadStyles> headStylesQueue) {
        this.headStylesQueue = headStylesQueue;
    }

    @Override
    protected void setHeadCellStyle(Cell cell, Head head, Integer relativeRowIndex) {

        WriteCellStyle writeCellStyle = new WriteCellStyle();

        if (headStylesQueue != null && !headStylesQueue.isEmpty()) {

            ComplexHeadStyles complexHeadStyle = headStylesQueue.peek();
            // 取出队列中的自定义表头信息，与当前坐标比较，判断是否相符
            if (cell.getColumnIndex() == complexHeadStyle.getY() && relativeRowIndex.equals(complexHeadStyle.getX())) {
                // 设置自定义的表头样式
                writeCellStyle.setFillForegroundColor(complexHeadStyle.getIndexColor());
                // 样式出队
                headStylesQueue.poll();
            }
        }

        // 创建CellStyle并应用WriteCellStyle的属性
        CellStyle headCellStyle = workbook.createCellStyle();
        if (writeCellStyle.getFillForegroundColor() != null) {
            headCellStyle.setFillForegroundColor(writeCellStyle.getFillForegroundColor());
            headCellStyle.setFillPattern(org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND);
        }
        // 设置表头样式
        cell.setCellStyle(headCellStyle);

    }

    @Override
    protected void setContentCellStyle(Cell cell, Head head, Integer relativeRowIndex) {

    }

}