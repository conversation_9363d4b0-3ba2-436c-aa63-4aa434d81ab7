package com.fx.green.electricity.shanxi.server.controller.elemanage;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryEleManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserTreeListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.elemanage.EleManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.server.service.elemanage.VppEleManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户用电管理
 *<AUTHOR>
 **/
@RestController
@Api(tags = "用户用电管理")
@RequestMapping("vppEleManage")
public class VppEleManageController {

    @Resource
    private VppEleManageService vppEleManageService;

    @ApiOperation("获取虚拟电厂用户户号三层树状列表")
    @PostMapping("queryThreeFloorsTreeList")
    public DataResult<VppLoadUserVO.TreeVO> queryUserTreeList(@RequestBody QueryUserTreeListDTO param) {
        return vppEleManageService.queryThreeFloorsTreeList(param);
    }

    @ApiOperation("获取图表和表格数据")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("queryEleManage")
    public DataResult<EleManageVO> queryEleManage(@RequestBody QueryEleManageDTO param) {
        return vppEleManageService.queryEleManage(param);
    }

    @ApiOperation("导出对比数据")
    @PostMapping("exportContrastData")
    public DataResult<Void> exportContrastData(HttpServletResponse response, @RequestBody QueryEleManageDTO param) {
        return vppEleManageService.exportContrastData(param, response);
    }


}
