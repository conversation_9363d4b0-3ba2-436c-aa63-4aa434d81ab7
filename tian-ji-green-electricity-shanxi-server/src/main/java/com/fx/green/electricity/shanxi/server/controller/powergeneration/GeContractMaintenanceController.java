package com.fx.green.electricity.shanxi.server.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeContractMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绿电直连合同维护 Controller
 */
@Slf4j
@RestController
@Api(tags = "发电管理 - 绿电直连合同维护")
@RequestMapping("/geContractMaintenance")
public class GeContractMaintenanceController {

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @ApiOperation("新增合同数据")
    @PostMapping("/insertContract")
    public DataResult<Void> insertContract(@RequestBody GeContractMaintenanceDTO param) {
        return geContractMaintenanceService.insertContract(param);
    }

    @ApiOperation("删除合同数据")
    @PostMapping("/deleteContract")
    public DataResult<Void> deleteContract(@RequestBody IdDTO param) {
        return geContractMaintenanceService.deleteContract(param);
    }

    @ApiOperation("合同数据列表")
    @PostMapping("/contractList")
    public DataResult<List<GeContractMaintenanceVO>> contractList(@RequestBody GeContractMaintenanceDTO param) {
        return geContractMaintenanceService.contractList(param);
    }

    @ApiOperation("获取发电合同列表分页")
    @PostMapping("/page")
    public DataResult<FxPage<GeContractMaintenanceVO>> getPowerGenerationContractPage(@RequestBody GeContractMaintenanceDTO param) {
        return geContractMaintenanceService.getPowerGenerationContractPage(param);
    }

    @ApiOperation("导出合同数据")
    @PostMapping("/export")
    public void exportContract(@RequestBody GeContractMaintenanceDTO param) {
        geContractMaintenanceService.exportContract(param);
    }

}
