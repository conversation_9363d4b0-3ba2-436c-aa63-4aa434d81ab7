package com.fx.green.electricity.shanxi.server.controller.powergeneration;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GePowerGenerationEnterpriseService;
import com.fx.operation.api.vo.TenantVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发电企业信息 Controller
 */
@Api(tags = "发电企业管理 - 发电企业信息")
@RestController
@RequestMapping("/gePowerGenerationEnterprise")
public class GePowerGenerationEnterpriseController {

    @Resource
    private GePowerGenerationEnterpriseService gePowerGenerationEnterpriseService;

    @CommonNoRepeat
    @ApiOperation("新增发电企业信息")
    @PostMapping("/create")
    public DataResult<Void> createPowerGenerationEnterprise(@RequestBody GePowerGenerationEnterpriseDTO param) {
        return gePowerGenerationEnterpriseService.createPowerGenerationEnterprise(param);
    }

    @ApiOperation("删除发电企业信息")
    @PostMapping("/delete")
    public DataResult<Void> deletePowerGenerationEnterprise(@RequestBody IdDTO param) {
        return gePowerGenerationEnterpriseService.deletePowerGenerationEnterprise(param);
    }

    @ApiOperation("获得发电企业信息分页")
    @PostMapping("/page")
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterprisePage(@RequestBody GePowerGenerationEnterpriseDTO param) {
        return gePowerGenerationEnterpriseService.getPowerGenerationEnterprisePage(param);
    }

    @ApiOperation("获取所有发电企业和机组对应信息")
    @PostMapping("/list-all")
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterpriseList() {
        return gePowerGenerationEnterpriseService.getPowerGenerationEnterpriseList();
    }

    @ApiOperation("获取运营平台绿电直连租户")
    @PostMapping("get-operation-green-tenant")
    public DataResult<List<TenantVO>> getOperationGreenTenant() {
        return gePowerGenerationEnterpriseService.getOperationGreenTenant();
    }

    @ApiOperation("获取企业机组树")
    @PostMapping("/unit-tree")
    public DataResult<List<GePowerGenerationEnterpriseUnitTreeVO>> getEnterpriseUnitTree(@RequestBody GePowerGenerationEnterpriseUnitTreeDTO param) {
        return gePowerGenerationEnterpriseService.getEnterpriseUnitTree(param);
    }

}
