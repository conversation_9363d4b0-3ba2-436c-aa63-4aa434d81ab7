package com.fx.green.electricity.shanxi.server.service.powergeneration.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.powergeneration.GePowerGenerationEnterpriseApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeUnitDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 绿电设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class GeUnitDeviceServiceImpl implements GeUnitDeviceService {

    @Resource
    private GePowerGenerationEnterpriseApi gePowerGenerationEnterpriseApi;

    @Override
    public DataResult<Void> createUnitDevice(GeUnitDeviceSaveDTO param) {
        return gePowerGenerationEnterpriseApi.insertDevice(param);
    }

    @Override
    public DataResult<Void> deleteUnitDevice(IdDTO param) {
        return gePowerGenerationEnterpriseApi.deleteDevice(param);
    }
}
