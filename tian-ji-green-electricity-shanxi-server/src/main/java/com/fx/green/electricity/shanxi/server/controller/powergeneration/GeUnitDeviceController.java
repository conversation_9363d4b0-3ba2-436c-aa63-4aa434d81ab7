package com.fx.green.electricity.shanxi.server.controller.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeUnitDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 机组设备信息 Controller
 */
@Api(tags = "发电管理- 机组设备信息")
@RestController
@RequestMapping("/geUnitDevice")
public class GeUnitDeviceController {

    @Resource
    private GeUnitDeviceService geUnitDeviceService;

    @ApiOperation("新增机组设备信息")
    @PostMapping("/create")
    public DataResult<Void> createUnitDevice(@RequestBody GeUnitDeviceSaveDTO param) {
        return geUnitDeviceService.createUnitDevice(param);
    }

    @ApiOperation("删除机组设备")
    @PostMapping("/delete")
    public DataResult<Void> deleteUnitDevice(@RequestBody IdDTO param) {
        return geUnitDeviceService.deleteUnitDevice(param);
    }
}
