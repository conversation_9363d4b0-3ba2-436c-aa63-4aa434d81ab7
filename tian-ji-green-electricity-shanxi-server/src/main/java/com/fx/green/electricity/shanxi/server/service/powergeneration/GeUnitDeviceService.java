package com.fx.green.electricity.shanxi.server.service.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitDeviceSaveDTO;

/**
 * 绿电设备信息 Service 接口
 */
public interface GeUnitDeviceService {

    /**
     * 新增设备信息
     *
     * @param param 设备信息
     */
    DataResult<Void> createUnitDevice(GeUnitDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param 设备信息
     */
    DataResult<Void> deleteUnitDevice(IdDTO param);
}
