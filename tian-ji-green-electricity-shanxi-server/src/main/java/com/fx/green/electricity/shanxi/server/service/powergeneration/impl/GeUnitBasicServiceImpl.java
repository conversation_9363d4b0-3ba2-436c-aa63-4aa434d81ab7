package com.fx.green.electricity.shanxi.server.service.powergeneration.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.powergeneration.GePowerGenerationEnterpriseApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GeUnitBasicService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绿电机组信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class GeUnitBasicServiceImpl implements GeUnitBasicService {

    @Resource
    private GePowerGenerationEnterpriseApi gePowerGenerationEnterpriseApi;

    @Override
    public DataResult<Void> insertUnitBase(GeUnitBasicDTO param) {
        return gePowerGenerationEnterpriseApi.insertUnitBase(param);
    }

    @Override
    public DataResult<List<GeUnitBasicVO>> unitBaseList(IdDTO param) {
        return gePowerGenerationEnterpriseApi.unitBaseList(param);
    }

    public DataResult<Void> deleteUnitBase(IdDTO param) {
        return gePowerGenerationEnterpriseApi.deleteUnitBase(param);
    }
}
