package com.fx.green.electricity.shanxi.server.service.data.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.data.GeMaintenanceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.server.service.data.GeFileRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 文件导入记录 Service 实现类
 */
@Service
public class GeFileRecordServiceImpl implements GeFileRecordService {

    @Resource
    private GeMaintenanceApi geMaintenanceApi;

    @Override
    public DataResult<List<GeDayRecordVO>> getFileRecordByDay(GeFileRecordDTO param) {
        return geMaintenanceApi.getFileRecordByDay(param);
    }

    @Override
    public DataResult<List<GeFileRecordVO>> fileStatus(GeFileRecordDTO param) {
        return geMaintenanceApi.fileStatus(param);
    }

    @Override
    public DataResult<Map<String, String>> getInfoDataValue(GeFileRecordDTO param) {
        return geMaintenanceApi.getInfoDataValue(param);
    }
}
