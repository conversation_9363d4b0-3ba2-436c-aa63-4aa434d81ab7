package com.fx.green.electricity.shanxi.server.service.data.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.api.data.DataMaintenanceApi;
import com.fx.green.electricity.shanxi.api.api.data.GeMaintenanceApi;
import com.fx.green.electricity.shanxi.api.api.data.VppElectricQuantityApi;
import com.fx.green.electricity.shanxi.api.api.data.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 数据维护 Service 实现类
 */
@Service
@Slf4j
public class DataMaintenanceServiceImpl implements DataMaintenanceService {

    @Resource
    private DataMaintenanceApi dataMaintenanceApi;

    @Resource
    private VppElectricQuantityApi vppElectricQuantityApi;

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private GeMaintenanceApi geMaintenanceApi;


    @Override
    public DataResult<List<DataMaintenanceVO>> queryList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.queryList(param);
    }

    @Override
    public DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.getExpireUserList(param);
    }

    @Override
    public DataResult<Integer> geUserCount(DataMaintenanceDTO param) {
        return dataMaintenanceApi.geUserCount(param);
    }

    @Override
    public DataResult<Void> importRetailContract(MultipartFile file, String runMonth) {
        return vppRetailContractsApi.importRetailContract(file, runMonth);
    }

    @Override
    public DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param) {
        return dataMaintenanceApi.updateStatus(param);
    }

    @Override
    public DataResult<ImportExcelVO> importActualElectricityConsumption(MultipartFile file) {
        return vppElectricQuantityApi.importActualElectricityConsumption(file);
    }

    @Override
    public DataResult<List<ElectricityDetailVO>> getActualElectricityConsumptionDetail(DataMaintenanceQueryDTO param) {
        return vppElectricQuantityApi.getActualElectricityConsumptionDetail(param);
    }

    @Override
    public DataResult<Void> deleteActualElectricityConsumption(DataMaintenanceQueryDTO param) {
        return vppElectricQuantityApi.deleteActualElectricityConsumption(param);
    }

    @Override
    public DataResult<ImportExcelVO> importRecord(MultipartFile file, String time) {
        return vppElectricQuantityApi.importRecord(file, time);
    }

    @Override
    public DataResult<Void> downloadRecord(CommonDTO.DateDTO param) {
        DataResult<List<SeElectricDeclareVO>> listDataResult = vppElectricQuantityApi.downloadRecord(param);
        if (ObjectUtil.isNotNull(listDataResult.getData())) {
            List<SeElectricDeclareVO> data = listDataResult.getData();
            String fileName = "日前申报数据" + DateUtil.formatDate(param.getQueryDate()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, SeElectricDeclareVO.class)
                    .sheet()
                    .doWrite(data);
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        return vppElectricQuantityApi.deleteRecordData(deleteRecordDTO);
    }

    @Override
    public DataResult<ImportExcelVO> importPowerGenerationData(MultipartFile file, Integer type) {
        return geMaintenanceApi.importPowerGenerationData(file, type);
    }
}
