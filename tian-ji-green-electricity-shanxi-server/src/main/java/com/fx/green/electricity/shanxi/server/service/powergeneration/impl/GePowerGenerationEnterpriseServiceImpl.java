package com.fx.green.electricity.shanxi.server.service.powergeneration.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.powergeneration.GePowerGenerationEnterpriseApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.green.electricity.shanxi.server.service.powergeneration.GePowerGenerationEnterpriseService;
import com.fx.operation.api.api.OperationTenantApi;
import com.fx.operation.api.vo.TenantVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发电企业信息 Service 实现类
 */
@Service
public class GePowerGenerationEnterpriseServiceImpl implements GePowerGenerationEnterpriseService {

    @Resource
    private GePowerGenerationEnterpriseApi gePowerGenerationEnterpriseApi;

    @Resource
    private OperationTenantApi operationTenantApi;

    @Override
    public DataResult<Void> createPowerGenerationEnterprise(GePowerGenerationEnterpriseDTO param) {
        return gePowerGenerationEnterpriseApi.createPowerGenerationEnterprise(param);
    }

    @Override
    public DataResult<Void> deletePowerGenerationEnterprise(IdDTO param) {
        return gePowerGenerationEnterpriseApi.deletePowerGenerationEnterprise(param);
    }

    @Override
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterprisePage(GePowerGenerationEnterpriseDTO param) {
        return gePowerGenerationEnterpriseApi.getPowerGenerationEnterprisePage(param);
    }

    @Override
    public DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterpriseList() {
        return gePowerGenerationEnterpriseApi.getPowerGenerationEnterpriseList();
    }

    @Override
    public DataResult<List<TenantVO>> getOperationGreenTenant() {
        return operationTenantApi.getGreenLinkTenant();
    }

    @Override
    public DataResult<List<GePowerGenerationEnterpriseUnitTreeVO>> getEnterpriseUnitTree(GePowerGenerationEnterpriseUnitTreeDTO param) {
        return gePowerGenerationEnterpriseApi.getEnterpriseUnitTree(param);
    }
}
