package com.fx.green.electricity.shanxi.server.service.powergeneration;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.powergeneration.GePowerGenerationEnterpriseUnitTreeDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseUnitTreeVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.powergeneration.GePowerGenerationEnterpriseVO;
import com.fx.operation.api.vo.TenantVO;

import java.util.List;

/**
 * 发电企业信息 Service 接口
 */
public interface GePowerGenerationEnterpriseService {

    /**
     * 新增发电企业信息
     *
     * @param param 发电企业信息
     */
    DataResult<Void> createPowerGenerationEnterprise(GePowerGenerationEnterpriseDTO param);

    /**
     * 删除发电企业信息
     *
     * @param param 发电企业信息
     */
    DataResult<Void> deletePowerGenerationEnterprise(IdDTO param);

    /**
     * 发电企业信息分页
     *
     * @param param 发电企业信息
     * @return 发电企业信息
     */
    DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterprisePage(GePowerGenerationEnterpriseDTO param);

    /**
     * 获取所有发电企业信息和机组对应信息
     *
     * @return 发电企业信息和机组对应信息
     */
    DataResult<List<GePowerGenerationEnterpriseVO>> getPowerGenerationEnterpriseList();

    /**
     * 获取运营平台绿电直连租户
     *
     * @return 运营平台绿电直连租户
     */
    DataResult<List<TenantVO>> getOperationGreenTenant();

    /**
     * 获取企业机组树
     *
     * @param param 企业机组树DTO
     * @return 企业机组树列表
     */
    DataResult<List<GePowerGenerationEnterpriseUnitTreeVO>> getEnterpriseUnitTree(GePowerGenerationEnterpriseUnitTreeDTO param);
}
