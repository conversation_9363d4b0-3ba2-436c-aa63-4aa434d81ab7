<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fx</groupId>
        <artifactId>tian-ji-parent</artifactId>
        <version>2.0.0.RELEASE</version>
        <relativePath/>
    </parent>

    <artifactId>tian-ji-green-electricity-shanxi</artifactId>
    <version>1.0.0.RELEASE</version>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <description>山西绿电直连平台</description>

    <modules>
        <module>tian-ji-green-electricity-shanxi-api</module>
        <module>tian-ji-green-electricity-shanxi-service</module>
        <module>tian-ji-green-electricity-shanxi-server</module>
    </modules>

    <properties>
        <!-- 天记云内部调用 -->
        <tian-ji-green-electricity-shanxi.version>${version}</tian-ji-green-electricity-shanxi.version>
        <tian-ji-operation.version>2.7.0.RELEASE</tian-ji-operation.version>
        <tian-ji-common.version>2.0.0.RELEASE</tian-ji-common.version>
        <tian-ji-auth-sdk.version>1.2.0.RELEASE</tian-ji-auth-sdk.version>
        <tian-ji-data-gather.version>2.5.0.RELEASE</tian-ji-data-gather.version>

        <tian-ji-mqtt.version>1.1.0.RELEASE</tian-ji-mqtt.version>
        <tian-ji-influx-db.version>1.2.0.RELEASE</tian-ji-influx-db.version>
        <tian-ji-websocket.version>1.1.0.RELEASE</tian-ji-websocket.version>

        <tian-ji-common-subject-task.version>1.0.1.RELEASE</tian-ji-common-subject-task.version>
        <tian-ji-common-file.version>1.0.0.RELEASE</tian-ji-common-file.version>
        <tian-ji-common-redis.version>1.0.0.RELEASE</tian-ji-common-redis.version>
        <tian-ji-auxiliary-gather.version>1.1.1.RELEASE</tian-ji-auxiliary-gather.version>
        <tian-ji-public-data-shanxi-api.version>2.7.0</tian-ji-public-data-shanxi-api.version>

        <!-- 其他 -->
        <weixin.version>4.3.0</weixin.version>
        <de.codecentric.version>2.6.2</de.codecentric.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <commons-io.version>2.15.1</commons-io.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-operation-api</artifactId>
                <version>${tian-ji-operation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-common-core</artifactId>
                <version>${tian-ji-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-green-electricity-shanxi-api</artifactId>
                <version>${tian-ji-green-electricity-shanxi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-auth-sdk</artifactId>
                <version>${tian-ji-auth-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-data-gather-api</artifactId>
                <version>${tian-ji-data-gather.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-mqtt-api</artifactId>
                <version>${tian-ji-mqtt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-influx-db-api</artifactId>
                <version>${tian-ji-influx-db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-websocket-api</artifactId>
                <version>${tian-ji-websocket.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-common-subject-task</artifactId>
                <version>${tian-ji-common-subject-task.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-common-file</artifactId>
                <version>${tian-ji-common-file.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-common-redis</artifactId>
                <version>${tian-ji-common-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-auxiliary-gather-api</artifactId>
                <version>${tian-ji-auxiliary-gather.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>tian-ji-public-data-shanxi-api</artifactId>
                <version>${tian-ji-public-data-shanxi-api.version}</version>
                <classifier>jdk1.8</classifier>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${de.codecentric.version}</version>
            </dependency>

            <!-- EasyExcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- Apache Commons IO - 确保版本兼容性 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.4.0.905</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>releases</name>
                    <url>http://192.168.9.183:9081/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshots</id>
                    <name>snapshots</name>
                    <url>http://192.168.9.183:9081/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <repositories>
                <repository>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <id>public</id>
                    <name>Public Repositories</name>
                    <url>http://192.168.9.183:9081/repository/maven-public/</url>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>prod</id>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>releases</name>
                    <url>http://10.1.4.12:8081/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshots</id>
                    <name>snapshots</name>
                    <url>http://10.1.4.12:8081/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <repositories>
                <repository>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <id>public</id>
                    <name>Public Repositories</name>
                    <url>http://10.1.4.12:8081/repository/maven-public/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>

</project>